# Database Documentation Hub

Bu repository, database teknolojilerinin pratik kullanımı, implementation detayları ve database-specific konuları kapsamlı bir şekilde dokumente etmektedir. SQL'den NoSQL'e, optimization'dan monitoring'e kadar tüm database konularını içerir.

> **📋 Not:** Teorik system design konuları (CAP teoremi, ACID, distributed systems patterns) için [system-design](../system-design) repository'sine bakınız.

## 📚 İçindekiler

### 🗃️ Database Türleri ve Karşılaştırma
- [Database Türleri Karşılaştırması](./database-types/README.md)
  - [İlişkisel vs NoSQL Karşılaştırması](./database-types/relational-vs-nosql.md)
  - [Database Seçim Kriterleri](./database-types/selection-criteria.md)
  - [Use Case Bazlı Öneriler](./database-types/use-case-recommendations.md)
  - [Performance Karşılaştırmaları](./database-types/performance-comparison.md)
  - [Cost Analysis](./database-types/cost-analysis.md)

### 📊 SQL ve Query Optimization
- [SQL Fundamentals](./sql/README.md)
  - [Temel SQL Komutları](./sql/basic-commands.md)
  - [İleri SQL Teknikleri](./sql/advanced-techniques.md)
  - [Window Functions](./sql/window-functions.md)
  - [Common Table Expressions (CTE)](./sql/cte.md)
  - [Stored Procedures ve Functions](./sql/stored-procedures.md)
  - [Triggers](./sql/triggers.md)
  - [Recursive Queries](./sql/recursive-queries.md)
  - [JSON Operations](./sql/json-operations.md)
- [Query Optimization](./optimization/README.md)
  - [Index Stratejileri](./optimization/indexing-strategies.md)
  - [Query Execution Plans](./optimization/execution-plans.md)
  - [Performance Tuning](./optimization/performance-tuning.md)
  - [Query Profiling](./optimization/query-profiling.md)
  - [Statistics ve Cardinality](./optimization/statistics-cardinality.md)
  - [Hint Usage](./optimization/hints.md)

### 🏗️ Database Design ve Schema Management
- [Database Design](./design/README.md)
  - [Normalization Techniques](./design/normalization.md)
  - [Denormalization Strategies](./design/denormalization.md)
  - [Schema Design Patterns](./design/schema-patterns.md)
  - [Data Modeling Best Practices](./design/data-modeling.md)
  - [Entity Relationship Diagrams](./design/erd.md)
  - [Constraint Design](./design/constraints.md)
- [Schema Management](./schema/README.md)
  - [Database Migrations](./schema/migrations.md)
  - [Version Control for Schemas](./schema/version-control.md)
  - [Schema Evolution Strategies](./schema/evolution-strategies.md)
  - [Backward Compatibility](./schema/backward-compatibility.md)

### 🔒 Transaction Management ve Isolation
- [Transaction Management](./transactions/README.md)
  - [Isolation Seviyeleri (Database-Specific)](./transactions/isolation-levels.md)
  - [Locking Strategies](./transactions/locking-strategies.md)
  - [Deadlock Detection ve Prevention](./transactions/deadlock-management.md)
  - [Transaction Log Management](./transactions/log-management.md)
  - [Long-Running Transactions](./transactions/long-running.md)

### ⚡ Performance ve Optimization
- [Performance Tuning](./performance/README.md)
  - [Connection Pooling](./performance/connection-pooling.md)
  - [Query Caching](./performance/query-caching.md)
  - [Buffer Pool Optimization](./performance/buffer-pool.md)
  - [I/O Optimization](./performance/io-optimization.md)
  - [Memory Management](./performance/memory-management.md)
- [Indexing Strategies](./indexing/README.md)
  - [B-Tree Indexes](./indexing/btree-indexes.md)
  - [Hash Indexes](./indexing/hash-indexes.md)
  - [Bitmap Indexes](./indexing/bitmap-indexes.md)
  - [Partial Indexes](./indexing/partial-indexes.md)
  - [Composite Indexes](./indexing/composite-indexes.md)
  - [Index Maintenance](./indexing/maintenance.md)

### 🔐 Security ve Compliance
- [Database Security](./security/README.md)
  - [Authentication Methods](./security/authentication.md)
  - [Authorization ve Role Management](./security/authorization.md)
  - [Encryption Implementation](./security/encryption.md)
  - [SQL Injection Prevention](./security/sql-injection.md)
  - [Audit Logging](./security/audit-logging.md)
  - [Data Masking Techniques](./security/data-masking.md)
  - [Row-Level Security](./security/row-level-security.md)

### 📈 Monitoring ve Maintenance
- [Database Monitoring](./monitoring/README.md)
  - [Performance Metrics](./monitoring/performance-metrics.md)
  - [Query Performance Monitoring](./monitoring/query-monitoring.md)
  - [Resource Usage Monitoring](./monitoring/resource-monitoring.md)
  - [Alerting Setup](./monitoring/alerting.md)
  - [Log Analysis](./monitoring/log-analysis.md)
- [Database Maintenance](./maintenance/README.md)
  - [Backup Strategies](./maintenance/backup-strategies.md)
  - [Recovery Procedures](./maintenance/recovery-procedures.md)
  - [Index Maintenance](./maintenance/index-maintenance.md)
  - [Statistics Updates](./maintenance/statistics-updates.md)
  - [Cleanup Procedures](./maintenance/cleanup.md)

## 🗄️ Database-Specific Implementation Guides

### İlişkisel Veritabanları (RDBMS)

#### PostgreSQL
- [PostgreSQL Complete Guide](./databases/postgresql/README.md)
  - [Installation ve Configuration](./databases/postgresql/installation-config.md)
  - [PostgreSQL-Specific SQL Features](./databases/postgresql/sql-features.md)
  - [Advanced Data Types](./databases/postgresql/data-types.md)
  - [Extensions (PostGIS, pg_stat_statements, etc.)](./databases/postgresql/extensions.md)
  - [VACUUM ve ANALYZE](./databases/postgresql/vacuum-analyze.md)
  - [Connection Pooling (PgBouncer)](./databases/postgresql/connection-pooling.md)
  - [Replication Setup](./databases/postgresql/replication.md)
  - [Partitioning](./databases/postgresql/partitioning.md)
  - [Performance Tuning](./databases/postgresql/performance-tuning.md)
  - [Backup ve Point-in-Time Recovery](./databases/postgresql/backup-recovery.md)
  - [Monitoring ve Maintenance](./databases/postgresql/monitoring.md)

#### Microsoft SQL Server
- [SQL Server Complete Guide](./databases/mssql/README.md)
  - [Installation ve Configuration](./databases/mssql/installation-config.md)
  - [T-SQL Advanced Features](./databases/mssql/tsql-advanced.md)
  - [SQL Server Management Studio (SSMS)](./databases/mssql/ssms.md)
  - [Always On Availability Groups](./databases/mssql/always-on.md)
  - [SQL Server Agent](./databases/mssql/sql-agent.md)
  - [Columnstore Indexes](./databases/mssql/columnstore.md)
  - [In-Memory OLTP](./databases/mssql/in-memory-oltp.md)
  - [Query Store](./databases/mssql/query-store.md)
  - [Performance Tuning](./databases/mssql/performance-tuning.md)
  - [Security Features](./databases/mssql/security.md)
  - [Backup Strategies](./databases/mssql/backup-strategies.md)

#### MySQL
- [MySQL Complete Guide](./databases/mysql/README.md)
  - [Installation ve Configuration](./databases/mysql/installation-config.md)
  - [Storage Engines (InnoDB, MyISAM)](./databases/mysql/storage-engines.md)
  - [MySQL-Specific Features](./databases/mysql/mysql-features.md)
  - [Replication (Master-Slave, Master-Master)](./databases/mysql/replication.md)
  - [MySQL Cluster](./databases/mysql/cluster.md)
  - [Performance Schema](./databases/mysql/performance-schema.md)
  - [Query Optimization](./databases/mysql/query-optimization.md)
  - [Backup ve Recovery](./databases/mysql/backup-recovery.md)

### NoSQL Veritabanları

#### Redis (Key-Value Store)
- [Redis Complete Guide](./databases/redis/README.md)
  - [Installation ve Configuration](./databases/redis/installation-config.md)
  - [Data Types ve Operations](./databases/redis/data-types.md)
  - [Persistence (RDB, AOF)](./databases/redis/persistence.md)
  - [Redis Cluster](./databases/redis/clustering.md)
  - [Pub/Sub Messaging](./databases/redis/pubsub.md)
  - [Redis Streams](./databases/redis/streams.md)
  - [Lua Scripting](./databases/redis/lua-scripting.md)
  - [Memory Optimization](./databases/redis/memory-optimization.md)
  - [Redis Modules](./databases/redis/modules.md)
  - [Monitoring ve Debugging](./databases/redis/monitoring.md)

#### MongoDB (Document Store)
- [MongoDB Complete Guide](./databases/mongodb/README.md)
  - [Installation ve Configuration](./databases/mongodb/installation-config.md)
  - [Document Model ve BSON](./databases/mongodb/document-model.md)
  - [Query Language](./databases/mongodb/query-language.md)
  - [Aggregation Pipeline](./databases/mongodb/aggregation.md)
  - [Indexing Strategies](./databases/mongodb/indexing.md)
  - [Sharding Implementation](./databases/mongodb/sharding.md)
  - [Replica Sets](./databases/mongodb/replica-sets.md)
  - [GridFS](./databases/mongodb/gridfs.md)
  - [Change Streams](./databases/mongodb/change-streams.md)
  - [Performance Optimization](./databases/mongodb/performance.md)

#### Amazon DynamoDB (Managed NoSQL)
- [DynamoDB Complete Guide](./databases/dynamodb/README.md)
  - [Table Design](./databases/dynamodb/table-design.md)
  - [Partition Keys ve Sort Keys](./databases/dynamodb/keys-design.md)
  - [Global Secondary Indexes (GSI)](./databases/dynamodb/gsi.md)
  - [Local Secondary Indexes (LSI)](./databases/dynamodb/lsi.md)
  - [DynamoDB Streams](./databases/dynamodb/streams.md)
  - [Transactions](./databases/dynamodb/transactions.md)
  - [Capacity Planning](./databases/dynamodb/capacity-planning.md)
  - [Cost Optimization](./databases/dynamodb/cost-optimization.md)
  - [DynamoDB Accelerator (DAX)](./databases/dynamodb/dax.md)
  - [Best Practices](./databases/dynamodb/best-practices.md)

### Search ve Analytics Engines

#### Elasticsearch
- [Elasticsearch Complete Guide](./databases/elasticsearch/README.md)
  - [Installation ve Cluster Setup](./databases/elasticsearch/installation-cluster.md)
  - [Index Management](./databases/elasticsearch/index-management.md)
  - [Mapping ve Field Types](./databases/elasticsearch/mapping.md)
  - [Query DSL](./databases/elasticsearch/query-dsl.md)
  - [Aggregations](./databases/elasticsearch/aggregations.md)
  - [Search Templates](./databases/elasticsearch/search-templates.md)
  - [Analyzers ve Tokenizers](./databases/elasticsearch/analyzers.md)
  - [Performance Tuning](./databases/elasticsearch/performance-tuning.md)
  - [Monitoring ve Alerting](./databases/elasticsearch/monitoring.md)
  - [Security (X-Pack)](./databases/elasticsearch/security.md)

### Graph Databases

#### Neo4j
- [Neo4j Complete Guide](./databases/neo4j/README.md)
  - [Installation ve Configuration](./databases/neo4j/installation-config.md)
  - [Cypher Query Language](./databases/neo4j/cypher.md)
  - [Graph Data Modeling](./databases/neo4j/graph-modeling.md)
  - [Indexing ve Constraints](./databases/neo4j/indexing-constraints.md)
  - [APOC Procedures](./databases/neo4j/apoc.md)
  - [Performance Optimization](./databases/neo4j/performance.md)
  - [Clustering (Enterprise)](./databases/neo4j/clustering.md)

## 🛠️ Database Development ve Operations

### Development Best Practices
- [Database Development](./development/README.md)
  - [Schema Version Control](./development/schema-version-control.md)
  - [Database Migration Best Practices](./development/migration-best-practices.md)
  - [Database Testing Strategies](./development/testing-strategies.md)
  - [Unit Testing for Databases](./development/unit-testing.md)
  - [Integration Testing](./development/integration-testing.md)
  - [Database CI/CD Pipelines](./development/cicd-pipelines.md)
  - [Code Review for Database Changes](./development/code-review.md)
  - [Database Refactoring](./development/refactoring.md)

### Database Operations (DevOps)
- [Database Operations](./operations/README.md)
  - [Deployment Strategies](./operations/deployment-strategies.md)
  - [Blue-Green Deployments](./operations/blue-green-deployment.md)
  - [Rolling Updates](./operations/rolling-updates.md)
  - [Disaster Recovery Planning](./operations/disaster-recovery.md)
  - [Capacity Planning](./operations/capacity-planning.md)
  - [Maintenance Windows](./operations/maintenance-windows.md)
  - [Database Automation](./operations/automation.md)
  - [Infrastructure as Code](./operations/infrastructure-as-code.md)

### Tools ve Utilities
- [Database Tools Ecosystem](./tools/README.md)
  - [Administration Tools](./tools/admin-tools.md)
  - [Migration Tools](./tools/migration-tools.md)
  - [Monitoring ve APM Tools](./tools/monitoring-tools.md)
  - [Backup ve Recovery Tools](./tools/backup-tools.md)
  - [Load Testing Tools](./tools/load-testing-tools.md)
  - [Schema Comparison Tools](./tools/schema-comparison.md)
  - [Database IDE'ler](./tools/database-ides.md)
  - [Command Line Tools](./tools/cli-tools.md)

## 📋 Practical Resources

### Checklists
- [Database Selection Checklist](./checklists/database-selection.md)
- [Performance Review Checklist](./checklists/performance-review.md)
- [Security Audit Checklist](./checklists/security-audit.md)
- [Pre-Production Checklist](./checklists/pre-production.md)
- [Go-Live Checklist](./checklists/go-live.md)

### Templates ve Runbooks
- [Migration Planning Template](./templates/migration-planning.md)
- [Incident Response Runbook](./templates/incident-response.md)
- [Performance Investigation Template](./templates/performance-investigation.md)
- [Database Health Check Template](./templates/health-check.md)
- [Capacity Planning Template](./templates/capacity-planning.md)

## 🎯 Real-World Use Cases ve Patterns

### Industry-Specific Patterns
- [E-commerce Database Architecture](./use-cases/ecommerce/README.md)
  - [Product Catalog Design](./use-cases/ecommerce/product-catalog.md)
  - [Order Management System](./use-cases/ecommerce/order-management.md)
  - [Inventory Management](./use-cases/ecommerce/inventory-management.md)
  - [Payment Processing](./use-cases/ecommerce/payment-processing.md)

- [Financial Services](./use-cases/financial/README.md)
  - [Transaction Processing](./use-cases/financial/transaction-processing.md)
  - [Risk Management Systems](./use-cases/financial/risk-management.md)
  - [Regulatory Compliance](./use-cases/financial/compliance.md)
  - [Real-time Fraud Detection](./use-cases/financial/fraud-detection.md)

- [Social Media Platforms](./use-cases/social-media/README.md)
  - [User Profile Management](./use-cases/social-media/user-profiles.md)
  - [Activity Feeds](./use-cases/social-media/activity-feeds.md)
  - [Content Management](./use-cases/social-media/content-management.md)
  - [Messaging Systems](./use-cases/social-media/messaging.md)

- [IoT ve Time-Series Data](./use-cases/iot/README.md)
  - [Sensor Data Ingestion](./use-cases/iot/sensor-data.md)
  - [Time-Series Optimization](./use-cases/iot/time-series-optimization.md)
  - [Real-time Analytics](./use-cases/iot/real-time-analytics.md)
  - [Data Retention Policies](./use-cases/iot/data-retention.md)

### Common Patterns
- [Multi-tenancy Patterns](./patterns/multi-tenancy.md)
- [Event-Driven Architecture](./patterns/event-driven.md)
- [Polyglot Persistence](./patterns/polyglot-persistence.md)
- [Database Proxy Patterns](./patterns/database-proxy.md)
- [Read-Write Splitting](./patterns/read-write-splitting.md)

## � Learning Resources

### Tutorials ve Workshops
- [Beginner's Guide to Databases](./tutorials/beginners-guide.md)
- [SQL Mastery Workshop](./tutorials/sql-mastery.md)
- [NoSQL Hands-on Labs](./tutorials/nosql-labs.md)
- [Performance Tuning Workshop](./tutorials/performance-tuning.md)
- [Database Security Workshop](./tutorials/security-workshop.md)

### Troubleshooting Guides
- [Common Database Issues](./troubleshooting/common-issues.md)
- [Performance Problems](./troubleshooting/performance-problems.md)
- [Connection Issues](./troubleshooting/connection-issues.md)
- [Data Corruption Recovery](./troubleshooting/data-corruption.md)
- [Replication Problems](./troubleshooting/replication-problems.md)

## 📖 Reference

### Quick References
- [SQL Quick Reference](./reference/sql-quick-reference.md)
- [Database Terminology](./reference/terminology.md)
- [Port Numbers ve Defaults](./reference/ports-defaults.md)
- [Configuration Parameters](./reference/config-parameters.md)
- [Error Codes Reference](./reference/error-codes.md)

### Comparison Tables
- [Database Feature Comparison](./reference/feature-comparison.md)
- [Performance Benchmarks](./reference/performance-benchmarks.md)
- [Licensing Comparison](./reference/licensing-comparison.md)
- [Cloud Provider Comparison](./reference/cloud-comparison.md)

---

## 🤝 Contributing

Bu repository'ye katkıda bulunmak için:
1. [CONTRIBUTING.md](./CONTRIBUTING.md) dosyasını okuyun
2. Issue açın veya mevcut issue'lara katkıda bulunun
3. Pull request gönderin

## 📄 License

Bu proje MIT lisansı altında lisanslanmıştır - detaylar için [LICENSE](./LICENSE) dosyasına bakın.

## 🔗 Related Repositories

- [System Design Documentation](../system-design) - Teorik system design konuları
- [Architecture Patterns](../architecture-patterns) - Genel architecture patterns
- [DevOps Best Practices](../devops-practices) - DevOps ve operations