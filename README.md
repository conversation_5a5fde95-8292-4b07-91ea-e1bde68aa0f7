# Database Documentation Hub

Bu repository, modern yazılım geliştirme ve sistem mimarisi için gerekli tüm database konularını kapsamlı bir şekilde dokumente etmektedir. Developer'dan architect se<PERSON><PERSON><PERSON> kadar tüm roller için referans kaynak olarak tasarlanmıştır.

## 📚 İçindekiler

### 🔰 Temel Kavramlar ve Ortak Konular
- [Database Temelleri](./fundamentals/README.md)
  - [ACID Özellikleri](./fundamentals/acid-properties.md)
  - [CAP Teoremi](./fundamentals/cap-theorem.md)
  - [Consistency Modelleri](./fundamentals/consistency-models.md)
  - [Sharding ve Partitioning](./fundamentals/sharding-partitioning.md)
  - [Replication Stratejileri](./fundamentals/replication-strategies.md)
  - [Backup ve Recovery](./fundamentals/backup-recovery.md)

### 🔒 Transaction Management ve Isolation
- [Transaction Yönetimi](./transactions/README.md)
  - [Isolation Seviyeleri](./transactions/isolation-levels.md)
  - [Locking Mekanizmaları](./transactions/locking-mechanisms.md)
  - [Deadlock Yönetimi](./transactions/deadlock-management.md)
  - [Distributed Transactions](./transactions/distributed-transactions.md)
  - [Two-Phase Commit](./transactions/two-phase-commit.md)

### 🗃️ Database Türleri ve Karşılaştırma
- [Database Türleri](./database-types/README.md)
  - [İlişkisel Veritabanları (RDBMS)](./database-types/relational.md)
  - [NoSQL Veritabanları](./database-types/nosql.md)
    - [Document Stores](./database-types/document-stores.md)
    - [Key-Value Stores](./database-types/key-value-stores.md)
    - [Column-Family](./database-types/column-family.md)
    - [Graph Databases](./database-types/graph-databases.md)
  - [NewSQL Veritabanları](./database-types/newsql.md)
  - [Time-Series Databases](./database-types/time-series.md)
  - [Search Engines](./database-types/search-engines.md)

### 📊 SQL ve Query Optimization
- [SQL Fundamentals](./sql/README.md)
  - [Temel SQL Komutları](./sql/basic-commands.md)
  - [İleri SQL Teknikleri](./sql/advanced-techniques.md)
  - [Window Functions](./sql/window-functions.md)
  - [Common Table Expressions (CTE)](./sql/cte.md)
  - [Stored Procedures ve Functions](./sql/stored-procedures.md)
  - [Triggers](./sql/triggers.md)
- [Query Optimization](./optimization/README.md)
  - [Index Stratejileri](./optimization/indexing-strategies.md)
  - [Query Execution Plans](./optimization/execution-plans.md)
  - [Performance Tuning](./optimization/performance-tuning.md)
  - [Query Profiling](./optimization/query-profiling.md)

### 🏗️ Database Design ve Architecture
- [Database Design](./design/README.md)
  - [Normalization](./design/normalization.md)
  - [Denormalization](./design/denormalization.md)
  - [Schema Design Patterns](./design/schema-patterns.md)
  - [Data Modeling](./design/data-modeling.md)
  - [Entity Relationship Diagrams](./design/erd.md)
- [Architecture Patterns](./architecture/README.md)
  - [Master-Slave Architecture](./architecture/master-slave.md)
  - [Master-Master Architecture](./architecture/master-master.md)
  - [Microservices ve Database](./architecture/microservices-db.md)
  - [Database per Service Pattern](./architecture/database-per-service.md)
  - [Shared Database Anti-Pattern](./architecture/shared-database.md)
  - [Event Sourcing](./architecture/event-sourcing.md)
  - [CQRS (Command Query Responsibility Segregation)](./architecture/cqrs.md)

### ⚡ Performance ve Scaling
- [Performance Optimization](./performance/README.md)
  - [Connection Pooling](./performance/connection-pooling.md)
  - [Caching Strategies](./performance/caching-strategies.md)
  - [Read Replicas](./performance/read-replicas.md)
  - [Load Balancing](./performance/load-balancing.md)
- [Scaling Strategies](./scaling/README.md)
  - [Vertical vs Horizontal Scaling](./scaling/vertical-vs-horizontal.md)
  - [Database Sharding](./scaling/database-sharding.md)
  - [Federation](./scaling/federation.md)
  - [Auto-scaling](./scaling/auto-scaling.md)

### 🔐 Security ve Compliance
- [Database Security](./security/README.md)
  - [Authentication ve Authorization](./security/auth.md)
  - [Encryption (At Rest & In Transit)](./security/encryption.md)
  - [SQL Injection Prevention](./security/sql-injection.md)
  - [Audit Logging](./security/audit-logging.md)
  - [Data Masking](./security/data-masking.md)
  - [GDPR ve Data Privacy](./security/gdpr-privacy.md)

### 📈 Monitoring ve Observability
- [Database Monitoring](./monitoring/README.md)
  - [Key Metrics](./monitoring/key-metrics.md)
  - [Alerting Strategies](./monitoring/alerting.md)
  - [Log Analysis](./monitoring/log-analysis.md)
  - [Performance Monitoring Tools](./monitoring/tools.md)

## 🗄️ Database-Specific Guides

### İlişkisel Veritabanları
- [PostgreSQL](./databases/postgresql/README.md)
  - [Installation ve Setup](./databases/postgresql/installation.md)
  - [Configuration](./databases/postgresql/configuration.md)
  - [Advanced Features](./databases/postgresql/advanced-features.md)
  - [Extensions](./databases/postgresql/extensions.md)
  - [Performance Tuning](./databases/postgresql/performance-tuning.md)
  - [Backup ve Recovery](./databases/postgresql/backup-recovery.md)
  - [High Availability](./databases/postgresql/high-availability.md)

- [Microsoft SQL Server](./databases/mssql/README.md)
  - [Installation ve Setup](./databases/mssql/installation.md)
  - [T-SQL Specifics](./databases/mssql/tsql.md)
  - [SQL Server Management Studio](./databases/mssql/ssms.md)
  - [Always On Availability Groups](./databases/mssql/always-on.md)
  - [Performance Tuning](./databases/mssql/performance-tuning.md)
  - [Security Features](./databases/mssql/security.md)

- [MySQL](./databases/mysql/README.md)
  - [Installation ve Setup](./databases/mysql/installation.md)
  - [Storage Engines](./databases/mysql/storage-engines.md)
  - [Replication](./databases/mysql/replication.md)
  - [Performance Tuning](./databases/mysql/performance-tuning.md)

### NoSQL Veritabanları
- [Redis](./databases/redis/README.md)
  - [Data Types](./databases/redis/data-types.md)
  - [Persistence Options](./databases/redis/persistence.md)
  - [Clustering](./databases/redis/clustering.md)
  - [Pub/Sub](./databases/redis/pubsub.md)
  - [Performance Optimization](./databases/redis/performance.md)
  - [Use Cases](./databases/redis/use-cases.md)

- [MongoDB](./databases/mongodb/README.md)
  - [Document Model](./databases/mongodb/document-model.md)
  - [Aggregation Framework](./databases/mongodb/aggregation.md)
  - [Indexing](./databases/mongodb/indexing.md)
  - [Sharding](./databases/mongodb/sharding.md)
  - [Replica Sets](./databases/mongodb/replica-sets.md)

- [Amazon DynamoDB](./databases/dynamodb/README.md)
  - [Key Concepts](./databases/dynamodb/key-concepts.md)
  - [Partition Keys ve Sort Keys](./databases/dynamodb/keys.md)
  - [Global Secondary Indexes](./databases/dynamodb/gsi.md)
  - [DynamoDB Streams](./databases/dynamodb/streams.md)
  - [Capacity Planning](./databases/dynamodb/capacity-planning.md)
  - [Best Practices](./databases/dynamodb/best-practices.md)

### Search ve Analytics
- [Elasticsearch](./databases/elasticsearch/README.md)
  - [Core Concepts](./databases/elasticsearch/core-concepts.md)
  - [Mapping ve Indexing](./databases/elasticsearch/mapping-indexing.md)
  - [Query DSL](./databases/elasticsearch/query-dsl.md)
  - [Aggregations](./databases/elasticsearch/aggregations.md)
  - [Cluster Management](./databases/elasticsearch/cluster-management.md)
  - [Performance Tuning](./databases/elasticsearch/performance-tuning.md)

- [Apache Solr](./databases/solr/README.md)
  - [Configuration](./databases/solr/configuration.md)
  - [Schema Design](./databases/solr/schema-design.md)
  - [Faceting](./databases/solr/faceting.md)

### Graph Databases
- [Neo4j](./databases/neo4j/README.md)
  - [Cypher Query Language](./databases/neo4j/cypher.md)
  - [Graph Modeling](./databases/neo4j/graph-modeling.md)
  - [Performance Optimization](./databases/neo4j/performance.md)

## 🛠️ Practical Guides

### Development Practices
- [Database Development Best Practices](./practices/README.md)
  - [Version Control for Databases](./practices/version-control.md)
  - [Database Migration Strategies](./practices/migrations.md)
  - [Testing Strategies](./practices/testing.md)
  - [CI/CD for Databases](./practices/cicd.md)
  - [Code Review Guidelines](./practices/code-review.md)

### Operations
- [Database Operations](./operations/README.md)
  - [Deployment Strategies](./operations/deployment.md)
  - [Disaster Recovery](./operations/disaster-recovery.md)
  - [Capacity Planning](./operations/capacity-planning.md)
  - [Maintenance Windows](./operations/maintenance.md)

### Tools ve Utilities
- [Database Tools](./tools/README.md)
  - [Administration Tools](./tools/admin-tools.md)
  - [Migration Tools](./tools/migration-tools.md)
  - [Monitoring Tools](./tools/monitoring-tools.md)
  - [Backup Tools](./tools/backup-tools.md)
  - [Load Testing Tools](./tools/load-testing.md)

## 📋 Checklists ve Templates

- [Database Selection Checklist](./checklists/database-selection.md)
- [Performance Review Checklist](./checklists/performance-review.md)
- [Security Audit Checklist](./checklists/security-audit.md)
- [Migration Planning Template](./templates/migration-planning.md)
- [Incident Response Template](./templates/incident-response.md)

## 🎯 Use Cases ve Scenarios

- [E-commerce Database Design](./use-cases/ecommerce.md)
- [Real-time Analytics](./use-cases/real-time-analytics.md)
- [IoT Data Management](./use-cases/iot-data.md)
- [Social Media Platform](./use-cases/social-media.md)
- [Financial Systems](./use-cases/financial-systems.md)

## 📖 Glossary

- [Database Terminology](./glossary/README.md)

---

## 🤝 Katkıda Bulunma

Bu dokümantasyona katkıda bulunmak için lütfen [CONTRIBUTING.md](./CONTRIBUTING.md) dosyasını okuyun.

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](./LICENSE) dosyasına bakın.