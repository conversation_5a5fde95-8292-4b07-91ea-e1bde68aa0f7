# Indexing

Database indexing is a critical performance optimization technique that creates data structures to improve query speed. This section covers comprehensive indexing strategies, types, maintenance, and best practices for optimal database performance.

## 📚 Contents

### Core Indexing Concepts
- [B-tree Indexes](./btree-indexes.md) - The most common index type for range and equality queries
- [Hash Indexes](./hash-indexes.md) - Optimized for equality comparisons
- [Bitmap Indexes](./bitmap-indexes.md) - Efficient for low-cardinality data and complex queries
- [Partial Indexes](./partial-indexes.md) - Indexes on subsets of data with conditions
- [Composite Indexes](./composite-indexes.md) - Multi-column indexes for complex queries
- [Index Maintenance](./index-maintenance.md) - Keeping indexes healthy and performant

## 🎯 Learning Path

### Beginner (Start Here)
1. [B-tree Indexes](./btree-indexes.md) - Master the fundamental index type
2. [Composite Indexes](./composite-indexes.md) - Learn multi-column indexing
3. [Index Maintenance](./index-maintenance.md) - Keep indexes healthy

### Intermediate
4. [Partial Indexes](./partial-indexes.md) - Optimize for specific conditions
5. [Hash Indexes](./hash-indexes.md) - Understand equality-optimized indexes

### Advanced
6. [Bitmap Indexes](./bitmap-indexes.md) - Handle complex analytical queries

## 💡 Indexing Fundamentals

### What Are Indexes?
Indexes are data structures that improve query performance by providing fast access paths to table data. They work like an index in a book - instead of scanning every page, you can jump directly to the relevant content.

### Index Benefits
- **Faster Queries**: Dramatically reduce query execution time
- **Efficient Sorting**: Speed up ORDER BY operations
- **Quick Joins**: Accelerate table joins
- **Unique Constraints**: Enforce data uniqueness
- **Foreign Key Performance**: Speed up referential integrity checks

### Index Costs
- **Storage Overhead**: Additional disk space required
- **Maintenance Cost**: Slower INSERT/UPDATE/DELETE operations
- **Memory Usage**: Indexes consume buffer pool memory
- **Complexity**: More objects to manage and monitor

## 🔧 Index Types Overview

### B-tree Indexes (Default)
```sql
-- Standard B-tree index
CREATE INDEX idx_customers_email ON customers(email);

-- Characteristics:
-- - Balanced tree structure
-- - Good for range queries (>, <, BETWEEN)
-- - Supports equality queries (=)
-- - Maintains sorted order
-- - Most common index type
```

### Hash Indexes
```sql
-- PostgreSQL: Hash index for equality only
CREATE INDEX idx_products_sku_hash ON products USING HASH(sku);

-- Characteristics:
-- - Optimized for equality comparisons (=)
-- - Cannot be used for range queries
-- - Faster than B-tree for exact matches
-- - Smaller than B-tree indexes
```

### Partial Indexes
```sql
-- Index only active records
CREATE INDEX idx_active_orders ON orders(order_date) 
WHERE status = 'active';

-- Benefits:
-- - Smaller index size
-- - Faster maintenance
-- - Reduced storage costs
-- - Better cache utilization
```

### Composite Indexes
```sql
-- Multi-column index
CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date);

-- Column order matters:
-- - Most selective column first
-- - Equality conditions before range conditions
-- - Consider query patterns
```

### Covering Indexes
```sql
-- Include additional columns
CREATE INDEX idx_orders_covering 
ON orders(customer_id, order_date) 
INCLUDE (order_amount, status);

-- Benefits:
-- - Avoid table lookups
-- - All query data in index
-- - Faster query execution
```

## 📊 Index Design Strategies

### Selectivity Analysis
```sql
-- Calculate column selectivity
SELECT 
    'customer_id' as column_name,
    COUNT(DISTINCT customer_id) as unique_values,
    COUNT(*) as total_rows,
    (COUNT(DISTINCT customer_id) * 100.0 / COUNT(*)) as selectivity_percent
FROM orders
UNION ALL
SELECT 
    'status',
    COUNT(DISTINCT status),
    COUNT(*),
    (COUNT(DISTINCT status) * 100.0 / COUNT(*))
FROM orders;

-- High selectivity (>5%): Good for indexing
-- Low selectivity (<1%): Consider bitmap or partial indexes
```

### Query Pattern Analysis
```sql
-- Analyze common query patterns
-- Pattern 1: Single column equality
SELECT * FROM customers WHERE email = '<EMAIL>';
-- Index: CREATE INDEX idx_customers_email ON customers(email);

-- Pattern 2: Range queries
SELECT * FROM orders WHERE order_date BETWEEN '2024-01-01' AND '2024-12-31';
-- Index: CREATE INDEX idx_orders_date ON orders(order_date);

-- Pattern 3: Multiple conditions
SELECT * FROM products WHERE category = 'electronics' AND price > 100;
-- Index: CREATE INDEX idx_products_category_price ON products(category, price);

-- Pattern 4: Sorting
SELECT * FROM customers ORDER BY last_name, first_name;
-- Index: CREATE INDEX idx_customers_name ON customers(last_name, first_name);
```

### Index Strategy by Workload

#### OLTP (Online Transaction Processing)
```sql
-- Focus on:
-- - Point lookups
-- - Small result sets
-- - Fast INSERT/UPDATE/DELETE

-- Primary key indexes (automatic)
-- Foreign key indexes
CREATE INDEX idx_orders_customer_id ON orders(customer_id);

-- Unique constraints
CREATE UNIQUE INDEX idx_customers_email ON customers(email);

-- Covering indexes for frequent queries
CREATE INDEX idx_orders_summary 
ON orders(customer_id, order_date) 
INCLUDE (order_amount, status);
```

#### OLAP (Online Analytical Processing)
```sql
-- Focus on:
-- - Large scans
-- - Aggregations
-- - Complex queries

-- Bitmap indexes for low-cardinality columns
CREATE INDEX idx_sales_region_bitmap ON sales(region); -- If supported

-- Partial indexes for filtered aggregations
CREATE INDEX idx_sales_current_year ON sales(product_id, sale_amount)
WHERE sale_date >= '2024-01-01';

-- Composite indexes for GROUP BY
CREATE INDEX idx_sales_grouping ON sales(region, product_category, sale_date);
```

## 🛠️ Index Monitoring

### Index Usage Statistics
```sql
-- PostgreSQL: Index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as times_used,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- SQL Server: Index usage
SELECT 
    i.name as index_name,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates
FROM sys.indexes i
LEFT JOIN sys.dm_db_index_usage_stats s 
    ON i.object_id = s.object_id AND i.index_id = s.index_id
WHERE i.object_id = OBJECT_ID('orders');

-- MySQL: Index usage
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    CARDINALITY
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA = 'myapp'
ORDER BY CARDINALITY DESC;
```

### Unused Index Detection
```sql
-- PostgreSQL: Find unused indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes
WHERE idx_scan = 0
    AND schemaname = 'public'
ORDER BY pg_relation_size(indexrelid) DESC;

-- Consider dropping unused indexes:
-- DROP INDEX IF EXISTS idx_unused_index;
```

### Index Size Analysis
```sql
-- PostgreSQL: Index sizes
SELECT 
    schemaname,
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size
FROM pg_stat_user_indexes
ORDER BY pg_relation_size(indexrelid) DESC;

-- SQL Server: Index sizes
SELECT 
    i.name as index_name,
    SUM(s.used_page_count) * 8 as size_kb
FROM sys.indexes i
JOIN sys.dm_db_partition_stats s ON i.object_id = s.object_id AND i.index_id = s.index_id
WHERE i.object_id = OBJECT_ID('orders')
GROUP BY i.name
ORDER BY size_kb DESC;
```

## 📈 Performance Impact

### Query Performance Improvement
```sql
-- Before index: Table scan
EXPLAIN ANALYZE
SELECT * FROM orders WHERE customer_id = 123;
-- Seq Scan on orders (cost=0.00..1000.00 rows=50 width=100) (actual time=50.123..50.125 rows=50 loops=1)

-- After index: Index scan
CREATE INDEX idx_orders_customer_id ON orders(customer_id);

EXPLAIN ANALYZE
SELECT * FROM orders WHERE customer_id = 123;
-- Index Scan using idx_orders_customer_id (cost=0.29..8.30 rows=50 width=100) (actual time=0.123..0.125 rows=50 loops=1)
```

### Write Performance Impact
```sql
-- Measure INSERT performance with indexes
-- Without indexes
\timing on
INSERT INTO test_table (col1, col2, col3) 
SELECT i, 'data' || i, random() FROM generate_series(1, 100000) i;
-- Time: 1234.567 ms

-- With multiple indexes
CREATE INDEX idx_test_col1 ON test_table(col1);
CREATE INDEX idx_test_col2 ON test_table(col2);
CREATE INDEX idx_test_col3 ON test_table(col3);

INSERT INTO test_table (col1, col2, col3) 
SELECT i, 'data' || i, random() FROM generate_series(100001, 200000) i;
-- Time: 2345.678 ms (slower due to index maintenance)
```

## 🎯 Best Practices

### 1. Index Design Guidelines
```sql
-- Good: Selective columns
CREATE INDEX idx_orders_status ON orders(status) 
WHERE status IN ('pending', 'processing'); -- Only index active statuses

-- Good: Composite index with proper column order
CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date);
-- customer_id (equality) before order_date (range)

-- Avoid: Over-indexing
-- Don't create indexes on every column
-- Consider maintenance overhead
```

### 2. Query-Driven Index Design
```sql
-- Analyze actual queries first
-- Common query pattern:
SELECT order_id, order_date, total_amount
FROM orders 
WHERE customer_id = ? AND status = 'shipped'
ORDER BY order_date DESC;

-- Optimal index:
CREATE INDEX idx_orders_customer_status_date 
ON orders(customer_id, status, order_date DESC)
INCLUDE (order_id, total_amount);
```

### 3. Regular Index Maintenance
```sql
-- PostgreSQL: Reindex when needed
REINDEX INDEX idx_orders_customer_id;
REINDEX TABLE orders;

-- SQL Server: Rebuild fragmented indexes
ALTER INDEX idx_orders_customer_id ON orders REBUILD;

-- MySQL: Optimize tables
OPTIMIZE TABLE orders;
```

### 4. Monitor and Tune
```python
# Example: Automated index monitoring
def monitor_index_performance():
    """Monitor index usage and performance"""
    with database.cursor() as cur:
        # Find unused indexes
        cur.execute("""
            SELECT indexname, pg_size_pretty(pg_relation_size(indexrelid)) as size
            FROM pg_stat_user_indexes 
            WHERE idx_scan = 0 AND schemaname = 'public'
        """)
        
        unused_indexes = cur.fetchall()
        if unused_indexes:
            print("Unused indexes found:")
            for index_name, size in unused_indexes:
                print(f"  {index_name}: {size}")
        
        # Find missing indexes (high seq_scan tables)
        cur.execute("""
            SELECT tablename, seq_scan, seq_tup_read, 
                   seq_tup_read / GREATEST(seq_scan, 1) as avg_rows_per_scan
            FROM pg_stat_user_tables 
            WHERE seq_scan > 1000 AND seq_tup_read / GREATEST(seq_scan, 1) > 1000
            ORDER BY seq_tup_read DESC
        """)
        
        scan_heavy_tables = cur.fetchall()
        if scan_heavy_tables:
            print("Tables with heavy sequential scans:")
            for table, scans, reads, avg_rows in scan_heavy_tables:
                print(f"  {table}: {scans} scans, {avg_rows:.0f} avg rows per scan")

# Schedule regular monitoring
import schedule
schedule.every().day.at("02:00").do(monitor_index_performance)
```

## 🔍 Troubleshooting

### Common Index Issues
```sql
-- Issue 1: Index not being used
-- Check if query conditions match index
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM orders WHERE UPPER(customer_email) = '<EMAIL>';
-- Solution: Create functional index
CREATE INDEX idx_orders_email_upper ON orders(UPPER(customer_email));

-- Issue 2: Wrong column order in composite index
-- Index: (order_date, customer_id) 
-- Query: WHERE customer_id = 123 AND order_date > '2024-01-01'
-- Solution: Recreate with correct order
DROP INDEX idx_orders_date_customer;
CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date);

-- Issue 3: Index bloat
-- Check index bloat
SELECT 
    schemaname, tablename, indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes 
WHERE pg_relation_size(indexrelid) > 100000000; -- > 100MB

-- Solution: Reindex
REINDEX INDEX CONCURRENTLY idx_large_index;
```

### Performance Debugging
```sql
-- Enable query logging for analysis
-- PostgreSQL
SET log_statement = 'all';
SET log_min_duration_statement = 1000; -- Log queries > 1 second

-- Analyze slow queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
WHERE mean_time > 1000 -- > 1 second average
ORDER BY total_time DESC;
```

## 📋 Index Strategy Checklist

### Design Phase
- [ ] Analyze query patterns and frequency
- [ ] Identify high-selectivity columns
- [ ] Consider composite index column order
- [ ] Plan for covering indexes where beneficial
- [ ] Estimate storage and maintenance costs

### Implementation Phase
- [ ] Create indexes during low-traffic periods
- [ ] Use CONCURRENTLY option when available
- [ ] Monitor index creation progress
- [ ] Validate index usage after creation
- [ ] Update application query plans if needed

### Maintenance Phase
- [ ] Monitor index usage statistics
- [ ] Check for index fragmentation
- [ ] Identify and remove unused indexes
- [ ] Reindex when necessary
- [ ] Review and optimize based on workload changes

## 🔗 Related Topics

After mastering indexing fundamentals:
- [Query Optimization](../optimization/README.md) - Optimize queries to use indexes effectively
- [Performance Tuning](../performance/README.md) - System-level performance optimization
- [Database Design](../design/README.md) - Design schemas with indexing in mind
- [Monitoring](../monitoring/README.md) - Monitor index performance and health

## 📚 Further Reading

- Database-specific indexing documentation
- Query optimizer internals
- Index design patterns and anti-patterns
- Performance benchmarking methodologies

Start with [B-tree Indexes](./btree-indexes.md) to understand the most fundamental index type, then explore [Composite Indexes](./composite-indexes.md) for multi-column indexing strategies!
