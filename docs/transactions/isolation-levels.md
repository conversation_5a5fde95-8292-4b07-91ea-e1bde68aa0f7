# Isolation Levels

Isolation levels define how transactions interact with each other and what phenomena are allowed or prevented. Understanding isolation levels is crucial for maintaining data consistency while optimizing performance in concurrent database systems.

## 📋 Table of Contents

1. [ACID Properties](#acid-properties)
2. [Concurrency Phenomena](#concurrency-phenomena)
3. [Standard Isolation Levels](#standard-isolation-levels)
4. [Database-Specific Implementations](#database-specific-implementations)
5. [Choosing the Right Isolation Level](#choosing-the-right-isolation-level)
6. [Performance Implications](#performance-implications)
7. [Best Practices](#best-practices)

## ACID Properties

### Atomicity
All operations in a transaction succeed or fail together.
```sql
-- Example: Bank transfer (atomic operation)
BEGIN;
    UPDATE accounts SET balance = balance - 100 WHERE account_id = 1;
    UPDATE accounts SET balance = balance + 100 WHERE account_id = 2;
COMMIT; -- Both updates succeed or both fail
```

### Consistency
Database remains in a valid state after transactions.
```sql
-- Example: Referential integrity maintained
BEGIN;
    INSERT INTO orders (customer_id, order_date) VALUES (123, '2024-01-01');
    -- customer_id 123 must exist in customers table
COMMIT;
```

### Isolation
Concurrent transactions don't interfere with each other.
```sql
-- Transaction 1
BEGIN;
    SELECT balance FROM accounts WHERE account_id = 1; -- Reads 1000
    -- ... other operations
COMMIT;

-- Transaction 2 (concurrent)
BEGIN;
    UPDATE accounts SET balance = 500 WHERE account_id = 1;
COMMIT;
-- Transaction 1 should not see the change until committed
```

### Durability
Committed changes persist even after system failures.
```sql
-- Once committed, changes survive system crashes
BEGIN;
    INSERT INTO critical_data (value) VALUES ('important');
COMMIT; -- Data is now durable
-- Even if system crashes here, data persists
```

## Concurrency Phenomena

### Dirty Read
Reading uncommitted changes from another transaction.
```sql
-- Transaction 1
BEGIN;
    UPDATE products SET price = 150 WHERE product_id = 1; -- Not committed yet

-- Transaction 2 (concurrent)
BEGIN;
    SELECT price FROM products WHERE product_id = 1; -- Reads 150 (dirty read)
COMMIT;

-- Transaction 1
ROLLBACK; -- Price reverts to original value
-- Transaction 2 read data that was never committed
```

### Non-Repeatable Read
Getting different values when reading the same row multiple times.
```sql
-- Transaction 1
BEGIN;
    SELECT price FROM products WHERE product_id = 1; -- Reads 100
    -- ... other operations

-- Transaction 2 (concurrent)
BEGIN;
    UPDATE products SET price = 150 WHERE product_id = 1;
COMMIT;

-- Transaction 1 (continuing)
    SELECT price FROM products WHERE product_id = 1; -- Reads 150 (different value)
COMMIT;
```

### Phantom Read
New rows appearing in subsequent reads with the same query.
```sql
-- Transaction 1
BEGIN;
    SELECT COUNT(*) FROM products WHERE category = 'electronics'; -- Returns 10
    -- ... other operations

-- Transaction 2 (concurrent)
BEGIN;
    INSERT INTO products (name, category) VALUES ('New Phone', 'electronics');
COMMIT;

-- Transaction 1 (continuing)
    SELECT COUNT(*) FROM products WHERE category = 'electronics'; -- Returns 11 (phantom)
COMMIT;
```

### Lost Update
One transaction's update overwrites another's.
```sql
-- Transaction 1
BEGIN;
    SELECT quantity FROM inventory WHERE product_id = 1; -- Reads 10
    -- Calculate new quantity: 10 - 3 = 7

-- Transaction 2 (concurrent)
BEGIN;
    SELECT quantity FROM inventory WHERE product_id = 1; -- Reads 10
    -- Calculate new quantity: 10 - 2 = 8
    UPDATE inventory SET quantity = 8 WHERE product_id = 1;
COMMIT;

-- Transaction 1 (continuing)
    UPDATE inventory SET quantity = 7 WHERE product_id = 1; -- Overwrites Transaction 2's update
COMMIT;
-- Transaction 2's update is lost
```

## Standard Isolation Levels

### Read Uncommitted (Level 0)
**Allows**: Dirty reads, non-repeatable reads, phantom reads
**Prevents**: Nothing

```sql
-- PostgreSQL
BEGIN TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
    SELECT * FROM products WHERE price > 100;
COMMIT;

-- SQL Server
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
BEGIN TRANSACTION;
    SELECT * FROM products WHERE price > 100;
COMMIT;
```

**Use Cases**:
- Reporting where approximate data is acceptable
- Data warehousing scenarios
- Maximum performance requirements

**Risks**:
- Reading uncommitted data
- Data inconsistency
- Potential application errors

### Read Committed (Level 1)
**Allows**: Non-repeatable reads, phantom reads
**Prevents**: Dirty reads

```sql
-- PostgreSQL (default)
BEGIN TRANSACTION ISOLATION LEVEL READ COMMITTED;
    SELECT * FROM products WHERE price > 100;
    -- Will only see committed data
COMMIT;

-- SQL Server
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
BEGIN TRANSACTION;
    SELECT * FROM products WHERE price > 100;
COMMIT;
```

**Implementation**:
- Shared locks on read data (released immediately)
- Exclusive locks on modified data (held until commit)

**Use Cases**:
- Most OLTP applications
- Web applications
- General business applications

### Repeatable Read (Level 2)
**Allows**: Phantom reads
**Prevents**: Dirty reads, non-repeatable reads

```sql
-- PostgreSQL
BEGIN TRANSACTION ISOLATION LEVEL REPEATABLE READ;
    SELECT * FROM products WHERE price > 100;
    -- ... other operations
    SELECT * FROM products WHERE price > 100; -- Same results as first query
COMMIT;

-- SQL Server
SET TRANSACTION ISOLATION LEVEL REPEATABLE READ;
BEGIN TRANSACTION;
    SELECT * FROM products WHERE price > 100;
COMMIT;
```

**Implementation**:
- Shared locks held until transaction end
- Range locks may be used

**Use Cases**:
- Financial calculations
- Reports requiring consistent data
- Batch processing

### Serializable (Level 3)
**Allows**: Nothing
**Prevents**: Dirty reads, non-repeatable reads, phantom reads

```sql
-- PostgreSQL
BEGIN TRANSACTION ISOLATION LEVEL SERIALIZABLE;
    SELECT COUNT(*) FROM products WHERE category = 'electronics';
    INSERT INTO products (name, category) VALUES ('New Product', 'electronics');
COMMIT;

-- SQL Server
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE;
BEGIN TRANSACTION;
    SELECT COUNT(*) FROM products WHERE category = 'electronics';
COMMIT;
```

**Implementation**:
- Full range locks
- Predicate locks
- Serialization graph testing

**Use Cases**:
- Critical financial transactions
- Inventory management
- Audit systems

## Database-Specific Implementations

### PostgreSQL Isolation Levels
```sql
-- PostgreSQL uses MVCC (Multi-Version Concurrency Control)
-- Snapshot isolation for REPEATABLE READ and SERIALIZABLE

-- Check current isolation level
SHOW transaction_isolation;

-- Set session-level isolation
SET SESSION CHARACTERISTICS AS TRANSACTION ISOLATION LEVEL REPEATABLE READ;

-- Set transaction-level isolation
BEGIN ISOLATION LEVEL SERIALIZABLE;
    -- Transaction operations
COMMIT;

-- PostgreSQL-specific: Serializable Snapshot Isolation (SSI)
BEGIN ISOLATION LEVEL SERIALIZABLE;
    -- Detects serialization conflicts and may abort transactions
COMMIT;
```

### SQL Server Isolation Levels
```sql
-- SQL Server uses locking by default

-- Check current isolation level
SELECT 
    CASE transaction_isolation_level
        WHEN 0 THEN 'Unspecified'
        WHEN 1 THEN 'ReadUncommitted'
        WHEN 2 THEN 'ReadCommitted'
        WHEN 3 THEN 'RepeatableRead'
        WHEN 4 THEN 'Serializable'
        WHEN 5 THEN 'Snapshot'
    END as isolation_level
FROM sys.dm_exec_sessions
WHERE session_id = @@SPID;

-- Snapshot isolation (SQL Server specific)
ALTER DATABASE MyDB SET ALLOW_SNAPSHOT_ISOLATION ON;
SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

-- Read committed snapshot (SQL Server specific)
ALTER DATABASE MyDB SET READ_COMMITTED_SNAPSHOT ON;
```

### MySQL Isolation Levels
```sql
-- MySQL InnoDB uses MVCC

-- Check current isolation level
SELECT @@transaction_isolation;

-- Set isolation level
SET SESSION TRANSACTION ISOLATION LEVEL REPEATABLE READ;

-- Set global isolation level
SET GLOBAL TRANSACTION ISOLATION LEVEL READ COMMITTED;

-- Transaction-specific isolation
START TRANSACTION WITH CONSISTENT SNAPSHOT;
-- Operations
COMMIT;
```

## Choosing the Right Isolation Level

### Decision Matrix
| Use Case | Recommended Level | Rationale |
|----------|------------------|-----------|
| Web Applications | Read Committed | Balance of consistency and performance |
| Financial Systems | Serializable | Maximum data integrity |
| Reporting | Read Uncommitted | Performance over consistency |
| Batch Processing | Repeatable Read | Consistent data throughout process |
| Real-time Analytics | Read Committed | Recent data with good performance |

### Application-Specific Examples

#### E-commerce Order Processing
```sql
-- Use Repeatable Read for order processing
BEGIN ISOLATION LEVEL REPEATABLE READ;
    -- Check inventory
    SELECT quantity FROM inventory WHERE product_id = 123;
    
    -- Reserve inventory
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
    
    -- Create order
    INSERT INTO orders (customer_id, product_id, quantity) VALUES (456, 123, 1);
COMMIT;
```

#### Financial Transfer
```sql
-- Use Serializable for financial transfers
BEGIN ISOLATION LEVEL SERIALIZABLE;
    -- Check source account balance
    SELECT balance FROM accounts WHERE account_id = 1;
    
    -- Perform transfer
    UPDATE accounts SET balance = balance - 100 WHERE account_id = 1;
    UPDATE accounts SET balance = balance + 100 WHERE account_id = 2;
    
    -- Log transaction
    INSERT INTO transaction_log (from_account, to_account, amount) VALUES (1, 2, 100);
COMMIT;
```

#### Reporting Query
```sql
-- Use Read Uncommitted for performance-critical reports
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;
SELECT 
    category,
    COUNT(*) as product_count,
    AVG(price) as avg_price
FROM products
GROUP BY category;
```

## Performance Implications

### Lock Duration and Scope
```sql
-- Read Committed: Minimal locking
BEGIN; -- Locks acquired and released quickly
    SELECT * FROM products WHERE price > 100;
COMMIT;

-- Repeatable Read: Extended locking
BEGIN; -- Locks held until commit
    SELECT * FROM products WHERE price > 100;
    -- ... long-running operations
COMMIT; -- Locks released here
```

### Deadlock Potential
```sql
-- Higher isolation levels increase deadlock risk
-- Transaction 1
BEGIN ISOLATION LEVEL REPEATABLE READ;
    SELECT * FROM table_a WHERE id = 1;
    SELECT * FROM table_b WHERE id = 1; -- May deadlock

-- Transaction 2 (concurrent)
BEGIN ISOLATION LEVEL REPEATABLE READ;
    SELECT * FROM table_b WHERE id = 1;
    SELECT * FROM table_a WHERE id = 1; -- May deadlock
```

### Performance Monitoring
```sql
-- PostgreSQL: Monitor lock waits
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- SQL Server: Monitor blocking
SELECT 
    r.session_id,
    r.wait_type,
    r.wait_time,
    r.blocking_session_id,
    s.program_name,
    t.text
FROM sys.dm_exec_requests r
LEFT JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
WHERE r.blocking_session_id <> 0;
```

## Best Practices

### 1. Use the Lowest Appropriate Level
```sql
-- Don't use SERIALIZABLE when READ COMMITTED is sufficient
-- Good for most web applications
BEGIN ISOLATION LEVEL READ COMMITTED;
    SELECT * FROM products WHERE category = 'electronics';
COMMIT;
```

### 2. Keep Transactions Short
```sql
-- Good: Short transaction
BEGIN;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;

-- Avoid: Long-running transaction
BEGIN;
    SELECT * FROM large_table; -- Long operation
    -- ... user interaction
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;
```

### 3. Handle Isolation-Related Errors
```python
import psycopg2

def transfer_money(from_account, to_account, amount):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            with psycopg2.connect(DATABASE_URL) as conn:
                with conn.cursor() as cur:
                    cur.execute("BEGIN ISOLATION LEVEL REPEATABLE READ")
                    
                    # Check balance
                    cur.execute("SELECT balance FROM accounts WHERE id = %s", (from_account,))
                    balance = cur.fetchone()[0]
                    
                    if balance < amount:
                        raise ValueError("Insufficient funds")
                    
                    # Perform transfer
                    cur.execute("UPDATE accounts SET balance = balance - %s WHERE id = %s", 
                               (amount, from_account))
                    cur.execute("UPDATE accounts SET balance = balance + %s WHERE id = %s", 
                               (amount, to_account))
                    
                    cur.execute("COMMIT")
                    return True
                    
        except psycopg2.extensions.TransactionRollbackError:
            # Serialization failure, retry
            if attempt == max_retries - 1:
                raise
            continue
        except Exception:
            raise
    
    return False
```

### 4. Monitor and Tune
```sql
-- Regular monitoring of lock contention
-- Adjust isolation levels based on actual needs
-- Use database-specific optimizations (MVCC, snapshot isolation)
```

### 5. Application Design Considerations
```python
# Design for eventual consistency where possible
# Use optimistic locking for better performance
def update_product_optimistic(product_id, new_price, expected_version):
    with database.transaction():
        result = database.execute("""
            UPDATE products 
            SET price = %s, version = version + 1 
            WHERE product_id = %s AND version = %s
        """, (new_price, product_id, expected_version))
        
        if result.rowcount == 0:
            raise OptimisticLockException("Product was modified by another transaction")
```

## Common Patterns

### Read-Heavy Workloads
```sql
-- Use READ COMMITTED or READ UNCOMMITTED for reports
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
SELECT 
    DATE_TRUNC('month', order_date) as month,
    SUM(total_amount) as monthly_revenue
FROM orders
WHERE order_date >= '2024-01-01'
GROUP BY DATE_TRUNC('month', order_date);
```

### Write-Heavy Workloads
```sql
-- Use appropriate isolation with retry logic
-- Keep transactions short and focused
BEGIN ISOLATION LEVEL READ COMMITTED;
    INSERT INTO audit_log (action, user_id, timestamp) 
    VALUES ('login', 123, CURRENT_TIMESTAMP);
COMMIT;
```

### Mixed Workloads
```sql
-- Use different isolation levels for different operations
-- Read operations: READ COMMITTED
-- Critical updates: REPEATABLE READ or SERIALIZABLE
-- Bulk operations: READ UNCOMMITTED (if acceptable)
```

## 🚀 Next Steps

After mastering isolation levels:
- [Locking Strategies](./locking-strategies.md) - Understand different locking approaches
- [Deadlock Management](./deadlock-management.md) - Handle concurrency conflicts
- [Performance Optimization](../optimization/README.md) - Optimize transaction performance

## 📝 Practice Exercises

1. Implement a banking system with appropriate isolation levels
2. Create a inventory management system handling concurrent updates
3. Design a reporting system balancing consistency and performance
4. Build a booking system preventing double-bookings
5. Implement optimistic locking for high-concurrency scenarios

Remember: Isolation levels are about balancing data consistency with performance. Choose the minimum level that meets your consistency requirements!
