# Locking Strategies

Locking strategies control how databases manage concurrent access to data. Understanding different locking approaches is essential for designing systems that balance data consistency with performance in multi-user environments.

## 📋 Table of Contents

1. [Locking Fundamentals](#locking-fundamentals)
2. [Pessimistic Locking](#pessimistic-locking)
3. [Optimistic Locking](#optimistic-locking)
4. [Lock Types and Granularity](#lock-types-and-granularity)
5. [Database-Specific Implementations](#database-specific-implementations)
6. [Performance Considerations](#performance-considerations)
7. [Best Practices](#best-practices)

## Locking Fundamentals

### What is Locking?
Locking is a mechanism to control concurrent access to database resources, ensuring data consistency and preventing conflicts between simultaneous transactions.

### Lock Purposes
- **Data Integrity**: Prevent inconsistent updates
- **Isolation**: Maintain transaction isolation levels
- **Consistency**: Ensure business rules are enforced
- **Atomicity**: Support all-or-nothing operations

### Lock Lifecycle
```
Request Lock → Acquire Lock → Hold Lock → Release Lock
     ↓             ↓            ↓           ↓
   Queue if     Use Resource  Block Others  Allow Others
   Unavailable
```

## Pessimistic Locking

### Concept
Acquire locks before accessing data, assuming conflicts will occur.

### Shared Locks (Read Locks)
```sql
-- PostgreSQL: Explicit shared lock
BEGIN;
    SELECT * FROM products WHERE product_id = 123 FOR SHARE;
    -- Other transactions can read but not modify
    -- ... business logic
COMMIT;

-- SQL Server: Shared lock hint
SELECT * FROM products WITH (HOLDLOCK, ROWLOCK) 
WHERE product_id = 123;

-- MySQL: Shared lock
START TRANSACTION;
    SELECT * FROM products WHERE product_id = 123 LOCK IN SHARE MODE;
COMMIT;
```

### Exclusive Locks (Write Locks)
```sql
-- PostgreSQL: Exclusive lock for update
BEGIN;
    SELECT * FROM inventory WHERE product_id = 123 FOR UPDATE;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;

-- SQL Server: Exclusive lock hint
SELECT * FROM inventory WITH (UPDLOCK, ROWLOCK) 
WHERE product_id = 123;

-- MySQL: Exclusive lock
START TRANSACTION;
    SELECT * FROM inventory WHERE product_id = 123 FOR UPDATE;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;
```

### Lock Escalation Prevention
```sql
-- PostgreSQL: Row-level locking
BEGIN;
    SELECT * FROM orders WHERE customer_id = 123 FOR UPDATE;
    -- Locks only matching rows, not the entire table
COMMIT;

-- SQL Server: Control lock escalation
ALTER TABLE orders SET (LOCK_ESCALATION = DISABLE);

-- Or use hints to maintain row-level locks
SELECT * FROM orders WITH (ROWLOCK) WHERE customer_id = 123;
```

### Pessimistic Locking Examples

#### Inventory Management
```sql
-- Prevent overselling with pessimistic locking
BEGIN;
    -- Lock the inventory record
    SELECT quantity FROM inventory 
    WHERE product_id = 123 FOR UPDATE;
    
    -- Check availability
    IF quantity >= requested_quantity THEN
        -- Update inventory
        UPDATE inventory 
        SET quantity = quantity - requested_quantity 
        WHERE product_id = 123;
        
        -- Create order
        INSERT INTO orders (customer_id, product_id, quantity) 
        VALUES (456, 123, requested_quantity);
    ELSE
        RAISE EXCEPTION 'Insufficient inventory';
    END IF;
COMMIT;
```

#### Seat Reservation System
```sql
-- Prevent double booking
BEGIN;
    -- Lock specific seats
    SELECT seat_number FROM seats 
    WHERE event_id = 789 AND seat_number IN ('A1', 'A2') 
    FOR UPDATE;
    
    -- Check availability
    SELECT COUNT(*) FROM reservations 
    WHERE event_id = 789 AND seat_number IN ('A1', 'A2');
    
    IF count = 0 THEN
        -- Reserve seats
        INSERT INTO reservations (event_id, seat_number, customer_id) 
        VALUES (789, 'A1', 123), (789, 'A2', 123);
    ELSE
        RAISE EXCEPTION 'Seats already reserved';
    END IF;
COMMIT;
```

## Optimistic Locking

### Concept
Assume conflicts are rare; check for conflicts before committing changes.

### Version-Based Optimistic Locking
```sql
-- Add version column to table
ALTER TABLE products ADD COLUMN version INTEGER DEFAULT 1;

-- Application logic for optimistic locking
-- 1. Read data with version
SELECT product_id, name, price, version 
FROM products WHERE product_id = 123;

-- 2. Update with version check
UPDATE products 
SET name = 'Updated Name', 
    price = 99.99, 
    version = version + 1
WHERE product_id = 123 AND version = 1; -- Original version

-- 3. Check if update succeeded
IF ROW_COUNT = 0 THEN
    RAISE EXCEPTION 'Product was modified by another transaction';
END IF;
```

### Timestamp-Based Optimistic Locking
```sql
-- Add timestamp column
ALTER TABLE products ADD COLUMN last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Update trigger to maintain timestamp
CREATE OR REPLACE FUNCTION update_last_modified()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_modified = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_products_timestamp
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_last_modified();

-- Application logic
-- 1. Read with timestamp
SELECT product_id, name, price, last_modified 
FROM products WHERE product_id = 123;

-- 2. Update with timestamp check
UPDATE products 
SET name = 'Updated Name', price = 99.99
WHERE product_id = 123 AND last_modified = '2024-01-01 10:00:00';
```

### Optimistic Locking Implementation Examples

#### Python with SQLAlchemy
```python
from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

Base = declarative_base()

class Product(Base):
    __tablename__ = 'products'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100))
    price = Column(Integer)  # Price in cents
    version = Column(Integer, default=1)

def update_product_optimistic(session, product_id, new_name, new_price):
    # Read current version
    product = session.query(Product).filter_by(id=product_id).first()
    if not product:
        raise ValueError("Product not found")
    
    original_version = product.version
    
    # Attempt update with version check
    result = session.query(Product).filter_by(
        id=product_id, 
        version=original_version
    ).update({
        'name': new_name,
        'price': new_price,
        'version': original_version + 1
    })
    
    if result == 0:
        raise OptimisticLockException("Product was modified by another transaction")
    
    session.commit()
    return True

class OptimisticLockException(Exception):
    pass
```

#### Java with JPA
```java
@Entity
@Table(name = "products")
public class Product {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    private String name;
    private BigDecimal price;
    
    @Version
    private Integer version;
    
    // Getters and setters
}

@Service
public class ProductService {
    
    @Autowired
    private ProductRepository productRepository;
    
    @Transactional
    public Product updateProduct(Long id, String name, BigDecimal price) {
        Product product = productRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Product not found"));
        
        product.setName(name);
        product.setPrice(price);
        
        try {
            return productRepository.save(product);
        } catch (OptimisticLockingFailureException e) {
            throw new ConcurrentModificationException("Product was modified by another user");
        }
    }
}
```

## Lock Types and Granularity

### Lock Granularity Levels
```sql
-- Database level (rarely used)
-- Schema level
-- Table level
ALTER TABLE products ADD CONSTRAINT check_price CHECK (price > 0);

-- Page level (internal to database)
-- Row level (most common)
SELECT * FROM products WHERE product_id = 123 FOR UPDATE;

-- Column level (some databases)
-- PostgreSQL: Column-level permissions (not locks)
GRANT SELECT (name, price) ON products TO app_user;
```

### Lock Compatibility Matrix
```
           | Shared | Exclusive | Update
-----------|--------|-----------|--------
Shared     |   ✓    |     ✗     |   ✓
Exclusive  |   ✗    |     ✗     |   ✗
Update     |   ✓    |     ✗     |   ✗
```

### Intent Locks
```sql
-- SQL Server: Intent locks prevent lock escalation conflicts
-- Intent Shared (IS) - intention to acquire shared locks
-- Intent Exclusive (IX) - intention to acquire exclusive locks
-- Shared with Intent Exclusive (SIX) - shared lock with intent to acquire exclusive locks

-- These are managed automatically by the database
SELECT * FROM products WITH (ROWLOCK) WHERE category = 'electronics';
```

## Database-Specific Implementations

### PostgreSQL Locking
```sql
-- Advisory locks (application-level)
SELECT pg_advisory_lock(12345);
-- ... critical section
SELECT pg_advisory_unlock(12345);

-- Row-level locks with different strengths
SELECT * FROM products WHERE product_id = 123 FOR UPDATE; -- Exclusive
SELECT * FROM products WHERE product_id = 123 FOR SHARE;  -- Shared
SELECT * FROM products WHERE product_id = 123 FOR NO KEY UPDATE; -- Allows foreign key references
SELECT * FROM products WHERE product_id = 123 FOR KEY SHARE; -- Allows updates to non-key columns

-- Table-level locks
LOCK TABLE products IN ACCESS EXCLUSIVE MODE;
LOCK TABLE products IN SHARE MODE;
```

### SQL Server Locking
```sql
-- Lock hints
SELECT * FROM products WITH (NOLOCK);        -- Read uncommitted
SELECT * FROM products WITH (HOLDLOCK);      -- Hold shared locks
SELECT * FROM products WITH (UPDLOCK);       -- Update locks
SELECT * FROM products WITH (XLOCK);         -- Exclusive locks
SELECT * FROM products WITH (ROWLOCK);       -- Force row-level locks
SELECT * FROM products WITH (PAGLOCK);       -- Force page-level locks
SELECT * FROM products WITH (TABLOCK);       -- Force table-level locks

-- Lock timeout
SET LOCK_TIMEOUT 5000; -- 5 seconds
SELECT * FROM products WHERE product_id = 123;
```

### MySQL Locking
```sql
-- Table-level locks
LOCK TABLES products READ;
LOCK TABLES products WRITE;
UNLOCK TABLES;

-- Row-level locks (InnoDB)
SELECT * FROM products WHERE product_id = 123 FOR UPDATE;
SELECT * FROM products WHERE product_id = 123 LOCK IN SHARE MODE;

-- Named locks (application-level)
SELECT GET_LOCK('my_lock', 10); -- Acquire lock with 10-second timeout
SELECT RELEASE_LOCK('my_lock');  -- Release lock
```

## Performance Considerations

### Lock Contention Monitoring
```sql
-- PostgreSQL: Monitor locks
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- SQL Server: Lock monitoring
SELECT 
    request_session_id,
    resource_type,
    resource_description,
    request_mode,
    request_status
FROM sys.dm_tran_locks
WHERE request_status = 'WAIT';
```

### Reducing Lock Contention
```sql
-- 1. Use appropriate isolation levels
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

-- 2. Keep transactions short
BEGIN;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;

-- 3. Access resources in consistent order
-- Always lock tables in alphabetical order to prevent deadlocks
BEGIN;
    LOCK TABLE customers IN SHARE MODE;
    LOCK TABLE orders IN SHARE MODE;
    -- ... operations
COMMIT;

-- 4. Use row-level locking when possible
SELECT * FROM products WHERE category = 'electronics' FOR UPDATE;
```

### Lock Escalation Management
```sql
-- SQL Server: Control lock escalation
ALTER TABLE large_table SET (LOCK_ESCALATION = DISABLE);

-- Use hints to maintain granular locks
UPDATE large_table WITH (ROWLOCK) 
SET status = 'processed' 
WHERE batch_id = 123;

-- PostgreSQL: Generally doesn't escalate locks automatically
-- Monitor lock memory usage
SELECT * FROM pg_locks;
```

## Best Practices

### 1. Choose the Right Strategy
```python
# Use pessimistic locking for:
# - High contention scenarios
# - Critical data integrity requirements
# - Short transactions

def reserve_seat_pessimistic(event_id, seat_number, customer_id):
    with database.transaction():
        # Lock the seat
        seat = database.execute(
            "SELECT * FROM seats WHERE event_id = %s AND seat_number = %s FOR UPDATE",
            (event_id, seat_number)
        ).fetchone()
        
        if seat['status'] != 'available':
            raise SeatUnavailableException()
        
        # Reserve the seat
        database.execute(
            "UPDATE seats SET status = 'reserved', customer_id = %s WHERE id = %s",
            (customer_id, seat['id'])
        )

# Use optimistic locking for:
# - Low contention scenarios
# - Long-running operations
# - Better scalability

def update_product_optimistic(product_id, updates):
    max_retries = 3
    for attempt in range(max_retries):
        try:
            with database.transaction():
                # Read current version
                product = database.execute(
                    "SELECT * FROM products WHERE id = %s",
                    (product_id,)
                ).fetchone()
                
                # Apply updates with version check
                result = database.execute(
                    "UPDATE products SET name = %s, price = %s, version = version + 1 "
                    "WHERE id = %s AND version = %s",
                    (updates['name'], updates['price'], product_id, product['version'])
                )
                
                if result.rowcount == 0:
                    raise OptimisticLockException()
                
                return True
                
        except OptimisticLockException:
            if attempt == max_retries - 1:
                raise ConcurrentModificationException()
            # Retry with exponential backoff
            time.sleep(0.1 * (2 ** attempt))
```

### 2. Minimize Lock Duration
```sql
-- Good: Short lock duration
BEGIN;
    SELECT quantity FROM inventory WHERE product_id = 123 FOR UPDATE;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;

-- Avoid: Long lock duration
BEGIN;
    SELECT quantity FROM inventory WHERE product_id = 123 FOR UPDATE;
    -- ... complex business logic
    -- ... external API calls
    -- ... user interaction
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;
```

### 3. Handle Lock Timeouts
```python
import time
import random

def execute_with_retry(operation, max_retries=3):
    for attempt in range(max_retries):
        try:
            return operation()
        except LockTimeoutException:
            if attempt == max_retries - 1:
                raise
            # Exponential backoff with jitter
            delay = (2 ** attempt) + random.uniform(0, 1)
            time.sleep(delay)
        except DeadlockException:
            if attempt == max_retries - 1:
                raise
            # Immediate retry for deadlocks
            time.sleep(0.1)
```

### 4. Monitor and Tune
```sql
-- Regular monitoring of lock waits and deadlocks
-- Adjust lock timeout settings
SET LOCK_TIMEOUT 30000; -- 30 seconds

-- Analyze lock contention patterns
-- Consider schema changes to reduce contention
```

### 5. Design for Concurrency
```sql
-- Use natural keys that distribute load
-- Partition large tables to reduce contention
-- Consider queue-based architectures for high-contention operations

-- Example: Distribute load across multiple counters
CREATE TABLE distributed_counters (
    counter_id INTEGER,
    shard_id INTEGER,
    count_value BIGINT DEFAULT 0,
    PRIMARY KEY (counter_id, shard_id)
);

-- Increment random shard
UPDATE distributed_counters 
SET count_value = count_value + 1 
WHERE counter_id = 1 AND shard_id = (RANDOM() * 10)::INTEGER;

-- Get total count
SELECT SUM(count_value) FROM distributed_counters WHERE counter_id = 1;
```

## Common Patterns

### Producer-Consumer with Locking
```sql
-- Producer: Add work items
INSERT INTO work_queue (task_data, status, created_at) 
VALUES ('task data', 'pending', CURRENT_TIMESTAMP);

-- Consumer: Process work items with locking
BEGIN;
    -- Get next available work item
    SELECT id, task_data FROM work_queue 
    WHERE status = 'pending' 
    ORDER BY created_at 
    LIMIT 1 
    FOR UPDATE SKIP LOCKED; -- PostgreSQL: Skip locked rows
    
    -- Mark as processing
    UPDATE work_queue SET status = 'processing' WHERE id = work_item_id;
COMMIT;

-- Process the work item...

-- Mark as completed
UPDATE work_queue SET status = 'completed' WHERE id = work_item_id;
```

### Distributed Locking
```python
# Redis-based distributed locking
import redis
import time
import uuid

class DistributedLock:
    def __init__(self, redis_client, key, timeout=10):
        self.redis = redis_client
        self.key = f"lock:{key}"
        self.timeout = timeout
        self.identifier = str(uuid.uuid4())
    
    def acquire(self):
        end_time = time.time() + self.timeout
        while time.time() < end_time:
            if self.redis.set(self.key, self.identifier, nx=True, ex=self.timeout):
                return True
            time.sleep(0.001)
        return False
    
    def release(self):
        # Use Lua script for atomic check-and-delete
        script = """
        if redis.call("GET", KEYS[1]) == ARGV[1] then
            return redis.call("DEL", KEYS[1])
        else
            return 0
        end
        """
        return self.redis.eval(script, 1, self.key, self.identifier)

# Usage
redis_client = redis.Redis(host='localhost', port=6379)
lock = DistributedLock(redis_client, 'critical_section')

if lock.acquire():
    try:
        # Critical section
        process_critical_operation()
    finally:
        lock.release()
else:
    print("Could not acquire lock")
```

## 🚀 Next Steps

After mastering locking strategies:
- [Deadlock Management](./deadlock-management.md) - Handle lock conflicts
- [Transaction Log Management](./log-management.md) - Understand transaction logging
- [Performance Optimization](../optimization/README.md) - Optimize locking performance

## 📝 Practice Exercises

1. Implement a ticket booking system with pessimistic locking
2. Create an inventory management system with optimistic locking
3. Build a distributed counter using lock-free techniques
4. Design a work queue system with proper locking
5. Implement a banking system handling concurrent transfers

Remember: The choice between pessimistic and optimistic locking depends on your specific use case, contention levels, and performance requirements!
