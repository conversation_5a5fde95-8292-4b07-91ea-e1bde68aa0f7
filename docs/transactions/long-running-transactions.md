# Long-Running Transactions

Long-running transactions can significantly impact database performance, cause lock contention, and prevent log cleanup. This guide covers strategies for handling complex operations that require extended transaction times while minimizing their impact on system performance.

## 📋 Table of Contents

1. [Understanding Long-Running Transactions](#understanding-long-running-transactions)
2. [Impact on Database Performance](#impact-on-database-performance)
3. [Chunking Strategies](#chunking-strategies)
4. [Cursor-Based Processing](#cursor-based-processing)
5. [Batch Processing Patterns](#batch-processing-patterns)
6. [Monitoring and Management](#monitoring-and-management)
7. [Best Practices](#best-practices)

## Understanding Long-Running Transactions

### What Constitutes a Long-Running Transaction?
A long-running transaction is any transaction that remains active for an extended period, typically:
- **OLTP Systems**: > 1-5 seconds
- **Batch Processing**: > 30 minutes
- **Data Migration**: > 1 hour
- **Reporting**: > 15 minutes

### Common Scenarios
```sql
-- Problematic: Large batch update
BEGIN;
UPDATE products SET price = price * 1.1 WHERE category = 'electronics';
-- This could affect millions of rows and run for hours
COMMIT;

-- Problematic: Complex data migration
BEGIN;
INSERT INTO new_table 
SELECT col1, col2, complex_calculation(col3)
FROM old_table 
WHERE migration_date IS NULL;
-- Could process millions of records
COMMIT;

-- Problematic: Report generation with user interaction
BEGIN;
SELECT * FROM orders WHERE order_date >= '2024-01-01';
-- User reviews data, makes decisions...
-- Transaction remains open during user interaction
COMMIT;
```

## Impact on Database Performance

### Lock Contention
```sql
-- Long-running transaction holds locks
BEGIN;
UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
-- ... long processing time
-- Other transactions wait for locks

-- Concurrent transaction blocked
BEGIN;
SELECT quantity FROM inventory WHERE product_id = 123 FOR UPDATE;
-- Waits for first transaction to complete
```

### Log Growth
```sql
-- PostgreSQL: Check WAL retention due to long transactions
SELECT 
    pid,
    now() - xact_start as duration,
    state,
    query
FROM pg_stat_activity
WHERE xact_start IS NOT NULL
ORDER BY xact_start;

-- SQL Server: Check log space usage
SELECT 
    name,
    log_reuse_wait_desc,
    size * 8 / 1024 as size_mb
FROM sys.databases
WHERE log_reuse_wait_desc <> 'NOTHING';
```

### MVCC Overhead
```sql
-- Long transactions prevent cleanup of old row versions
-- PostgreSQL: Check table bloat
SELECT 
    schemaname,
    tablename,
    n_dead_tup,
    n_live_tup,
    (n_dead_tup::float / GREATEST(n_live_tup, 1)) * 100 as dead_tuple_percent
FROM pg_stat_user_tables
WHERE n_dead_tup > 1000
ORDER BY dead_tuple_percent DESC;
```

## Chunking Strategies

### Basic Chunking Pattern
```sql
-- Instead of processing all records at once
-- Bad:
UPDATE large_table SET status = 'processed' WHERE status = 'pending';

-- Good: Process in chunks
DO $$
DECLARE
    chunk_size INTEGER := 1000;
    rows_affected INTEGER;
BEGIN
    LOOP
        UPDATE large_table 
        SET status = 'processed' 
        WHERE ctid IN (
            SELECT ctid FROM large_table 
            WHERE status = 'pending' 
            LIMIT chunk_size
        );
        
        GET DIAGNOSTICS rows_affected = ROW_COUNT;
        
        -- Commit chunk
        COMMIT;
        
        -- Exit if no more rows
        EXIT WHEN rows_affected = 0;
        
        -- Brief pause to allow other transactions
        PERFORM pg_sleep(0.1);
    END LOOP;
END $$;
```

### Adaptive Chunking
```python
# Python implementation of adaptive chunking
import psycopg2
import time

class AdaptiveChunker:
    def __init__(self, connection, initial_chunk_size=1000):
        self.conn = connection
        self.chunk_size = initial_chunk_size
        self.target_duration = 5.0  # Target 5 seconds per chunk
        self.min_chunk_size = 100
        self.max_chunk_size = 10000
    
    def process_large_update(self, table_name, set_clause, where_clause):
        """Process large update with adaptive chunking"""
        total_processed = 0
        
        while True:
            start_time = time.time()
            
            with self.conn.cursor() as cur:
                # Process chunk
                cur.execute(f"""
                    UPDATE {table_name} 
                    SET {set_clause}
                    WHERE ctid IN (
                        SELECT ctid FROM {table_name} 
                        WHERE {where_clause}
                        LIMIT %s
                    )
                """, (self.chunk_size,))
                
                rows_affected = cur.rowcount
                self.conn.commit()
            
            # Calculate processing time
            duration = time.time() - start_time
            total_processed += rows_affected
            
            # Exit if no more rows
            if rows_affected == 0:
                break
            
            # Adjust chunk size based on performance
            self._adjust_chunk_size(duration, rows_affected)
            
            # Brief pause
            time.sleep(0.1)
            
            print(f"Processed {rows_affected} rows in {duration:.2f}s, "
                  f"total: {total_processed}, next chunk size: {self.chunk_size}")
        
        return total_processed
    
    def _adjust_chunk_size(self, duration, rows_processed):
        """Adjust chunk size based on performance"""
        if duration > 0:
            # Calculate optimal chunk size
            optimal_size = int(self.chunk_size * (self.target_duration / duration))
            
            # Apply bounds
            optimal_size = max(self.min_chunk_size, 
                             min(self.max_chunk_size, optimal_size))
            
            # Gradual adjustment to avoid oscillation
            if optimal_size > self.chunk_size:
                self.chunk_size = min(optimal_size, int(self.chunk_size * 1.5))
            elif optimal_size < self.chunk_size:
                self.chunk_size = max(optimal_size, int(self.chunk_size * 0.8))

# Usage
chunker = AdaptiveChunker(connection)
total_rows = chunker.process_large_update(
    table_name='products',
    set_clause='price = price * 1.1',
    where_clause='category = \'electronics\' AND price_updated = false'
)
```

### Range-Based Chunking
```sql
-- Process data in date ranges
DO $$
DECLARE
    start_date DATE := '2024-01-01';
    end_date DATE := '2024-12-31';
    current_date DATE := start_date;
    chunk_days INTEGER := 7; -- Process one week at a time
BEGIN
    WHILE current_date <= end_date LOOP
        -- Process chunk
        UPDATE orders 
        SET status = 'archived'
        WHERE order_date >= current_date 
        AND order_date < current_date + chunk_days
        AND status = 'completed';
        
        -- Log progress
        RAISE NOTICE 'Processed orders from % to %', 
            current_date, current_date + chunk_days - 1;
        
        -- Commit chunk
        COMMIT;
        
        -- Move to next chunk
        current_date := current_date + chunk_days;
        
        -- Brief pause
        PERFORM pg_sleep(1);
    END LOOP;
END $$;
```

## Cursor-Based Processing

### Server-Side Cursors
```sql
-- PostgreSQL: Process large result set with cursor
BEGIN;

DECLARE large_data_cursor CURSOR FOR
    SELECT customer_id, order_date, total_amount
    FROM orders
    WHERE order_date >= '2024-01-01'
    ORDER BY order_date;

-- Process in batches
DO $$
DECLARE
    rec RECORD;
    batch_count INTEGER := 0;
    batch_size INTEGER := 1000;
BEGIN
    LOOP
        FETCH large_data_cursor INTO rec;
        EXIT WHEN NOT FOUND;
        
        -- Process record
        INSERT INTO order_summary (customer_id, order_date, amount)
        VALUES (rec.customer_id, rec.order_date, rec.total_amount);
        
        batch_count := batch_count + 1;
        
        -- Commit every batch_size records
        IF batch_count % batch_size = 0 THEN
            COMMIT;
            RAISE NOTICE 'Processed % records', batch_count;
        END IF;
    END LOOP;
    
    -- Final commit
    COMMIT;
END $$;

CLOSE large_data_cursor;
COMMIT;
```

### Application-Level Cursors
```python
# Python: Streaming large result sets
def process_large_dataset(connection, query, batch_size=1000):
    """Process large dataset using server-side cursor"""
    
    with connection.cursor(name='large_dataset_cursor') as cursor:
        cursor.execute(query)
        
        processed_count = 0
        
        while True:
            # Fetch batch
            records = cursor.fetchmany(batch_size)
            if not records:
                break
            
            # Process batch in separate transaction
            with connection.cursor() as update_cursor:
                for record in records:
                    # Process individual record
                    process_record(update_cursor, record)
                
                # Commit batch
                connection.commit()
                processed_count += len(records)
                
                print(f"Processed {processed_count} records")
                
                # Brief pause to allow other operations
                time.sleep(0.1)
        
        return processed_count

def process_record(cursor, record):
    """Process individual record"""
    cursor.execute("""
        INSERT INTO processed_orders (customer_id, order_date, amount)
        VALUES (%s, %s, %s)
    """, (record[0], record[1], record[2]))

# Usage
query = """
    SELECT customer_id, order_date, total_amount
    FROM orders
    WHERE order_date >= '2024-01-01'
    ORDER BY order_date
"""

total_processed = process_large_dataset(connection, query)
print(f"Total records processed: {total_processed}")
```

## Batch Processing Patterns

### Queue-Based Processing
```python
# Implement queue-based batch processing
import redis
import json
from datetime import datetime

class BatchProcessor:
    def __init__(self, db_connection, redis_connection):
        self.db = db_connection
        self.redis = redis_connection
        self.queue_name = 'batch_processing_queue'
        self.batch_size = 100
        self.max_wait_time = 30  # seconds
    
    def queue_item(self, item_data):
        """Add item to processing queue"""
        item = {
            'data': item_data,
            'queued_at': datetime.now().isoformat()
        }
        self.redis.lpush(self.queue_name, json.dumps(item))
    
    def process_batch(self):
        """Process a batch of items from queue"""
        batch = []
        start_time = time.time()
        
        # Collect batch
        while (len(batch) < self.batch_size and 
               time.time() - start_time < self.max_wait_time):
            
            item_json = self.redis.brpop(self.queue_name, timeout=1)
            if item_json:
                item = json.loads(item_json[1])
                batch.append(item)
            else:
                break
        
        if not batch:
            return 0
        
        # Process batch in single transaction
        with self.db.cursor() as cur:
            for item in batch:
                self._process_item(cur, item['data'])
            
            self.db.commit()
        
        return len(batch)
    
    def _process_item(self, cursor, item_data):
        """Process individual item"""
        cursor.execute("""
            INSERT INTO processed_items (data, processed_at)
            VALUES (%s, %s)
        """, (json.dumps(item_data), datetime.now()))
    
    def run_processor(self):
        """Run continuous batch processor"""
        while True:
            try:
                processed = self.process_batch()
                if processed > 0:
                    print(f"Processed batch of {processed} items")
                else:
                    time.sleep(1)  # No items, wait a bit
            except Exception as e:
                print(f"Error processing batch: {e}")
                time.sleep(5)  # Error recovery delay

# Usage
processor = BatchProcessor(db_connection, redis_connection)

# Queue items for processing
for i in range(1000):
    processor.queue_item({'id': i, 'value': f'data_{i}'})

# Run processor
processor.run_processor()
```

### Parallel Processing
```python
# Parallel processing of large datasets
import multiprocessing
from concurrent.futures import ProcessPoolExecutor
import psycopg2

def process_chunk(connection_params, start_id, end_id):
    """Process a chunk of data in separate process"""
    conn = psycopg2.connect(**connection_params)
    
    try:
        with conn.cursor() as cur:
            # Process chunk
            cur.execute("""
                UPDATE large_table 
                SET processed = true, processed_at = NOW()
                WHERE id >= %s AND id < %s AND processed = false
            """, (start_id, end_id))
            
            rows_affected = cur.rowcount
            conn.commit()
            
            return rows_affected
    
    finally:
        conn.close()

def parallel_batch_processing(connection_params, total_records, chunk_size=10000):
    """Process large dataset using parallel workers"""
    
    # Calculate chunks
    chunks = []
    for start_id in range(1, total_records + 1, chunk_size):
        end_id = min(start_id + chunk_size, total_records + 1)
        chunks.append((start_id, end_id))
    
    # Process chunks in parallel
    max_workers = min(4, multiprocessing.cpu_count())
    
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        
        for start_id, end_id in chunks:
            future = executor.submit(
                process_chunk, connection_params, start_id, end_id
            )
            futures.append(future)
        
        # Collect results
        total_processed = 0
        for i, future in enumerate(futures):
            try:
                rows_processed = future.result(timeout=300)  # 5 minute timeout
                total_processed += rows_processed
                print(f"Chunk {i+1}/{len(chunks)}: {rows_processed} rows processed")
            except Exception as e:
                print(f"Chunk {i+1} failed: {e}")
    
    return total_processed

# Usage
connection_params = {
    'host': 'localhost',
    'database': 'myapp',
    'user': 'user',
    'password': 'password'
}

total_processed = parallel_batch_processing(connection_params, 1000000)
print(f"Total processed: {total_processed}")
```

## Monitoring and Management

### Identifying Long-Running Transactions
```sql
-- PostgreSQL: Find long-running transactions
SELECT 
    pid,
    now() - xact_start as duration,
    now() - query_start as query_duration,
    state,
    query,
    client_addr,
    application_name
FROM pg_stat_activity
WHERE xact_start IS NOT NULL
AND now() - xact_start > interval '5 minutes'
ORDER BY xact_start;

-- SQL Server: Find long-running transactions
SELECT 
    s.session_id,
    s.login_name,
    s.program_name,
    t.transaction_begin_time,
    DATEDIFF(second, t.transaction_begin_time, GETDATE()) as duration_seconds,
    r.command,
    r.percent_complete,
    r.estimated_completion_time
FROM sys.dm_tran_active_transactions t
JOIN sys.dm_tran_session_transactions st ON t.transaction_id = st.transaction_id
JOIN sys.dm_exec_sessions s ON st.session_id = s.session_id
LEFT JOIN sys.dm_exec_requests r ON s.session_id = r.session_id
WHERE DATEDIFF(second, t.transaction_begin_time, GETDATE()) > 300 -- > 5 minutes
ORDER BY t.transaction_begin_time;

-- MySQL: Find long-running transactions
SELECT 
    id,
    user,
    host,
    db,
    command,
    time,
    state,
    info
FROM information_schema.PROCESSLIST
WHERE command != 'Sleep'
AND time > 300 -- > 5 minutes
ORDER BY time DESC;
```

### Transaction Monitoring Script
```python
# Automated monitoring for long-running transactions
import psycopg2
import time
import smtplib
from email.mime.text import MIMEText

class TransactionMonitor:
    def __init__(self, connection_params, alert_threshold_minutes=30):
        self.conn_params = connection_params
        self.alert_threshold = alert_threshold_minutes
        self.alerted_pids = set()
    
    def check_long_running_transactions(self):
        """Check for long-running transactions"""
        with psycopg2.connect(**self.conn_params) as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT 
                        pid,
                        extract(epoch from (now() - xact_start))/60 as duration_minutes,
                        state,
                        query,
                        client_addr,
                        application_name
                    FROM pg_stat_activity
                    WHERE xact_start IS NOT NULL
                    AND now() - xact_start > interval '%s minutes'
                    ORDER BY xact_start
                """, (self.alert_threshold,))
                
                long_transactions = cur.fetchall()
                
                for transaction in long_transactions:
                    pid, duration, state, query, client_addr, app_name = transaction
                    
                    if pid not in self.alerted_pids:
                        self._send_alert(pid, duration, state, query, client_addr, app_name)
                        self.alerted_pids.add(pid)
                
                # Clean up alerted PIDs that are no longer running
                current_pids = {t[0] for t in long_transactions}
                self.alerted_pids &= current_pids
                
                return long_transactions
    
    def _send_alert(self, pid, duration, state, query, client_addr, app_name):
        """Send alert for long-running transaction"""
        subject = f"Long-running transaction alert - PID {pid}"
        body = f"""
        Long-running transaction detected:
        
        PID: {pid}
        Duration: {duration:.1f} minutes
        State: {state}
        Client: {client_addr}
        Application: {app_name}
        Query: {query[:200]}...
        
        Please investigate and consider terminating if necessary.
        """
        
        print(f"ALERT: Long-running transaction PID {pid} ({duration:.1f} minutes)")
        # Send email alert here if configured
    
    def terminate_transaction(self, pid, force=False):
        """Terminate a long-running transaction"""
        with psycopg2.connect(**self.conn_params) as conn:
            with conn.cursor() as cur:
                if force:
                    cur.execute("SELECT pg_terminate_backend(%s)", (pid,))
                else:
                    cur.execute("SELECT pg_cancel_request(%s)", (pid,))
                
                result = cur.fetchone()[0]
                return result

# Usage
monitor = TransactionMonitor({
    'host': 'localhost',
    'database': 'myapp',
    'user': 'monitor_user',
    'password': 'password'
}, alert_threshold_minutes=15)

# Run monitoring loop
while True:
    long_transactions = monitor.check_long_running_transactions()
    if long_transactions:
        print(f"Found {len(long_transactions)} long-running transactions")
    
    time.sleep(60)  # Check every minute
```

## Best Practices

### 1. Design for Short Transactions
```python
# Good: Break large operations into smaller transactions
def migrate_user_data(user_ids, batch_size=100):
    """Migrate user data in small batches"""
    
    for i in range(0, len(user_ids), batch_size):
        batch = user_ids[i:i + batch_size]
        
        with database.transaction():
            for user_id in batch:
                # Migrate individual user
                migrate_single_user(user_id)
        
        # Brief pause between batches
        time.sleep(0.1)
        print(f"Migrated batch {i//batch_size + 1}")

# Avoid: Single large transaction
def migrate_all_users_bad(user_ids):
    """Don't do this - single large transaction"""
    with database.transaction():
        for user_id in user_ids:  # Could be millions
            migrate_single_user(user_id)
```

### 2. Use Savepoints for Complex Operations
```sql
-- Use savepoints to avoid full rollback
BEGIN;
    INSERT INTO audit_log (action) VALUES ('migration_start');
    
    -- Process each table with savepoints
    SAVEPOINT table1_migration;
    BEGIN
        -- Migrate table1
        INSERT INTO new_table1 SELECT * FROM old_table1;
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK TO SAVEPOINT table1_migration;
            INSERT INTO error_log (error, table_name) VALUES (SQLERRM, 'table1');
    END;
    
    SAVEPOINT table2_migration;
    BEGIN
        -- Migrate table2
        INSERT INTO new_table2 SELECT * FROM old_table2;
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK TO SAVEPOINT table2_migration;
            INSERT INTO error_log (error, table_name) VALUES (SQLERRM, 'table2');
    END;
    
    INSERT INTO audit_log (action) VALUES ('migration_complete');
COMMIT;
```

### 3. Implement Progress Tracking
```python
# Track progress for long operations
class ProgressTracker:
    def __init__(self, total_items, operation_name):
        self.total_items = total_items
        self.processed_items = 0
        self.operation_name = operation_name
        self.start_time = time.time()
    
    def update(self, items_processed):
        """Update progress"""
        self.processed_items += items_processed
        
        elapsed_time = time.time() - self.start_time
        progress_percent = (self.processed_items / self.total_items) * 100
        
        if elapsed_time > 0:
            rate = self.processed_items / elapsed_time
            eta_seconds = (self.total_items - self.processed_items) / rate
            eta_minutes = eta_seconds / 60
            
            print(f"{self.operation_name}: {progress_percent:.1f}% "
                  f"({self.processed_items}/{self.total_items}) "
                  f"Rate: {rate:.1f}/sec ETA: {eta_minutes:.1f}min")

# Usage
tracker = ProgressTracker(1000000, "Data Migration")

for batch in process_in_batches(data, batch_size=1000):
    process_batch(batch)
    tracker.update(len(batch))
```

### 4. Implement Circuit Breakers
```python
# Circuit breaker for batch operations
class CircuitBreaker:
    def __init__(self, failure_threshold=5, recovery_timeout=60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
    
    def call(self, func, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
            else:
                raise Exception("Circuit breaker is OPEN")
        
        try:
            result = func(*args, **kwargs)
            
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
            
            raise

# Usage in batch processing
circuit_breaker = CircuitBreaker()

def process_with_circuit_breaker(batch):
    """Process batch with circuit breaker protection"""
    return circuit_breaker.call(process_batch, batch)
```

## 🚀 Next Steps

After mastering long-running transaction management:
- [Performance Optimization](../optimization/README.md) - Optimize overall database performance
- [Monitoring](../monitoring/README.md) - Set up comprehensive monitoring
- [Backup and Recovery](../monitoring/backup-strategies.md) - Plan for recovery scenarios

## 📝 Practice Exercises

1. Implement chunked processing for a large data migration
2. Create a monitoring system for long-running transactions
3. Build a queue-based batch processing system
4. Design a parallel processing solution for bulk operations
5. Implement progress tracking and circuit breakers

Remember: The key to handling long-running operations is to break them into smaller, manageable chunks while maintaining data consistency and system performance!
