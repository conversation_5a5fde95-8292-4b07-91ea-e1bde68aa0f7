# Deadlock Management

Deadlocks occur when two or more transactions are waiting for each other to release locks, creating a circular dependency. Understanding deadlock detection, prevention, and resolution is crucial for maintaining database performance and reliability.

## 📋 Table of Contents

1. [Understanding Deadlocks](#understanding-deadlocks)
2. [Deadlock Detection](#deadlock-detection)
3. [Deadlock Prevention](#deadlock-prevention)
4. [Deadlock Resolution](#deadlock-resolution)
5. [Database-Specific Handling](#database-specific-handling)
6. [Monitoring and Analysis](#monitoring-and-analysis)
7. [Best Practices](#best-practices)

## Understanding Deadlocks

### What is a Deadlock?
A deadlock occurs when two or more transactions are permanently blocked, each waiting for the other to release a lock.

### Classic Deadlock Scenario
```sql
-- Transaction 1
BEGIN;
    UPDATE accounts SET balance = balance - 100 WHERE account_id = 1; -- Locks account 1
    -- ... some processing time
    UPDATE accounts SET balance = balance + 100 WHERE account_id = 2; -- Waits for account 2

-- Transaction 2 (concurrent)
BEGIN;
    UPDATE accounts SET balance = balance - 50 WHERE account_id = 2;  -- Locks account 2
    -- ... some processing time
    UPDATE accounts SET balance = balance + 50 WHERE account_id = 1;  -- Waits for account 1

-- Result: Deadlock! Each transaction waits for the other's lock
```

### Deadlock Conditions (Coffman Conditions)
1. **Mutual Exclusion**: Resources cannot be shared
2. **Hold and Wait**: Processes hold resources while waiting for others
3. **No Preemption**: Resources cannot be forcibly taken away
4. **Circular Wait**: Circular chain of processes waiting for resources

### Types of Deadlocks

#### Simple Deadlock (Two Transactions)
```sql
-- Transaction A
BEGIN;
    SELECT * FROM table1 WHERE id = 1 FOR UPDATE;  -- Lock 1
    SELECT * FROM table2 WHERE id = 1 FOR UPDATE;  -- Wait for Lock 2

-- Transaction B
BEGIN;
    SELECT * FROM table2 WHERE id = 1 FOR UPDATE;  -- Lock 2
    SELECT * FROM table1 WHERE id = 1 FOR UPDATE;  -- Wait for Lock 1
```

#### Complex Deadlock (Multiple Transactions)
```sql
-- Transaction A: Locks R1, waits for R2
-- Transaction B: Locks R2, waits for R3
-- Transaction C: Locks R3, waits for R1
-- Result: Circular dependency A→B→C→A
```

#### Conversion Deadlock
```sql
-- Transaction 1
BEGIN;
    SELECT * FROM products WHERE id = 123 FOR SHARE;     -- Shared lock
    -- ... later in transaction
    UPDATE products SET price = 100 WHERE id = 123;     -- Needs exclusive lock

-- Transaction 2
BEGIN;
    SELECT * FROM products WHERE id = 123 FOR SHARE;     -- Shared lock
    -- ... later in transaction
    UPDATE products SET price = 150 WHERE id = 123;     -- Needs exclusive lock

-- Both transactions hold shared locks and wait for exclusive locks
```

## Deadlock Detection

### Automatic Detection
Most databases automatically detect deadlocks using wait-for graphs or timeout mechanisms.

```sql
-- PostgreSQL: Deadlock detection
-- Automatically detects deadlocks and aborts one transaction
-- Default deadlock_timeout = 1s

-- SQL Server: Deadlock detection
-- Uses lock monitor to detect deadlocks every 5 seconds
-- Chooses deadlock victim based on transaction cost

-- MySQL: Deadlock detection
-- InnoDB automatically detects deadlocks
-- Rolls back the transaction with fewer row locks
```

### Manual Detection Queries

#### PostgreSQL Deadlock Monitoring
```sql
-- Check for blocking queries
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process,
    blocked_activity.application_name AS blocked_application,
    blocking_activity.application_name AS blocking_application
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
    ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;
```

#### SQL Server Deadlock Monitoring
```sql
-- Enable deadlock monitoring
DBCC TRACEON(1222, -1); -- Global trace flag for deadlock information

-- Query current blocking
SELECT 
    r.session_id,
    r.wait_type,
    r.wait_time,
    r.blocking_session_id,
    s.program_name,
    s.host_name,
    s.login_name,
    t.text as sql_text
FROM sys.dm_exec_requests r
LEFT JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
WHERE r.blocking_session_id <> 0;

-- Deadlock graph (requires extended events)
CREATE EVENT SESSION deadlock_monitor ON SERVER
ADD EVENT sqlserver.xml_deadlock_report
ADD TARGET package0.event_file(SET filename='C:\temp\deadlocks.xel');

ALTER EVENT SESSION deadlock_monitor ON SERVER STATE = START;
```

## Deadlock Prevention

### 1. Consistent Resource Ordering
```sql
-- Always access tables/rows in the same order
-- Good: Consistent ordering
BEGIN;
    UPDATE accounts SET balance = balance - amount WHERE account_id = LEAST(from_account, to_account);
    UPDATE accounts SET balance = balance + amount WHERE account_id = GREATEST(from_account, to_account);
COMMIT;

-- Bad: Inconsistent ordering
BEGIN;
    UPDATE accounts SET balance = balance - amount WHERE account_id = from_account;
    UPDATE accounts SET balance = balance + amount WHERE account_id = to_account;
COMMIT;
```

### 2. Timeout-Based Prevention
```sql
-- PostgreSQL: Set lock timeout
SET lock_timeout = '5s';
BEGIN;
    SELECT * FROM products WHERE id = 123 FOR UPDATE;
    -- Will timeout after 5 seconds if lock cannot be acquired
COMMIT;

-- SQL Server: Set lock timeout
SET LOCK_TIMEOUT 5000; -- 5 seconds
BEGIN TRANSACTION;
    SELECT * FROM products WITH (UPDLOCK) WHERE id = 123;
COMMIT;

-- MySQL: Set lock wait timeout
SET innodb_lock_wait_timeout = 5;
START TRANSACTION;
    SELECT * FROM products WHERE id = 123 FOR UPDATE;
COMMIT;
```

### 3. Reduce Transaction Scope
```sql
-- Good: Minimal transaction scope
BEGIN;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;

-- Process business logic outside transaction
process_order_logic();

BEGIN;
    INSERT INTO orders (customer_id, product_id) VALUES (456, 123);
COMMIT;

-- Bad: Large transaction scope
BEGIN;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
    -- ... complex business logic
    -- ... external API calls
    -- ... email sending
    INSERT INTO orders (customer_id, product_id) VALUES (456, 123);
COMMIT;
```

### 4. Use Lower Isolation Levels
```sql
-- Use READ COMMITTED instead of REPEATABLE READ when possible
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
BEGIN;
    SELECT * FROM products WHERE category = 'electronics';
    -- Less likely to cause deadlocks than higher isolation levels
COMMIT;
```

### 5. Avoid User Interaction in Transactions
```python
# Bad: User interaction within transaction
def bad_transfer_money():
    with database.transaction():
        # Lock accounts
        from_account = database.execute(
            "SELECT * FROM accounts WHERE id = %s FOR UPDATE", (from_id,)
        ).fetchone()
        
        # User interaction while holding locks!
        confirmation = input("Confirm transfer? (y/n): ")
        if confirmation != 'y':
            return False
        
        # Complete transfer
        database.execute(
            "UPDATE accounts SET balance = balance - %s WHERE id = %s",
            (amount, from_id)
        )

# Good: User interaction outside transaction
def good_transfer_money():
    # Get user confirmation first
    confirmation = input("Confirm transfer? (y/n): ")
    if confirmation != 'y':
        return False
    
    # Then perform transaction quickly
    with database.transaction():
        database.execute(
            "UPDATE accounts SET balance = balance - %s WHERE id = %s",
            (amount, from_id)
        )
        database.execute(
            "UPDATE accounts SET balance = balance + %s WHERE id = %s",
            (amount, to_id)
        )
```

## Deadlock Resolution

### Automatic Resolution
```sql
-- Most databases automatically resolve deadlocks by:
-- 1. Detecting the deadlock
-- 2. Choosing a victim transaction
-- 3. Rolling back the victim
-- 4. Allowing other transactions to proceed

-- Victim selection criteria:
-- - Transaction with least work done
-- - Transaction with fewer locks
-- - Transaction with lower priority
-- - Newest transaction
```

### Application-Level Retry Logic
```python
import time
import random
from contextlib import contextmanager

class DeadlockException(Exception):
    pass

def execute_with_deadlock_retry(operation, max_retries=3):
    """Execute operation with automatic deadlock retry"""
    for attempt in range(max_retries):
        try:
            return operation()
        except DeadlockException:
            if attempt == max_retries - 1:
                raise
            
            # Exponential backoff with jitter
            delay = (2 ** attempt) + random.uniform(0, 1)
            time.sleep(delay)
            
            print(f"Deadlock detected, retrying... (attempt {attempt + 1})")

def transfer_money_with_retry(from_account, to_account, amount):
    def transfer_operation():
        with database.transaction():
            try:
                # Consistent ordering to prevent deadlocks
                first_account = min(from_account, to_account)
                second_account = max(from_account, to_account)
                
                # Lock accounts in consistent order
                database.execute(
                    "SELECT balance FROM accounts WHERE id = %s FOR UPDATE",
                    (first_account,)
                )
                database.execute(
                    "SELECT balance FROM accounts WHERE id = %s FOR UPDATE", 
                    (second_account,)
                )
                
                # Perform transfer
                database.execute(
                    "UPDATE accounts SET balance = balance - %s WHERE id = %s",
                    (amount, from_account)
                )
                database.execute(
                    "UPDATE accounts SET balance = balance + %s WHERE id = %s",
                    (amount, to_account)
                )
                
            except database.DeadlockError:
                raise DeadlockException()
    
    return execute_with_deadlock_retry(transfer_operation)
```

### Savepoints for Partial Rollback
```sql
-- PostgreSQL: Use savepoints to avoid full transaction rollback
BEGIN;
    INSERT INTO audit_log (action, timestamp) VALUES ('transfer_start', NOW());
    
    SAVEPOINT before_transfer;
    
    BEGIN
        -- Risky operations that might deadlock
        UPDATE accounts SET balance = balance - 100 WHERE account_id = 1;
        UPDATE accounts SET balance = balance + 100 WHERE account_id = 2;
    EXCEPTION
        WHEN deadlock_detected THEN
            ROLLBACK TO SAVEPOINT before_transfer;
            -- Retry logic or alternative approach
            RAISE NOTICE 'Deadlock detected, retrying...';
    END;
    
    INSERT INTO audit_log (action, timestamp) VALUES ('transfer_complete', NOW());
COMMIT;
```

## Database-Specific Handling

### PostgreSQL Deadlock Handling
```sql
-- Configure deadlock detection
-- postgresql.conf
deadlock_timeout = 1s          -- Time to wait before checking for deadlock
log_lock_waits = on            -- Log lock waits longer than deadlock_timeout

-- Handle deadlocks in PL/pgSQL
CREATE OR REPLACE FUNCTION safe_transfer(from_id INT, to_id INT, amount DECIMAL)
RETURNS BOOLEAN AS $$
DECLARE
    attempt_count INT := 0;
    max_attempts INT := 3;
BEGIN
    LOOP
        attempt_count := attempt_count + 1;
        
        BEGIN
            -- Perform transfer with consistent ordering
            IF from_id < to_id THEN
                UPDATE accounts SET balance = balance - amount WHERE account_id = from_id;
                UPDATE accounts SET balance = balance + amount WHERE account_id = to_id;
            ELSE
                UPDATE accounts SET balance = balance + amount WHERE account_id = to_id;
                UPDATE accounts SET balance = balance - amount WHERE account_id = from_id;
            END IF;
            
            RETURN TRUE; -- Success
            
        EXCEPTION
            WHEN deadlock_detected THEN
                IF attempt_count >= max_attempts THEN
                    RAISE EXCEPTION 'Transfer failed after % attempts due to deadlocks', max_attempts;
                END IF;
                
                -- Wait before retry (exponential backoff)
                PERFORM pg_sleep(0.1 * power(2, attempt_count - 1));
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
```

### SQL Server Deadlock Handling
```sql
-- Set deadlock priority
SET DEADLOCK_PRIORITY LOW; -- This session is preferred deadlock victim

-- Handle deadlocks with TRY-CATCH
CREATE PROCEDURE TransferMoney
    @FromAccount INT,
    @ToAccount INT,
    @Amount DECIMAL(10,2)
AS
BEGIN
    DECLARE @RetryCount INT = 0;
    DECLARE @MaxRetries INT = 3;
    
    WHILE @RetryCount < @MaxRetries
    BEGIN
        BEGIN TRY
            BEGIN TRANSACTION;
            
            -- Consistent ordering
            IF @FromAccount < @ToAccount
            BEGIN
                UPDATE Accounts SET Balance = Balance - @Amount WHERE AccountId = @FromAccount;
                UPDATE Accounts SET Balance = Balance + @Amount WHERE AccountId = @ToAccount;
            END
            ELSE
            BEGIN
                UPDATE Accounts SET Balance = Balance + @Amount WHERE AccountId = @ToAccount;
                UPDATE Accounts SET Balance = Balance - @Amount WHERE AccountId = @FromAccount;
            END
            
            COMMIT TRANSACTION;
            RETURN; -- Success
            
        END TRY
        BEGIN CATCH
            IF @@TRANCOUNT > 0
                ROLLBACK TRANSACTION;
            
            -- Check if it's a deadlock
            IF ERROR_NUMBER() = 1205 -- Deadlock error number
            BEGIN
                SET @RetryCount = @RetryCount + 1;
                
                IF @RetryCount < @MaxRetries
                BEGIN
                    -- Wait before retry
                    WAITFOR DELAY '00:00:01'; -- 1 second delay
                    CONTINUE;
                END
            END
            
            -- Re-throw if not a deadlock or max retries reached
            THROW;
        END CATCH
    END
END;
```

### MySQL Deadlock Handling
```sql
-- Configure InnoDB deadlock detection
-- my.cnf
-- innodb_deadlock_detect = ON
-- innodb_lock_wait_timeout = 50

-- Handle deadlocks in stored procedure
DELIMITER //

CREATE PROCEDURE TransferMoney(
    IN from_account INT,
    IN to_account INT,
    IN amount DECIMAL(10,2)
)
BEGIN
    DECLARE retry_count INT DEFAULT 0;
    DECLARE max_retries INT DEFAULT 3;
    DECLARE deadlock_found CONDITION FOR 1213;
    DECLARE EXIT HANDLER FOR deadlock_found
    BEGIN
        ROLLBACK;
        SET retry_count = retry_count + 1;
        IF retry_count < max_retries THEN
            -- Wait before retry
            SELECT SLEEP(0.1 * POW(2, retry_count - 1));
            ITERATE retry_loop;
        ELSE
            RESIGNAL;
        END IF;
    END;
    
    retry_loop: LOOP
        START TRANSACTION;
        
        -- Consistent ordering
        IF from_account < to_account THEN
            UPDATE accounts SET balance = balance - amount WHERE account_id = from_account;
            UPDATE accounts SET balance = balance + amount WHERE account_id = to_account;
        ELSE
            UPDATE accounts SET balance = balance + amount WHERE account_id = to_account;
            UPDATE accounts SET balance = balance - amount WHERE account_id = from_account;
        END IF;
        
        COMMIT;
        LEAVE retry_loop; -- Success
    END LOOP;
END //

DELIMITER ;
```

## Monitoring and Analysis

### Deadlock Statistics
```sql
-- PostgreSQL: Check deadlock statistics
SELECT 
    datname,
    deadlocks
FROM pg_stat_database
WHERE datname NOT IN ('template0', 'template1');

-- SQL Server: Deadlock statistics
SELECT 
    cntr_value as deadlock_count
FROM sys.dm_os_performance_counters
WHERE counter_name = 'Number of Deadlocks/sec'
AND object_name = 'SQLServer:Locks';

-- MySQL: Deadlock information
SHOW ENGINE INNODB STATUS;
-- Look for "LATEST DETECTED DEADLOCK" section
```

### Deadlock Analysis Tools
```python
# Python script to analyze deadlock patterns
import re
from collections import defaultdict

def analyze_deadlock_log(log_file):
    """Analyze PostgreSQL log for deadlock patterns"""
    deadlock_patterns = defaultdict(int)
    
    with open(log_file, 'r') as f:
        content = f.read()
    
    # Find deadlock entries
    deadlock_entries = re.findall(
        r'DETAIL:.*?deadlock detected.*?STATEMENT:.*?(?=\n\d{4}-|\Z)',
        content,
        re.DOTALL
    )
    
    for entry in deadlock_entries:
        # Extract table names involved
        tables = re.findall(r'relation "(\w+)"', entry)
        if len(tables) >= 2:
            # Sort tables to create consistent pattern
            pattern = ' -> '.join(sorted(tables))
            deadlock_patterns[pattern] += 1
    
    return deadlock_patterns

# Usage
patterns = analyze_deadlock_log('/var/log/postgresql/postgresql.log')
for pattern, count in sorted(patterns.items(), key=lambda x: x[1], reverse=True):
    print(f"{pattern}: {count} occurrences")
```

## Best Practices

### 1. Design for Deadlock Avoidance
```sql
-- Use consistent resource ordering
CREATE OR REPLACE FUNCTION transfer_funds(
    from_account_id INTEGER,
    to_account_id INTEGER,
    transfer_amount DECIMAL
) RETURNS BOOLEAN AS $$
BEGIN
    -- Always lock accounts in ascending order of ID
    IF from_account_id < to_account_id THEN
        PERFORM * FROM accounts WHERE account_id = from_account_id FOR UPDATE;
        PERFORM * FROM accounts WHERE account_id = to_account_id FOR UPDATE;
    ELSE
        PERFORM * FROM accounts WHERE account_id = to_account_id FOR UPDATE;
        PERFORM * FROM accounts WHERE account_id = from_account_id FOR UPDATE;
    END IF;
    
    -- Perform the transfer
    UPDATE accounts SET balance = balance - transfer_amount WHERE account_id = from_account_id;
    UPDATE accounts SET balance = balance + transfer_amount WHERE account_id = to_account_id;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
```

### 2. Implement Robust Retry Logic
```python
class DeadlockRetryManager:
    def __init__(self, max_retries=3, base_delay=0.1):
        self.max_retries = max_retries
        self.base_delay = base_delay
    
    def execute(self, operation, *args, **kwargs):
        for attempt in range(self.max_retries):
            try:
                return operation(*args, **kwargs)
            except DeadlockException as e:
                if attempt == self.max_retries - 1:
                    raise DeadlockException(f"Operation failed after {self.max_retries} attempts") from e
                
                # Exponential backoff with jitter
                delay = self.base_delay * (2 ** attempt) + random.uniform(0, 0.1)
                time.sleep(delay)
                
                logging.warning(f"Deadlock detected, retrying in {delay:.2f}s (attempt {attempt + 1})")

# Usage
retry_manager = DeadlockRetryManager()
result = retry_manager.execute(transfer_money, from_account=1, to_account=2, amount=100)
```

### 3. Monitor and Alert
```python
# Deadlock monitoring and alerting
def check_deadlock_rate():
    """Monitor deadlock rate and alert if threshold exceeded"""
    current_deadlocks = get_deadlock_count()
    previous_deadlocks = get_previous_deadlock_count()
    
    deadlock_rate = (current_deadlocks - previous_deadlocks) / 60  # per minute
    
    if deadlock_rate > DEADLOCK_THRESHOLD:
        send_alert(f"High deadlock rate detected: {deadlock_rate:.2f}/min")
        
        # Analyze recent deadlocks
        recent_patterns = analyze_recent_deadlocks()
        for pattern, count in recent_patterns.items():
            logging.warning(f"Deadlock pattern: {pattern} ({count} times)")

# Schedule monitoring
schedule.every(1).minutes.do(check_deadlock_rate)
```

### 4. Use Application-Level Coordination
```python
# Use application-level locks for complex operations
import threading

class ApplicationLockManager:
    def __init__(self):
        self._locks = {}
        self._lock = threading.Lock()
    
    def get_ordered_locks(self, *resource_ids):
        """Get locks in consistent order to prevent deadlocks"""
        sorted_ids = sorted(resource_ids)
        locks = []
        
        with self._lock:
            for resource_id in sorted_ids:
                if resource_id not in self._locks:
                    self._locks[resource_id] = threading.Lock()
                locks.append(self._locks[resource_id])
        
        return locks
    
    @contextmanager
    def acquire_multiple(self, *resource_ids):
        """Acquire multiple locks in consistent order"""
        locks = self.get_ordered_locks(*resource_ids)
        
        # Acquire all locks
        for lock in locks:
            lock.acquire()
        
        try:
            yield
        finally:
            # Release in reverse order
            for lock in reversed(locks):
                lock.release()

# Usage
lock_manager = ApplicationLockManager()

def transfer_with_app_locks(from_account, to_account, amount):
    with lock_manager.acquire_multiple(from_account, to_account):
        # Database operations are now serialized at application level
        with database.transaction():
            database.execute(
                "UPDATE accounts SET balance = balance - %s WHERE id = %s",
                (amount, from_account)
            )
            database.execute(
                "UPDATE accounts SET balance = balance + %s WHERE id = %s",
                (amount, to_account)
            )
```

## 🚀 Next Steps

After mastering deadlock management:
- [Transaction Log Management](./log-management.md) - Understand transaction logging
- [Long-Running Transactions](./long-running-transactions.md) - Handle complex scenarios
- [Performance Optimization](../optimization/README.md) - Optimize transaction performance

## 📝 Practice Exercises

1. Create a banking system that handles concurrent transfers without deadlocks
2. Implement a reservation system with proper deadlock prevention
3. Build a work queue system that avoids deadlocks under high concurrency
4. Design a multi-table update operation with consistent resource ordering
5. Create a deadlock monitoring and alerting system

Remember: Deadlock prevention is better than deadlock resolution. Design your applications to minimize deadlock potential through consistent resource ordering and minimal transaction scope!
