# Redis Complete Guide

Redis (Remote Dictionary Server) is an in-memory data structure store used as a database, cache, and message broker. This comprehensive guide covers Redis data types, operations, clustering, and best practices for high-performance applications.

## 📚 Contents

### Getting Started
- [Installation and Configuration](./installation-config.md) - Setting up Redis for different environments
- [Data Types and Operations](./data-types.md) - Redis data structures and commands
- [Persistence (RDB, AOF)](./persistence.md) - Data durability strategies

### Advanced Features
- [Redis Cluster](./clustering.md) - Horizontal scaling with Redis Cluster
- [Pub/Sub Messaging](./pubsub.md) - Real-time messaging patterns
- [Redis Streams](./streams.md) - Log-based data structures for event streaming
- [Lua Scripting](./lua-scripting.md) - Server-side scripting capabilities

### Performance and Operations
- [Memory Optimization](./memory-optimization.md) - Efficient memory usage strategies
- [Redis Modules](./modules.md) - Extending Redis functionality
- [Monitoring and Debugging](./monitoring.md) - Keeping Redis healthy and performant

## 🎯 Learning Path

### Beginner (Start Here)
1. [Installation and Configuration](./installation-config.md) - Get Redis running
2. [Data Types and Operations](./data-types.md) - Master Redis data structures
3. [Persistence](./persistence.md) - Understand data durability

### Intermediate
4. [Pub/Sub Messaging](./pubsub.md) - Real-time communication
5. [Lua Scripting](./lua-scripting.md) - Server-side logic
6. [Memory Optimization](./memory-optimization.md) - Efficient resource usage

### Advanced
7. [Redis Cluster](./clustering.md) - Scale horizontally
8. [Redis Streams](./streams.md) - Event streaming
9. [Redis Modules](./modules.md) - Extend functionality
10. [Monitoring and Debugging](./monitoring.md) - Production operations

## 💡 Redis Key Features

### In-Memory Performance
- **Sub-millisecond latency**: Extremely fast read/write operations
- **High throughput**: Millions of operations per second
- **Single-threaded**: Eliminates locking overhead
- **Pipelining**: Batch multiple commands
- **Atomic operations**: All operations are atomic

### Rich Data Types
- **Strings**: Binary-safe strings up to 512MB
- **Lists**: Ordered collections of strings
- **Sets**: Unordered collections of unique strings
- **Sorted Sets**: Sets ordered by score
- **Hashes**: Field-value pairs (like objects)
- **Bitmaps**: Bit-level operations
- **HyperLogLog**: Probabilistic cardinality estimation
- **Streams**: Log-based data structures

### Advanced Capabilities
- **Persistence**: RDB snapshots and AOF logging
- **Replication**: Master-slave replication
- **Clustering**: Automatic sharding and failover
- **Pub/Sub**: Message broadcasting
- **Lua Scripting**: Server-side scripting
- **Modules**: Extensible architecture

## 🔧 Quick Start

### Installation (Ubuntu/Debian)
```bash
# Install Redis
sudo apt update
sudo apt install redis-server

# Start Redis service
sudo systemctl start redis-server
sudo systemctl enable redis-server

# Test Redis
redis-cli ping
# Should return: PONG
```

### Basic Operations
```bash
# Connect to Redis
redis-cli

# Basic string operations
SET mykey "Hello Redis"
GET mykey
INCR counter
EXPIRE mykey 60

# List operations
LPUSH mylist "item1"
LPUSH mylist "item2"
LRANGE mylist 0 -1

# Hash operations
HSET user:1 name "John Doe"
HSET user:1 email "<EMAIL>"
HGETALL user:1
```

### Configuration Basics
```bash
# Redis configuration file: /etc/redis/redis.conf

# Memory settings
maxmemory 2gb
maxmemory-policy allkeys-lru

# Persistence settings
save 900 1      # Save if at least 1 key changed in 900 seconds
save 300 10     # Save if at least 10 keys changed in 300 seconds
save 60 10000   # Save if at least 10000 keys changed in 60 seconds

# Network settings
bind 127.0.0.1
port 6379
timeout 300

# Security
requirepass your_secure_password
```

## 📊 Redis Data Types Deep Dive

### Strings
```bash
# Basic string operations
SET key value
GET key
MSET key1 value1 key2 value2
MGET key1 key2

# Numeric operations
SET counter 10
INCR counter        # Returns 11
INCRBY counter 5    # Returns 16
DECR counter        # Returns 15

# Binary operations
SETBIT key 7 1
GETBIT key 7
BITCOUNT key
```

### Lists
```bash
# List operations (stack/queue behavior)
LPUSH mylist "first"    # Add to left (head)
RPUSH mylist "last"     # Add to right (tail)
LPOP mylist             # Remove from left
RPOP mylist             # Remove from right

# Blocking operations
BLPOP mylist 10         # Block for 10 seconds waiting for element
BRPOP mylist 10

# List manipulation
LLEN mylist             # Get length
LINDEX mylist 0         # Get element at index
LSET mylist 0 "new"     # Set element at index
LTRIM mylist 0 99       # Keep only elements 0-99
```

### Sets
```bash
# Set operations
SADD myset "member1"
SADD myset "member2"
SMEMBERS myset
SISMEMBER myset "member1"

# Set operations between sets
SINTER set1 set2        # Intersection
SUNION set1 set2        # Union
SDIFF set1 set2         # Difference
SCARD myset             # Cardinality (size)
```

### Sorted Sets
```bash
# Sorted set operations
ZADD leaderboard 100 "player1"
ZADD leaderboard 200 "player2"
ZADD leaderboard 150 "player3"

# Range operations
ZRANGE leaderboard 0 -1 WITHSCORES    # All members with scores
ZREVRANGE leaderboard 0 2             # Top 3 players
ZRANGEBYSCORE leaderboard 100 200     # Players with score 100-200

# Rank operations
ZRANK leaderboard "player1"           # Get rank (0-based)
ZSCORE leaderboard "player1"          # Get score
ZINCRBY leaderboard 50 "player1"      # Increment score
```

### Hashes
```bash
# Hash operations
HSET user:1 name "John"
HSET user:1 email "<EMAIL>"
HSET user:1 age 30

HGET user:1 name
HGETALL user:1
HMGET user:1 name email

# Hash manipulation
HINCRBY user:1 age 1        # Increment age
HEXISTS user:1 name         # Check if field exists
HDEL user:1 age             # Delete field
HLEN user:1                 # Number of fields
```

## 🚀 Common Use Cases

### Caching
```python
import redis
import json

# Connect to Redis
r = redis.Redis(host='localhost', port=6379, db=0)

# Cache user data
def get_user(user_id):
    # Try cache first
    cached = r.get(f"user:{user_id}")
    if cached:
        return json.loads(cached)
    
    # Fetch from database
    user = fetch_user_from_db(user_id)
    
    # Cache for 1 hour
    r.setex(f"user:{user_id}", 3600, json.dumps(user))
    return user

# Cache invalidation
def update_user(user_id, data):
    # Update database
    update_user_in_db(user_id, data)
    
    # Invalidate cache
    r.delete(f"user:{user_id}")
```

### Session Storage
```python
# Store session data
def create_session(user_id, session_data):
    session_id = generate_session_id()
    session_key = f"session:{session_id}"
    
    # Store session with 24-hour expiry
    r.hmset(session_key, session_data)
    r.expire(session_key, 86400)
    
    return session_id

# Retrieve session
def get_session(session_id):
    session_key = f"session:{session_id}"
    return r.hgetall(session_key)

# Extend session
def extend_session(session_id):
    session_key = f"session:{session_id}"
    r.expire(session_key, 86400)
```

### Real-time Analytics
```python
# Track page views
def track_page_view(page, timestamp):
    # Increment daily counter
    date = timestamp.strftime('%Y-%m-%d')
    r.incr(f"pageviews:{page}:{date}")
    
    # Add to hourly sorted set
    hour = timestamp.strftime('%Y-%m-%d:%H')
    r.zincrby(f"hourly_views:{date}", 1, hour)
    
    # Track unique visitors (HyperLogLog)
    r.pfadd(f"unique_visitors:{page}:{date}", user_id)

# Get analytics
def get_page_analytics(page, date):
    daily_views = r.get(f"pageviews:{page}:{date}") or 0
    unique_visitors = r.pfcount(f"unique_visitors:{page}:{date}")
    hourly_data = r.zrange(f"hourly_views:{date}", 0, -1, withscores=True)
    
    return {
        'daily_views': int(daily_views),
        'unique_visitors': unique_visitors,
        'hourly_breakdown': dict(hourly_data)
    }
```

### Rate Limiting
```python
def is_rate_limited(user_id, limit=100, window=3600):
    key = f"rate_limit:{user_id}"
    current = r.get(key)
    
    if current is None:
        # First request in window
        r.setex(key, window, 1)
        return False
    
    if int(current) >= limit:
        return True
    
    # Increment counter
    r.incr(key)
    return False

# Sliding window rate limiting
def sliding_window_rate_limit(user_id, limit=100, window=3600):
    key = f"sliding_rate_limit:{user_id}"
    now = time.time()
    
    # Remove old entries
    r.zremrangebyscore(key, 0, now - window)
    
    # Count current requests
    current_count = r.zcard(key)
    
    if current_count >= limit:
        return True
    
    # Add current request
    r.zadd(key, {str(now): now})
    r.expire(key, window)
    
    return False
```

### Message Queues
```python
# Producer
def enqueue_job(queue_name, job_data):
    r.lpush(queue_name, json.dumps(job_data))

# Consumer
def process_jobs(queue_name):
    while True:
        # Blocking pop with timeout
        result = r.brpop(queue_name, timeout=10)
        if result:
            queue, job_data = result
            job = json.loads(job_data)
            process_job(job)

# Priority queue using sorted sets
def enqueue_priority_job(job_data, priority):
    job_id = generate_job_id()
    r.zadd("priority_queue", {job_id: priority})
    r.hset(f"job:{job_id}", mapping=job_data)

def dequeue_priority_job():
    # Get highest priority job
    result = r.zpopmax("priority_queue")
    if result:
        job_id, priority = result[0]
        job_data = r.hgetall(f"job:{job_id}")
        r.delete(f"job:{job_id}")
        return job_data
    return None
```

## 📈 Performance Optimization

### Memory Optimization
```bash
# Monitor memory usage
INFO memory

# Configure memory policies
CONFIG SET maxmemory 2gb
CONFIG SET maxmemory-policy allkeys-lru

# Use appropriate data structures
# For small hashes/sets, Redis uses compact encoding
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
set-max-intset-entries 512
```

### Connection Optimization
```python
# Use connection pooling
import redis

pool = redis.ConnectionPool(
    host='localhost',
    port=6379,
    max_connections=20,
    retry_on_timeout=True
)

r = redis.Redis(connection_pool=pool)

# Pipeline multiple commands
pipe = r.pipeline()
pipe.set('key1', 'value1')
pipe.set('key2', 'value2')
pipe.incr('counter')
results = pipe.execute()
```

### Monitoring and Metrics
```bash
# Real-time monitoring
redis-cli --latency
redis-cli --latency-history
redis-cli --stat

# Key metrics to monitor
INFO stats
INFO replication
INFO persistence
INFO memory
INFO clients

# Slow log analysis
SLOWLOG GET 10
CONFIG SET slowlog-log-slower-than 10000  # 10ms
```

## 🔐 Security Best Practices

### Authentication and Authorization
```bash
# Set password
CONFIG SET requirepass your_secure_password

# Rename dangerous commands
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG "CONFIG_b840fc02d524045429941cc15f59e41cb7be6c52"

# Disable dangerous commands
rename-command DEBUG ""
rename-command EVAL ""
```

### Network Security
```bash
# Bind to specific interfaces
bind 127.0.0.1 ********

# Use protected mode
protected-mode yes

# Configure firewall
sudo ufw allow from 10.0.0.0/8 to any port 6379
```

### SSL/TLS Configuration
```bash
# Enable TLS (Redis 6.0+)
tls-port 6380
tls-cert-file /path/to/redis.crt
tls-key-file /path/to/redis.key
tls-ca-cert-file /path/to/ca.crt
```

## 📋 Redis Checklist

### Installation and Setup
- [ ] Redis installed and configured
- [ ] Security settings applied
- [ ] Memory limits configured
- [ ] Persistence strategy chosen
- [ ] Monitoring tools set up

### Performance Optimization
- [ ] Memory usage optimized
- [ ] Connection pooling implemented
- [ ] Appropriate data structures used
- [ ] Pipelining utilized where beneficial
- [ ] Slow queries identified and optimized

### Security
- [ ] Authentication enabled
- [ ] Network access restricted
- [ ] Dangerous commands disabled
- [ ] SSL/TLS configured (if needed)
- [ ] Regular security updates applied

### Operations
- [ ] Backup procedures implemented
- [ ] Monitoring and alerting configured
- [ ] Capacity planning done
- [ ] Disaster recovery tested
- [ ] Documentation maintained

## 🔗 Related Topics

- [Caching Strategies](../../performance/query-caching.md) - Database caching patterns
- [NoSQL Design](../../design/schema-patterns.md) - NoSQL design principles
- [Performance Monitoring](../../monitoring/README.md) - System monitoring
- [Security](../../security/README.md) - Database security practices

## 📚 Further Reading

- [Redis Official Documentation](https://redis.io/documentation)
- [Redis Commands Reference](https://redis.io/commands)
- [Redis Best Practices](https://redis.io/topics/memory-optimization)
- [Redis Clustering Guide](https://redis.io/topics/cluster-tutorial)

Start with [Installation and Configuration](./installation-config.md) to get Redis running, then explore [Data Types and Operations](./data-types.md) to master Redis data structures!
