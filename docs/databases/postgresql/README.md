# PostgreSQL Complete Guide

PostgreSQL is a powerful, open-source object-relational database system with a strong reputation for reliability, feature robustness, and performance. This comprehensive guide covers PostgreSQL-specific features, optimization techniques, and best practices.

## 📚 Contents

### Installation and Setup
- [Installation and Configuration](./installation-config.md) - Setting up PostgreSQL for different environments
- [PostgreSQL-Specific SQL Features](./sql-features.md) - Advanced SQL capabilities unique to PostgreSQL
- [Advanced Data Types](./data-types.md) - PostgreSQL's rich data type system

### Advanced Features
- [Extensions (PostGIS, pg_stat_statements, etc.)](./extensions.md) - Extending PostgreSQL functionality
- [VACUUM and ANALYZE](./vacuum-analyze.md) - Maintenance operations for optimal performance
- [Connection Pooling (PgBouncer)](./connection-pooling.md) - Managing database connections efficiently

### High Availability and Scaling
- [Replication Setup](./replication.md) - Master-slave and streaming replication
- [Partitioning](./partitioning.md) - Table partitioning strategies
- [Performance Tuning](./performance-tuning.md) - PostgreSQL-specific optimization

### Operations and Maintenance
- [Backup and Point-in-Time Recovery](./backup-recovery.md) - Comprehensive backup strategies
- [Monitoring and Maintenance](./monitoring.md) - Keeping PostgreSQL healthy

## 🎯 Learning Path

### Beginner (Start Here)
1. [Installation and Configuration](./installation-config.md) - Get PostgreSQL running
2. [PostgreSQL-Specific SQL Features](./sql-features.md) - Learn unique capabilities
3. [Advanced Data Types](./data-types.md) - Understand PostgreSQL's type system

### Intermediate
4. [Extensions](./extensions.md) - Extend PostgreSQL functionality
5. [VACUUM and ANALYZE](./vacuum-analyze.md) - Master maintenance operations
6. [Connection Pooling](./connection-pooling.md) - Optimize connection management

### Advanced
7. [Replication Setup](./replication.md) - Implement high availability
8. [Partitioning](./partitioning.md) - Scale large datasets
9. [Performance Tuning](./performance-tuning.md) - Optimize for your workload
10. [Backup and Recovery](./backup-recovery.md) - Protect your data
11. [Monitoring](./monitoring.md) - Maintain system health

## 💡 PostgreSQL Key Features

### Advanced SQL Support
- **Window Functions**: Comprehensive analytical capabilities
- **CTEs and Recursive Queries**: Complex query structures
- **JSON/JSONB**: Native JSON support with indexing
- **Arrays**: Native array data types and operations
- **Full-Text Search**: Built-in text search capabilities

### Extensibility
- **Custom Data Types**: Define your own data types
- **Custom Functions**: Write functions in multiple languages
- **Extensions**: Rich ecosystem of extensions
- **Foreign Data Wrappers**: Access external data sources
- **Custom Operators**: Define custom operators

### ACID Compliance
- **Full ACID compliance**: Reliable transactions
- **MVCC**: Multi-Version Concurrency Control
- **Isolation Levels**: Configurable transaction isolation
- **Point-in-Time Recovery**: Precise recovery capabilities
- **Write-Ahead Logging**: Durable transaction logging

### Performance Features
- **Advanced Indexing**: B-tree, Hash, GiST, SP-GiST, GIN, BRIN
- **Query Planner**: Sophisticated cost-based optimizer
- **Parallel Queries**: Multi-core query execution
- **Partitioning**: Table and index partitioning
- **Connection Pooling**: Efficient connection management

## 🔧 Quick Start

### Installation (Ubuntu/Debian)
```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Start PostgreSQL service
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Access PostgreSQL
sudo -u postgres psql
```

### Basic Configuration
```sql
-- Connect to PostgreSQL
psql -U postgres -h localhost

-- Create database
CREATE DATABASE myapp;

-- Create user
CREATE USER myapp_user WITH PASSWORD 'secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE myapp TO myapp_user;

-- Connect to new database
\c myapp
```

### Essential Settings
```sql
-- postgresql.conf key settings
shared_buffers = 256MB              -- 25% of RAM for dedicated server
work_mem = 4MB                      -- Per-operation memory
maintenance_work_mem = 64MB         -- Maintenance operations
effective_cache_size = 1GB          -- OS cache estimate
random_page_cost = 1.1              -- SSD optimization
```

## 📊 PostgreSQL Architecture

### Process Architecture
```
PostgreSQL Instance
├── Postmaster (main process)
├── Background Processes
│   ├── Background Writer
│   ├── WAL Writer
│   ├── Checkpointer
│   ├── Autovacuum Launcher
│   └── Statistics Collector
└── Backend Processes (one per connection)
```

### Memory Architecture
```
Shared Memory
├── Shared Buffers (data cache)
├── WAL Buffers (transaction log)
└── Lock Tables

Per-Connection Memory
├── work_mem (sort/hash operations)
├── maintenance_work_mem (maintenance)
└── temp_buffers (temporary tables)
```

### Storage Architecture
```
Data Directory
├── base/ (database files)
├── pg_wal/ (Write-Ahead Log)
├── pg_xact/ (transaction status)
├── pg_multixact/ (multixact status)
└── postgresql.conf (configuration)
```

## 🛠️ Common Operations

### Database Management
```sql
-- List databases
\l

-- Create database with options
CREATE DATABASE myapp 
    WITH OWNER = myapp_user
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TEMPLATE = template0;

-- Drop database
DROP DATABASE myapp;

-- Database size
SELECT pg_size_pretty(pg_database_size('myapp'));
```

### User Management
```sql
-- Create user with specific privileges
CREATE USER readonly_user WITH PASSWORD 'password';
GRANT CONNECT ON DATABASE myapp TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;

-- Create superuser
CREATE USER admin_user WITH PASSWORD 'password' SUPERUSER;

-- Modify user
ALTER USER myapp_user WITH PASSWORD 'new_password';
ALTER USER myapp_user CREATEDB;

-- List users
\du
```

### Schema Management
```sql
-- Create schema
CREATE SCHEMA sales;

-- Set search path
SET search_path TO sales, public;

-- Grant schema privileges
GRANT USAGE ON SCHEMA sales TO myapp_user;
GRANT CREATE ON SCHEMA sales TO myapp_user;

-- List schemas
\dn
```

## 📈 Performance Optimization

### Query Optimization
```sql
-- Analyze query performance
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM orders WHERE order_date >= '2024-01-01';

-- Update table statistics
ANALYZE orders;

-- Vacuum table
VACUUM orders;

-- Reindex table
REINDEX TABLE orders;
```

### Index Optimization
```sql
-- Create various index types
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status_date ON orders(status, order_date);
CREATE INDEX idx_orders_customer_partial ON orders(customer_id) WHERE status = 'active';

-- GIN index for arrays
CREATE INDEX idx_tags_gin ON articles USING GIN(tags);

-- GiST index for full-text search
CREATE INDEX idx_content_fts ON articles USING GiST(to_tsvector('english', content));
```

### Configuration Tuning
```sql
-- View current settings
SHOW ALL;
SHOW shared_buffers;

-- Modify settings (requires restart for some)
ALTER SYSTEM SET shared_buffers = '512MB';
SELECT pg_reload_conf();

-- Session-level settings
SET work_mem = '8MB';
SET enable_seqscan = off;
```

## 🔍 Monitoring and Diagnostics

### System Information
```sql
-- Database activity
SELECT * FROM pg_stat_activity;

-- Database statistics
SELECT * FROM pg_stat_database;

-- Table statistics
SELECT * FROM pg_stat_user_tables;

-- Index usage
SELECT * FROM pg_stat_user_indexes;
```

### Performance Monitoring
```sql
-- Enable pg_stat_statements
CREATE EXTENSION pg_stat_statements;

-- View slow queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;

-- Lock monitoring
SELECT * FROM pg_locks WHERE NOT granted;
```

### Maintenance Information
```sql
-- Vacuum and analyze status
SELECT 
    schemaname,
    tablename,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables;

-- Table bloat estimation
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

## 🔐 Security Best Practices

### Authentication and Authorization
```sql
-- Create role hierarchy
CREATE ROLE app_read;
CREATE ROLE app_write;
CREATE ROLE app_admin;

-- Grant role permissions
GRANT SELECT ON ALL TABLES IN SCHEMA public TO app_read;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_write;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_admin;

-- Assign roles to users
GRANT app_read TO readonly_user;
GRANT app_write TO app_user;
GRANT app_admin TO admin_user;
```

### Connection Security
```bash
# pg_hba.conf configuration
# TYPE  DATABASE        USER            ADDRESS                 METHOD

# Local connections
local   all             postgres                                peer
local   all             all                                     md5

# IPv4 local connections
host    all             all             127.0.0.1/32            md5
host    all             all             10.0.0.0/8              md5

# SSL connections
hostssl all             all             0.0.0.0/0               md5
```

### Data Encryption
```sql
-- Enable SSL
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'

-- Column-level encryption
CREATE EXTENSION pgcrypto;

-- Encrypt sensitive data
INSERT INTO users (username, password) 
VALUES ('john', crypt('password', gen_salt('bf')));

-- Verify password
SELECT * FROM users 
WHERE username = 'john' 
AND password = crypt('password', password);
```

## 🚀 Advanced Features

### JSON/JSONB Operations
```sql
-- Create table with JSONB
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    attributes JSONB
);

-- Insert JSON data
INSERT INTO products (name, attributes) VALUES
('Laptop', '{"brand": "Dell", "cpu": "Intel i7", "ram": "16GB"}'),
('Phone', '{"brand": "Apple", "model": "iPhone 14", "storage": "128GB"}');

-- Query JSON data
SELECT name, attributes->>'brand' as brand
FROM products
WHERE attributes->>'brand' = 'Dell';

-- Create GIN index on JSONB
CREATE INDEX idx_products_attributes ON products USING GIN(attributes);
```

### Full-Text Search
```sql
-- Create full-text search index
CREATE INDEX idx_articles_fts ON articles 
USING GIN(to_tsvector('english', title || ' ' || content));

-- Search articles
SELECT title, ts_rank(to_tsvector('english', title || ' ' || content), 
                     plainto_tsquery('english', 'database optimization')) as rank
FROM articles
WHERE to_tsvector('english', title || ' ' || content) @@ 
      plainto_tsquery('english', 'database optimization')
ORDER BY rank DESC;
```

### Window Functions
```sql
-- Advanced analytics with window functions
SELECT 
    customer_id,
    order_date,
    order_amount,
    SUM(order_amount) OVER (PARTITION BY customer_id ORDER BY order_date) as running_total,
    LAG(order_amount) OVER (PARTITION BY customer_id ORDER BY order_date) as prev_order,
    RANK() OVER (PARTITION BY EXTRACT(MONTH FROM order_date) ORDER BY order_amount DESC) as monthly_rank
FROM orders
ORDER BY customer_id, order_date;
```

## 📋 PostgreSQL Checklist

### Installation and Setup
- [ ] PostgreSQL installed and configured
- [ ] Basic security settings applied
- [ ] Backup strategy implemented
- [ ] Monitoring tools configured
- [ ] Connection pooling set up

### Performance Optimization
- [ ] Memory settings tuned
- [ ] Appropriate indexes created
- [ ] Query performance analyzed
- [ ] Vacuum and analyze scheduled
- [ ] Statistics updated regularly

### Security
- [ ] Authentication configured
- [ ] SSL/TLS enabled
- [ ] User roles and permissions set
- [ ] Audit logging enabled
- [ ] Regular security updates applied

### Maintenance
- [ ] Backup procedures tested
- [ ] Recovery procedures documented
- [ ] Monitoring alerts configured
- [ ] Maintenance windows scheduled
- [ ] Documentation updated

## 🔗 Related Topics

- [SQL Fundamentals](../../sql/README.md) - Core SQL concepts
- [Query Optimization](../../optimization/README.md) - Performance tuning
- [Database Design](../../design/README.md) - Schema design principles
- [Security](../../security/README.md) - Database security practices

## 📚 Further Reading

- [PostgreSQL Official Documentation](https://www.postgresql.org/docs/)
- [PostgreSQL Wiki](https://wiki.postgresql.org/)
- [PostgreSQL Performance Tuning](https://www.postgresql.org/docs/current/performance-tips.html)
- [PostgreSQL High Availability](https://www.postgresql.org/docs/current/high-availability.html)

Start with [Installation and Configuration](./installation-config.md) to get PostgreSQL up and running, then explore [PostgreSQL-Specific SQL Features](./sql-features.md) to leverage its unique capabilities!
