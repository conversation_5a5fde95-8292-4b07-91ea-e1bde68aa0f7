# Query Caching

Query caching is a performance optimization technique that stores the results of expensive database queries in memory, allowing subsequent identical queries to be served from cache rather than re-executing against the database. This guide covers comprehensive caching strategies, implementation patterns, and best practices.

## 📋 Table of Contents

1. [Caching Fundamentals](#caching-fundamentals)
2. [Cache Types and Levels](#cache-types-and-levels)
3. [Implementation Strategies](#implementation-strategies)
4. [Cache Invalidation](#cache-invalidation)
5. [Database-Specific Caching](#database-specific-caching)
6. [Application-Level Caching](#application-level-caching)
7. [Monitoring and Optimization](#monitoring-and-optimization)

## Caching Fundamentals

### What is Query Caching?
Query caching stores the results of database queries in fast-access memory, reducing the need to re-execute expensive operations and improving application response times.

### Benefits of Query Caching
- **Reduced Database Load**: Fewer queries hit the database
- **Improved Response Times**: Cache hits are much faster than database queries
- **Better Scalability**: Handle more concurrent users with same resources
- **Cost Efficiency**: Reduce database resource consumption

### Cache Hit vs Cache Miss
```python
# Cache hit: Data found in cache
def get_user_profile(user_id):
    cache_key = f"user_profile:{user_id}"

    # Try cache first
    cached_data = cache.get(cache_key)
    if cached_data:  # Cache hit
        return cached_data

    # Cache miss: Query database
    user_data = database.query("SELECT * FROM users WHERE id = %s", user_id)

    # Store in cache for future requests
    cache.set(cache_key, user_data, ttl=300)  # 5 minutes

    return user_data
```

## Cache Types and Levels

### Database-Level Caching
```sql
-- MySQL: Query cache (deprecated in 8.0)
-- my.cnf
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 1M

-- Check query cache status
SHOW STATUS LIKE 'Qcache%';

-- PostgreSQL: Shared buffers (data page caching)
-- postgresql.conf
shared_buffers = 256MB
effective_cache_size = 1GB

-- SQL Server: Buffer pool
-- Automatic buffer pool management
-- Monitor with sys.dm_os_buffer_descriptors
```

### Application-Level Caching
```python
# Redis-based application caching
import redis
import json
import hashlib
from datetime import timedelta

class QueryCache:
    def __init__(self, redis_host='localhost', redis_port=6379):
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
        self.default_ttl = 300  # 5 minutes

    def get_cache_key(self, query, params=None):
        """Generate cache key from query and parameters"""
        key_data = f"{query}:{params}" if params else query
        return hashlib.md5(key_data.encode()).hexdigest()

    def get(self, query, params=None):
        """Get cached query result"""
        cache_key = self.get_cache_key(query, params)
        cached_data = self.redis_client.get(cache_key)

        if cached_data:
            return json.loads(cached_data)
        return None

    def set(self, query, params, result, ttl=None):
        """Cache query result"""
        cache_key = self.get_cache_key(query, params)
        ttl = ttl or self.default_ttl

        self.redis_client.setex(
            cache_key,
            ttl,
            json.dumps(result, default=str)  # Handle datetime serialization
        )

    def invalidate(self, pattern):
        """Invalidate cache entries matching pattern"""
        keys = self.redis_client.keys(pattern)
        if keys:
            self.redis_client.delete(*keys)

# Usage
cache = QueryCache()

def get_popular_products():
    query = "SELECT * FROM products WHERE rating > 4.5 ORDER BY sales_count DESC LIMIT 10"

    # Try cache first
    cached_result = cache.get(query)
    if cached_result:
        return cached_result

    # Execute query
    result = database.execute(query)

    # Cache result for 1 hour
    cache.set(query, None, result, ttl=3600)

    return result
```

### Multi-Level Caching
```python
# Implement multi-level caching strategy
class MultiLevelCache:
    def __init__(self):
        self.l1_cache = {}  # In-memory cache (fastest)
        self.l1_max_size = 1000
        self.l2_cache = redis.Redis()  # Redis cache (fast)
        self.l3_cache = memcached.Client(['127.0.0.1:11211'])  # Memcached (distributed)

    def get(self, key):
        """Get value from multi-level cache"""
        # Try L1 cache (in-memory)
        if key in self.l1_cache:
            return self.l1_cache[key]

        # Try L2 cache (Redis)
        value = self.l2_cache.get(key)
        if value:
            # Promote to L1 cache
            self._set_l1(key, json.loads(value))
            return json.loads(value)

        # Try L3 cache (Memcached)
        value = self.l3_cache.get(key)
        if value:
            # Promote to L2 and L1 caches
            self.l2_cache.setex(key, 300, json.dumps(value))
            self._set_l1(key, value)
            return value

        return None

    def set(self, key, value, ttl=300):
        """Set value in all cache levels"""
        # Set in all levels
        self._set_l1(key, value)
        self.l2_cache.setex(key, ttl, json.dumps(value, default=str))
        self.l3_cache.set(key, value, time=ttl)

    def _set_l1(self, key, value):
        """Set value in L1 cache with size limit"""
        if len(self.l1_cache) >= self.l1_max_size:
            # Remove oldest entry (simple LRU)
            oldest_key = next(iter(self.l1_cache))
            del self.l1_cache[oldest_key]

        self.l1_cache[key] = value
```

## Implementation Strategies

### Query Result Caching
```python
# Decorator-based query caching
import functools
import time

def cache_query(ttl=300, key_prefix="query"):
    """Decorator for caching query results"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}:{func.__name__}:{hash(str(args) + str(kwargs))}"

            # Try cache
            cached_result = cache.get(cache_key)
            if cached_result:
                return cached_result

            # Execute function
            result = func(*args, **kwargs)

            # Cache result
            cache.set(cache_key, result, ttl)

            return result
        return wrapper
    return decorator

# Usage
@cache_query(ttl=600, key_prefix="products")
def get_products_by_category(category_id, limit=10):
    return database.execute("""
        SELECT id, name, price, rating
        FROM products
        WHERE category_id = %s
        ORDER BY rating DESC
        LIMIT %s
    """, (category_id, limit))

@cache_query(ttl=3600, key_prefix="stats")
def get_daily_sales_stats(date):
    return database.execute("""
        SELECT
            COUNT(*) as order_count,
            SUM(total_amount) as total_revenue,
            AVG(total_amount) as avg_order_value
        FROM orders
        WHERE DATE(created_at) = %s
    """, (date,))
```

### Prepared Statement Caching
```python
# Cache prepared statements for better performance
class PreparedStatementCache:
    def __init__(self, connection):
        self.connection = connection
        self.statement_cache = {}
        self.max_cache_size = 100

    def execute(self, query, params=None):
        """Execute query with prepared statement caching"""
        # Generate statement key
        stmt_key = hashlib.md5(query.encode()).hexdigest()

        # Get or create prepared statement
        if stmt_key not in self.statement_cache:
            if len(self.statement_cache) >= self.max_cache_size:
                # Remove oldest statement
                oldest_key = next(iter(self.statement_cache))
                self.statement_cache[oldest_key].close()
                del self.statement_cache[oldest_key]

            # Prepare statement
            self.statement_cache[stmt_key] = self.connection.prepare(query)

        # Execute prepared statement
        stmt = self.statement_cache[stmt_key]
        return stmt.execute(params or [])

# Usage
stmt_cache = PreparedStatementCache(connection)

# These will reuse prepared statements
result1 = stmt_cache.execute("SELECT * FROM users WHERE id = ?", [123])
result2 = stmt_cache.execute("SELECT * FROM users WHERE id = ?", [456])
```

### Materialized View Caching
```sql
-- PostgreSQL: Materialized views for complex aggregations
CREATE MATERIALIZED VIEW daily_sales_summary AS
SELECT
    DATE(created_at) as sale_date,
    COUNT(*) as order_count,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_order_value,
    COUNT(DISTINCT customer_id) as unique_customers
FROM orders
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY sale_date DESC;

-- Create index for fast access
CREATE INDEX idx_daily_sales_summary_date ON daily_sales_summary(sale_date);

-- Refresh materialized view (can be automated)
REFRESH MATERIALIZED VIEW daily_sales_summary;

-- Query the materialized view (much faster than original query)
SELECT * FROM daily_sales_summary WHERE sale_date >= '2024-01-01';
```

## Cache Invalidation

### Time-Based Invalidation (TTL)
```python
# Simple TTL-based invalidation
class TTLCache:
    def __init__(self):
        self.cache = {}
        self.expiry_times = {}

    def set(self, key, value, ttl=300):
        """Set value with TTL"""
        self.cache[key] = value
        self.expiry_times[key] = time.time() + ttl

    def get(self, key):
        """Get value, checking expiry"""
        if key not in self.cache:
            return None

        if time.time() > self.expiry_times[key]:
            # Expired, remove from cache
            del self.cache[key]
            del self.expiry_times[key]
            return None

        return self.cache[key]

    def cleanup_expired(self):
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, expiry_time in self.expiry_times.items()
            if current_time > expiry_time
        ]

        for key in expired_keys:
            del self.cache[key]
            del self.expiry_times[key]
```

### Event-Based Invalidation
```python
# Invalidate cache based on data changes
class EventBasedCache:
    def __init__(self):
        self.cache = QueryCache()
        self.tag_mappings = {}  # Maps cache keys to tags

    def set_with_tags(self, key, value, tags, ttl=300):
        """Set cache value with associated tags"""
        self.cache.set(key, None, value, ttl)

        # Map tags to cache key
        for tag in tags:
            if tag not in self.tag_mappings:
                self.tag_mappings[tag] = set()
            self.tag_mappings[tag].add(key)

    def invalidate_by_tag(self, tag):
        """Invalidate all cache entries with specific tag"""
        if tag in self.tag_mappings:
            keys_to_invalidate = self.tag_mappings[tag]

            # Remove from cache
            for key in keys_to_invalidate:
                self.cache.redis_client.delete(key)

            # Clean up tag mappings
            del self.tag_mappings[tag]

    def on_data_change(self, table_name, operation, record_id=None):
        """Handle data change events"""
        # Invalidate related caches
        if table_name == 'products':
            self.invalidate_by_tag('products')
            if record_id:
                self.invalidate_by_tag(f'product:{record_id}')
        elif table_name == 'orders':
            self.invalidate_by_tag('orders')
            self.invalidate_by_tag('sales_stats')

# Usage
event_cache = EventBasedCache()

# Cache with tags
def get_product_details(product_id):
    cache_key = f"product_details:{product_id}"

    cached_result = event_cache.cache.get(cache_key)
    if cached_result:
        return cached_result

    result = database.execute("SELECT * FROM products WHERE id = %s", product_id)

    # Cache with relevant tags
    event_cache.set_with_tags(
        cache_key,
        result,
        tags=['products', f'product:{product_id}'],
        ttl=3600
    )

    return result

# Invalidate when product is updated
def update_product(product_id, updates):
    database.execute("UPDATE products SET ... WHERE id = %s", product_id)

    # Invalidate related caches
    event_cache.on_data_change('products', 'update', product_id)
```

### Write-Through and Write-Behind Caching
```python
# Write-through caching: Update cache and database simultaneously
class WriteThroughCache:
    def __init__(self, database, cache):
        self.db = database
        self.cache = cache

    def get(self, key):
        """Get from cache first, then database"""
        # Try cache
        cached_value = self.cache.get(key)
        if cached_value:
            return cached_value

        # Load from database
        value = self.db.get(key)
        if value:
            self.cache.set(key, value)

        return value

    def set(self, key, value):
        """Update both cache and database"""
        # Update database first
        self.db.set(key, value)

        # Update cache
        self.cache.set(key, value)

    def delete(self, key):
        """Delete from both cache and database"""
        self.db.delete(key)
        self.cache.delete(key)

# Write-behind caching: Update cache immediately, database asynchronously
import queue
import threading

class WriteBehindCache:
    def __init__(self, database, cache):
        self.db = database
        self.cache = cache
        self.write_queue = queue.Queue()
        self.writer_thread = threading.Thread(target=self._writer_loop)
        self.writer_thread.daemon = True
        self.writer_thread.start()

    def set(self, key, value):
        """Update cache immediately, queue database update"""
        # Update cache immediately
        self.cache.set(key, value)

        # Queue database update
        self.write_queue.put(('set', key, value))

    def delete(self, key):
        """Delete from cache immediately, queue database delete"""
        self.cache.delete(key)
        self.write_queue.put(('delete', key, None))

    def _writer_loop(self):
        """Background thread to process database writes"""
        while True:
            try:
                operation, key, value = self.write_queue.get(timeout=1)

                if operation == 'set':
                    self.db.set(key, value)
                elif operation == 'delete':
                    self.db.delete(key)

                self.write_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"Error in write-behind: {e}")
```

## Database-Specific Caching

### PostgreSQL Caching
```sql
-- PostgreSQL: Configure shared buffers and caching
-- postgresql.conf
shared_buffers = 256MB              -- Buffer pool size
effective_cache_size = 1GB          -- OS cache estimate
work_mem = 4MB                      -- Per-operation memory
maintenance_work_mem = 64MB         -- Maintenance operations

-- Monitor buffer cache hit ratio
SELECT
    sum(heap_blks_read) as heap_read,
    sum(heap_blks_hit) as heap_hit,
    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as ratio
FROM pg_statio_user_tables;

-- Check most accessed tables
SELECT
    schemaname,
    tablename,
    heap_blks_read,
    heap_blks_hit,
    heap_blks_hit / (heap_blks_hit + heap_blks_read + 1) as hit_ratio
FROM pg_statio_user_tables
WHERE heap_blks_read > 0
ORDER BY heap_blks_read DESC;
```

### SQL Server Caching
```sql
-- SQL Server: Buffer pool and plan cache
-- Check buffer pool usage
SELECT
    COUNT(*) * 8 / 1024 as buffer_pool_mb,
    database_id,
    DB_NAME(database_id) as database_name
FROM sys.dm_os_buffer_descriptors
GROUP BY database_id
ORDER BY buffer_pool_mb DESC;

-- Check plan cache
SELECT
    cp.objtype,
    COUNT(*) as plan_count,
    SUM(cp.size_in_bytes) / 1024 / 1024 as size_mb,
    AVG(cp.usecounts) as avg_use_count
FROM sys.dm_exec_cached_plans cp
GROUP BY cp.objtype
ORDER BY size_mb DESC;

-- Clear plan cache (use carefully)
DBCC FREEPROCCACHE;

-- Clear buffer pool (use carefully)
DBCC DROPCLEANBUFFERS;
```

### MySQL Caching
```sql
-- MySQL: InnoDB buffer pool and query cache
-- my.cnf
innodb_buffer_pool_size = 1G        -- 70-80% of RAM
innodb_buffer_pool_instances = 8    -- Multiple instances for concurrency

-- Check buffer pool status
SHOW STATUS LIKE 'Innodb_buffer_pool%';

-- Calculate buffer pool hit ratio
SELECT
    (1 - (Innodb_buffer_pool_reads / Innodb_buffer_pool_read_requests)) * 100
    as buffer_pool_hit_ratio
FROM (
    SELECT
        VARIABLE_VALUE as Innodb_buffer_pool_reads
    FROM information_schema.GLOBAL_STATUS
    WHERE VARIABLE_NAME = 'Innodb_buffer_pool_reads'
) reads,
(
    SELECT
        VARIABLE_VALUE as Innodb_buffer_pool_read_requests
    FROM information_schema.GLOBAL_STATUS
    WHERE VARIABLE_NAME = 'Innodb_buffer_pool_read_requests'
) requests;
```

## Application-Level Caching

### ORM-Level Caching
```python
# SQLAlchemy with Redis caching
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import redis

Base = declarative_base()

class CachedSession:
    def __init__(self, session, cache_client):
        self.session = session
        self.cache = cache_client
        self.default_ttl = 300

    def query_with_cache(self, model, cache_key, ttl=None):
        """Query with automatic caching"""
        ttl = ttl or self.default_ttl

        # Try cache first
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return json.loads(cached_result)

        # Query database
        result = self.session.query(model).all()

        # Serialize and cache
        serialized_result = [
            {column.name: getattr(row, column.name) for column in row.__table__.columns}
            for row in result
        ]

        self.cache.setex(cache_key, ttl, json.dumps(serialized_result, default=str))

        return serialized_result

# Usage with Flask
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

app = Flask(__name__)
db = SQLAlchemy(app)
cache_client = redis.Redis()

@app.route('/products/<category>')
def get_products_by_category(category):
    cache_key = f"products:category:{category}"

    cached_session = CachedSession(db.session, cache_client)
    products = cached_session.query_with_cache(
        Product.query.filter_by(category=category),
        cache_key,
        ttl=600
    )

    return jsonify(products)
```

### API Response Caching
```python
# Flask with response caching
from flask import Flask, request, jsonify
from functools import wraps
import hashlib

app = Flask(__name__)

def cache_response(ttl=300):
    """Decorator for caching API responses"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key from request
            cache_key = generate_request_cache_key(request)

            # Try cache
            cached_response = cache.get(cache_key)
            if cached_response:
                return json.loads(cached_response)

            # Execute function
            response = func(*args, **kwargs)

            # Cache response
            cache.set(cache_key, json.dumps(response.get_json()), ttl)

            return response
        return wrapper
    return decorator

def generate_request_cache_key(request):
    """Generate cache key from request parameters"""
    key_data = f"{request.endpoint}:{request.args.to_dict()}:{request.view_args}"
    return hashlib.md5(key_data.encode()).hexdigest()

@app.route('/api/products')
@cache_response(ttl=600)  # Cache for 10 minutes
def get_products():
    category = request.args.get('category')
    limit = request.args.get('limit', 10)

    products = database.execute("""
        SELECT id, name, price, rating
        FROM products
        WHERE category = %s
        ORDER BY rating DESC
        LIMIT %s
    """, (category, limit))

    return jsonify(products)
```

## Monitoring and Optimization

### Cache Performance Metrics
```python
# Cache performance monitoring
class CacheMonitor:
    def __init__(self, cache_client):
        self.cache = cache_client
        self.metrics = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }

    def get(self, key):
        """Get with metrics tracking"""
        try:
            value = self.cache.get(key)
            if value:
                self.metrics['hits'] += 1
            else:
                self.metrics['misses'] += 1
            return value
        except Exception as e:
            self.metrics['errors'] += 1
            raise

    def set(self, key, value, ttl=None):
        """Set with metrics tracking"""
        try:
            result = self.cache.setex(key, ttl or 300, value)
            self.metrics['sets'] += 1
            return result
        except Exception as e:
            self.metrics['errors'] += 1
            raise

    def get_hit_ratio(self):
        """Calculate cache hit ratio"""
        total_requests = self.metrics['hits'] + self.metrics['misses']
        if total_requests == 0:
            return 0
        return (self.metrics['hits'] / total_requests) * 100

    def get_stats(self):
        """Get comprehensive cache statistics"""
        return {
            'hit_ratio': self.get_hit_ratio(),
            'total_requests': self.metrics['hits'] + self.metrics['misses'],
            'metrics': self.metrics.copy()
        }

# Usage
monitored_cache = CacheMonitor(redis.Redis())

# Regular monitoring
def log_cache_stats():
    stats = monitored_cache.get_stats()
    print(f"Cache hit ratio: {stats['hit_ratio']:.2f}%")
    print(f"Total requests: {stats['total_requests']}")
    print(f"Metrics: {stats['metrics']}")

# Schedule regular monitoring
import schedule
schedule.every(5).minutes.do(log_cache_stats)
```

### Cache Optimization Strategies
```python
# Adaptive cache sizing based on hit ratios
class AdaptiveCache:
    def __init__(self, initial_ttl=300):
        self.cache = redis.Redis()
        self.ttl_stats = {}  # Track hit ratios by TTL
        self.default_ttl = initial_ttl

    def set_with_adaptive_ttl(self, key, value, base_ttl=None):
        """Set cache value with adaptive TTL based on access patterns"""
        base_ttl = base_ttl or self.default_ttl

        # Analyze access patterns for this key type
        key_type = self._get_key_type(key)
        optimal_ttl = self._calculate_optimal_ttl(key_type, base_ttl)

        self.cache.setex(key, optimal_ttl, value)

        # Track TTL usage
        if key_type not in self.ttl_stats:
            self.ttl_stats[key_type] = {'ttl': optimal_ttl, 'hits': 0, 'sets': 1}
        else:
            self.ttl_stats[key_type]['sets'] += 1

    def _get_key_type(self, key):
        """Extract key type from cache key"""
        return key.split(':')[0] if ':' in key else 'default'

    def _calculate_optimal_ttl(self, key_type, base_ttl):
        """Calculate optimal TTL based on access patterns"""
        if key_type not in self.ttl_stats:
            return base_ttl

        stats = self.ttl_stats[key_type]
        hit_ratio = stats['hits'] / max(stats['sets'], 1)

        # Increase TTL for frequently accessed data
        if hit_ratio > 0.8:
            return min(base_ttl * 2, 3600)  # Max 1 hour
        elif hit_ratio < 0.3:
            return max(base_ttl // 2, 60)   # Min 1 minute

        return base_ttl

# Cache warming strategies
def warm_cache():
    """Pre-populate cache with frequently accessed data"""
    # Popular products
    popular_products = database.execute("""
        SELECT id, name, price, rating
        FROM products
        WHERE rating > 4.5
        ORDER BY sales_count DESC
        LIMIT 100
    """)

    for product in popular_products:
        cache_key = f"product:{product['id']}"
        cache.set(cache_key, json.dumps(product), ttl=3600)

    # Recent orders for active users
    recent_orders = database.execute("""
        SELECT DISTINCT customer_id
        FROM orders
        WHERE created_at >= NOW() - INTERVAL '7 days'
    """)

    for customer in recent_orders:
        cache_key = f"customer_orders:{customer['customer_id']}"
        orders = get_customer_orders(customer['customer_id'])
        cache.set(cache_key, json.dumps(orders), ttl=1800)

# Schedule cache warming
schedule.every().day.at("02:00").do(warm_cache)
```

## Best Practices Summary

### Cache Design Principles
- **Cache frequently accessed data** with stable content
- **Use appropriate TTL values** based on data volatility
- **Implement proper invalidation strategies**
- **Monitor cache hit ratios** and adjust accordingly
- **Consider cache warming** for critical data

### Performance Optimization
- **Use compression** for large cached values
- **Implement cache hierarchies** (L1, L2, L3)
- **Batch cache operations** when possible
- **Use connection pooling** for cache clients
- **Monitor memory usage** and implement eviction policies

### Reliability and Consistency
- **Handle cache failures gracefully**
- **Implement circuit breakers** for cache operations
- **Use write-through caching** for critical data
- **Test cache invalidation** thoroughly
- **Plan for cache recovery** scenarios

## 🚀 Next Steps

After mastering query caching:
- [Buffer Pool Optimization](./buffer-pool.md) - Optimize database memory usage
- [I/O Optimization](./io-optimization.md) - Optimize storage performance
- [Memory Management](./memory-management.md) - Comprehensive memory optimization

## 📝 Practice Exercises

1. Implement a multi-level caching system
2. Create an adaptive TTL caching strategy
3. Build a cache warming system for your application
4. Implement event-based cache invalidation
5. Design a cache monitoring and alerting system

Remember: Effective caching requires understanding your data access patterns and balancing cache hit ratios with data freshness requirements!