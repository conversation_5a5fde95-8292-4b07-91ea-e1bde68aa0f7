# Connection Pooling

Connection pooling is a technique that maintains a cache of database connections that can be reused across multiple requests. This approach significantly improves application performance by eliminating the overhead of establishing and tearing down connections for each database operation.

## 📋 Table of Contents

1. [Connection Pooling Fundamentals](#connection-pooling-fundamentals)
2. [Benefits and Trade-offs](#benefits-and-trade-offs)
3. [Pool Configuration](#pool-configuration)
4. [Implementation Examples](#implementation-examples)
5. [Monitoring and Tuning](#monitoring-and-tuning)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

## Connection Pooling Fundamentals

### What is Connection Pooling?
Connection pooling maintains a pool of pre-established database connections that applications can borrow, use, and return. This eliminates the need to create new connections for each database operation.

### Connection Lifecycle
```
Pool Creation → Connection Establishment → Connection Borrowing →
Query Execution → Connection Return → Connection Reuse → Pool Cleanup
```

### Why Connection Pooling Matters
- **Connection Overhead**: Creating TCP connections and database authentication is expensive
- **Resource Limits**: Databases have maximum connection limits
- **Performance**: Reusing connections is much faster than creating new ones
- **Scalability**: Enables handling more concurrent users with fewer resources

## Benefits and Trade-offs

### Benefits
```python
# Without connection pooling (inefficient)
def get_user_data(user_id):
    connection = create_database_connection()  # Expensive operation
    try:
        cursor = connection.cursor()
        cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
        return cursor.fetchone()
    finally:
        connection.close()  # Destroys connection

# With connection pooling (efficient)
def get_user_data(user_id):
    connection = pool.get_connection()  # Fast operation
    try:
        cursor = connection.cursor()
        cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
        return cursor.fetchone()
    finally:
        pool.return_connection(connection)  # Returns to pool
```

### Performance Improvements
- **Reduced Latency**: 50-90% reduction in connection establishment time
- **Higher Throughput**: More requests handled per second
- **Lower CPU Usage**: Less overhead from connection management
- **Better Resource Utilization**: Optimal use of database connections

### Trade-offs
- **Memory Usage**: Pool maintains connections in memory
- **Complexity**: Additional configuration and monitoring required
- **Connection Leaks**: Risk of not returning connections to pool
- **Stale Connections**: Need to handle disconnected connections

## Pool Configuration

### Key Configuration Parameters

#### Pool Size Settings
```python
# Example configuration
pool_config = {
    'min_connections': 5,      # Minimum connections to maintain
    'max_connections': 20,     # Maximum connections allowed
    'initial_connections': 10,  # Connections created at startup
    'increment': 2,            # Connections added when pool grows
    'decrement': 1,            # Connections removed when pool shrinks
}
```

#### Timeout Settings
```python
timeout_config = {
    'connection_timeout': 30,     # Seconds to wait for available connection
    'idle_timeout': 300,          # Seconds before idle connection is closed
    'max_lifetime': 3600,         # Maximum connection lifetime (seconds)
    'validation_timeout': 5,      # Seconds to wait for connection validation
}
```

#### Health Check Settings
```python
health_config = {
    'test_on_borrow': True,       # Validate connection before use
    'test_on_return': False,      # Validate connection when returned
    'test_while_idle': True,      # Validate idle connections
    'validation_query': 'SELECT 1', # Query to test connection health
    'validation_interval': 60,    # Seconds between idle connection tests
}
```

## Implementation Examples

### Python with psycopg2 (PostgreSQL)
```python
import psycopg2
from psycopg2 import pool
import threading
import time

class PostgreSQLConnectionPool:
    def __init__(self, min_conn=5, max_conn=20, **db_params):
        self.pool = psycopg2.pool.ThreadedConnectionPool(
            min_conn, max_conn, **db_params
        )
        self.lock = threading.Lock()

    def get_connection(self, timeout=30):
        """Get connection from pool with timeout"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with self.lock:
                    conn = self.pool.getconn()
                    if conn:
                        return conn
            except psycopg2.pool.PoolError:
                time.sleep(0.1)  # Brief wait before retry

        raise TimeoutError("Could not get connection from pool")

    def return_connection(self, conn):
        """Return connection to pool"""
        with self.lock:
            self.pool.putconn(conn)

    def close_all(self):
        """Close all connections in pool"""
        self.pool.closeall()

# Usage
pool = PostgreSQLConnectionPool(
    min_conn=5,
    max_conn=20,
    host='localhost',
    database='myapp',
    user='user',
    password='password'
)

def execute_query(sql, params=None):
    conn = pool.get_connection()
    try:
        with conn.cursor() as cursor:
            cursor.execute(sql, params)
            if sql.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            conn.commit()
    except Exception:
        conn.rollback()
        raise
    finally:
        pool.return_connection(conn)
```

### Java with HikariCP
```java
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import java.sql.Connection;
import java.sql.SQLException;

public class DatabaseConnectionPool {
    private HikariDataSource dataSource;

    public DatabaseConnectionPool() {
        HikariConfig config = new HikariConfig();

        // Database connection settings
        config.setJdbcUrl("**************************************");
        config.setUsername("user");
        config.setPassword("password");

        // Pool settings
        config.setMinimumIdle(5);
        config.setMaximumPoolSize(20);
        config.setConnectionTimeout(30000);      // 30 seconds
        config.setIdleTimeout(300000);           // 5 minutes
        config.setMaxLifetime(1800000);          // 30 minutes

        // Health check settings
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(5000);       // 5 seconds

        // Performance settings
        config.setLeakDetectionThreshold(60000); // 1 minute
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");

        this.dataSource = new HikariDataSource(config);
    }

    public Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }

    public void close() {
        if (dataSource != null) {
            dataSource.close();
        }
    }

    // Usage example
    public List<User> getUsers() throws SQLException {
        String sql = "SELECT id, name, email FROM users";
        List<User> users = new ArrayList<>();

        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                users.add(new User(
                    rs.getInt("id"),
                    rs.getString("name"),
                    rs.getString("email")
                ));
            }
        }

        return users;
    }
}
```

### Node.js with node-postgres
```javascript
const { Pool } = require('pg');

class DatabasePool {
    constructor() {
        this.pool = new Pool({
            host: 'localhost',
            port: 5432,
            database: 'myapp',
            user: 'user',
            password: 'password',

            // Pool settings
            min: 5,                    // Minimum connections
            max: 20,                   // Maximum connections
            idleTimeoutMillis: 300000, // 5 minutes
            connectionTimeoutMillis: 30000, // 30 seconds

            // Health check
            allowExitOnIdle: true,
        });

        // Error handling
        this.pool.on('error', (err) => {
            console.error('Unexpected error on idle client', err);
        });

        // Connection monitoring
        this.pool.on('connect', () => {
            console.log('New client connected');
        });

        this.pool.on('remove', () => {
            console.log('Client removed');
        });
    }

    async query(text, params) {
        const client = await this.pool.connect();
        try {
            const result = await client.query(text, params);
            return result;
        } finally {
            client.release(); // Return connection to pool
        }
    }

    async transaction(callback) {
        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');
            const result = await callback(client);
            await client.query('COMMIT');
            return result;
        } catch (error) {
            await client.query('ROLLBACK');
            throw error;
        } finally {
            client.release();
        }
    }

    async close() {
        await this.pool.end();
    }

    getPoolInfo() {
        return {
            totalCount: this.pool.totalCount,
            idleCount: this.pool.idleCount,
            waitingCount: this.pool.waitingCount
        };
    }
}

// Usage
const db = new DatabasePool();

async function getUser(userId) {
    try {
        const result = await db.query(
            'SELECT * FROM users WHERE id = $1',
            [userId]
        );
        return result.rows[0];
    } catch (error) {
        console.error('Error fetching user:', error);
        throw error;
    }
}

async function transferMoney(fromAccount, toAccount, amount) {
    return await db.transaction(async (client) => {
        // Debit from account
        await client.query(
            'UPDATE accounts SET balance = balance - $1 WHERE id = $2',
            [amount, fromAccount]
        );

        // Credit to account
        await client.query(
            'UPDATE accounts SET balance = balance + $1 WHERE id = $2',
            [amount, toAccount]
        );

        return { success: true };
    });
}
```

### C# with Entity Framework
```csharp
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

public class DatabaseContextFactory
{
    public static void ConfigureServices(IServiceCollection services, string connectionString)
    {
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.CommandTimeout(30);
            });
        }, ServiceLifetime.Scoped);

        // Configure connection pooling
        services.AddDbContextPool<ApplicationDbContext>(options =>
        {
            options.UseNpgsql(connectionString);
        }, poolSize: 128); // Maximum pool size
    }
}

public class UserService
{
    private readonly ApplicationDbContext _context;

    public UserService(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<User> GetUserAsync(int userId)
    {
        return await _context.Users
            .FirstOrDefaultAsync(u => u.Id == userId);
    }

    public async Task<bool> TransferMoneyAsync(int fromAccount, int toAccount, decimal amount)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();

        try
        {
            var fromAcc = await _context.Accounts.FindAsync(fromAccount);
            var toAcc = await _context.Accounts.FindAsync(toAccount);

            fromAcc.Balance -= amount;
            toAcc.Balance += amount;

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            return true;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }
}
```

## Monitoring and Tuning

### Pool Metrics to Monitor
```python
class PoolMonitor:
    def __init__(self, pool):
        self.pool = pool
        self.metrics = {
            'connections_created': 0,
            'connections_closed': 0,
            'connections_borrowed': 0,
            'connections_returned': 0,
            'wait_time_total': 0,
            'wait_count': 0,
        }

    def get_pool_stats(self):
        return {
            'active_connections': self.pool.active_count,
            'idle_connections': self.pool.idle_count,
            'total_connections': self.pool.total_count,
            'waiting_requests': self.pool.waiting_count,
            'average_wait_time': self.get_average_wait_time(),
            'pool_utilization': self.get_pool_utilization(),
        }

    def get_average_wait_time(self):
        if self.metrics['wait_count'] == 0:
            return 0
        return self.metrics['wait_time_total'] / self.metrics['wait_count']

    def get_pool_utilization(self):
        total = self.pool.total_count
        if total == 0:
            return 0
        return (self.pool.active_count / total) * 100

# Monitoring with logging
import logging
import time

class MonitoredConnectionPool:
    def __init__(self, pool):
        self.pool = pool
        self.logger = logging.getLogger(__name__)

    def get_connection(self):
        start_time = time.time()
        try:
            conn = self.pool.get_connection()
            wait_time = time.time() - start_time

            if wait_time > 1.0:  # Log slow connection acquisitions
                self.logger.warning(f"Slow connection acquisition: {wait_time:.2f}s")

            return conn
        except Exception as e:
            self.logger.error(f"Failed to get connection: {e}")
            raise

    def log_pool_stats(self):
        stats = self.get_pool_stats()
        self.logger.info(f"Pool stats: {stats}")

        # Alert on high utilization
        if stats['pool_utilization'] > 80:
            self.logger.warning(f"High pool utilization: {stats['pool_utilization']:.1f}%")
```

### Performance Tuning Guidelines
```python
def calculate_optimal_pool_size(concurrent_users, avg_request_time, requests_per_user_per_second):
    """
    Calculate optimal pool size based on Little's Law:
    Pool Size = Arrival Rate × Service Time
    """
    arrival_rate = concurrent_users * requests_per_user_per_second
    service_time = avg_request_time  # in seconds

    optimal_size = arrival_rate * service_time

    # Add buffer for spikes (20-50%)
    buffer_factor = 1.3
    recommended_size = int(optimal_size * buffer_factor)

    return {
        'calculated_size': optimal_size,
        'recommended_size': recommended_size,
        'min_size': max(5, recommended_size // 4),
        'max_size': recommended_size * 2
    }

# Example calculation
sizing = calculate_optimal_pool_size(
    concurrent_users=1000,
    avg_request_time=0.1,  # 100ms average
    requests_per_user_per_second=2
)
print(f"Recommended pool size: {sizing['recommended_size']}")
```

## Best Practices

### 1. Proper Connection Management
```python
# Good: Always return connections
def good_database_operation():
    conn = pool.get_connection()
    try:
        # Database operations
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users")
        return cursor.fetchall()
    finally:
        pool.return_connection(conn)  # Always return

# Better: Use context managers
from contextlib import contextmanager

@contextmanager
def get_db_connection():
    conn = pool.get_connection()
    try:
        yield conn
    finally:
        pool.return_connection(conn)

def better_database_operation():
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users")
        return cursor.fetchall()

# Best: Framework-provided connection management
def best_database_operation():
    # Let the framework handle connection management
    return db.session.query(User).all()
```

### 2. Handle Connection Failures
```python
def robust_database_operation(max_retries=3):
    for attempt in range(max_retries):
        conn = None
        try:
            conn = pool.get_connection(timeout=10)

            # Test connection health
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()

            # Perform actual operation
            cursor.execute("SELECT * FROM users")
            return cursor.fetchall()

        except (ConnectionError, TimeoutError) as e:
            if conn:
                pool.return_connection(conn, force_close=True)

            if attempt == max_retries - 1:
                raise

            time.sleep(0.5 * (attempt + 1))  # Exponential backoff

        except Exception as e:
            if conn:
                pool.return_connection(conn)
            raise
        finally:
            if conn:
                pool.return_connection(conn)
```

### 3. Monitor Pool Health
```python
def monitor_pool_health():
    """Regular health check for connection pool"""
    stats = pool.get_stats()

    # Check for connection leaks
    if stats['active_connections'] > stats['total_connections'] * 0.9:
        logger.warning("Possible connection leak detected")

    # Check for pool exhaustion
    if stats['waiting_requests'] > 0:
        logger.warning(f"Pool exhaustion: {stats['waiting_requests']} waiting")

    # Check for stale connections
    if stats['idle_connections'] == stats['total_connections']:
        logger.info("All connections idle - consider reducing pool size")

    return stats

# Schedule regular monitoring
import schedule
schedule.every(1).minutes.do(monitor_pool_health)
```

### 4. Environment-Specific Configuration
```python
import os

def get_pool_config():
    """Get pool configuration based on environment"""
    env = os.getenv('ENVIRONMENT', 'development')

    configs = {
        'development': {
            'min_connections': 2,
            'max_connections': 5,
            'connection_timeout': 10,
        },
        'testing': {
            'min_connections': 1,
            'max_connections': 3,
            'connection_timeout': 5,
        },
        'production': {
            'min_connections': 10,
            'max_connections': 50,
            'connection_timeout': 30,
            'idle_timeout': 300,
            'max_lifetime': 3600,
        }
    }

    return configs.get(env, configs['development'])
```

## Troubleshooting

### Common Issues and Solutions

#### Connection Leaks
```python
# Problem: Connections not returned to pool
def problematic_code():
    conn = pool.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users")
    return cursor.fetchall()
    # Connection never returned!

# Solution: Always use try/finally or context managers
def fixed_code():
    with pool.get_connection() as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users")
        return cursor.fetchall()
    # Connection automatically returned
```

#### Pool Exhaustion
```python
# Detect pool exhaustion
def check_pool_exhaustion():
    stats = pool.get_stats()
    if stats['waiting_requests'] > 0:
        logger.error(f"Pool exhausted: {stats['waiting_requests']} requests waiting")

        # Emergency actions
        if stats['waiting_requests'] > 10:
            # Force close idle connections
            pool.force_close_idle_connections()

            # Increase pool size temporarily
            pool.increase_max_size(stats['max_connections'] + 5)
```

#### Stale Connections
```python
# Handle stale connections
def validate_connection(conn):
    """Validate connection before use"""
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        return True
    except Exception:
        return False

def get_validated_connection():
    """Get connection with validation"""
    max_attempts = 3
    for attempt in range(max_attempts):
        conn = pool.get_connection()

        if validate_connection(conn):
            return conn
        else:
            # Close stale connection and try again
            pool.return_connection(conn, force_close=True)

            if attempt == max_attempts - 1:
                raise ConnectionError("Could not get valid connection")
```

### Debugging Tools
```python
class PoolDebugger:
    def __init__(self, pool):
        self.pool = pool
        self.connection_history = []

    def trace_connection_usage(self):
        """Trace connection borrowing and returning"""
        original_get = self.pool.get_connection
        original_return = self.pool.return_connection

        def traced_get(*args, **kwargs):
            conn = original_get(*args, **kwargs)
            self.connection_history.append({
                'action': 'borrowed',
                'connection_id': id(conn),
                'timestamp': time.time(),
                'stack_trace': traceback.format_stack()
            })
            return conn

        def traced_return(conn, *args, **kwargs):
            self.connection_history.append({
                'action': 'returned',
                'connection_id': id(conn),
                'timestamp': time.time()
            })
            return original_return(conn, *args, **kwargs)

        self.pool.get_connection = traced_get
        self.pool.return_connection = traced_return

    def find_connection_leaks(self):
        """Find connections that were borrowed but never returned"""
        borrowed = {}
        leaks = []

        for event in self.connection_history:
            conn_id = event['connection_id']

            if event['action'] == 'borrowed':
                borrowed[conn_id] = event
            elif event['action'] == 'returned' and conn_id in borrowed:
                del borrowed[conn_id]

        # Remaining borrowed connections are potential leaks
        for conn_id, event in borrowed.items():
            leaks.append({
                'connection_id': conn_id,
                'borrowed_at': event['timestamp'],
                'stack_trace': event['stack_trace']
            })

        return leaks
```

## 🚀 Next Steps

After mastering connection pooling:
- [Query Caching](./query-caching.md) - Implement effective caching strategies
- [Buffer Pool Optimization](./buffer-pool.md) - Optimize database memory usage
- [I/O Optimization](./io-optimization.md) - Optimize storage performance

## 📝 Practice Exercises

1. Implement a custom connection pool with health checking
2. Create a monitoring system for pool metrics
3. Build a load testing tool to determine optimal pool sizes
4. Implement connection leak detection and prevention
5. Design a multi-database connection pooling system

Remember: Connection pooling is about balancing resource utilization with performance. Monitor your pools closely and tune them based on actual usage patterns!