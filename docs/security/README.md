# Database Security

Database security involves protecting data from unauthorized access, ensuring data integrity, and maintaining compliance with security standards. This section covers comprehensive security strategies from authentication to data protection.

## 📚 Contents

### Core Security Topics
- [Authentication and Authorization](./authentication-authorization.md) - User management and access control
- [Encryption (TDE, Column-level)](./encryption.md) - Data protection at rest and in transit
- [SQL Injection Prevention](./sql-injection-prevention.md) - Protecting against injection attacks
- [Audit Logging](./audit-logging.md) - Tracking database activities for compliance
- [Data Masking and Anonymization](./data-masking.md) - Protecting sensitive data in non-production
- [Row-Level Security](./row-level-security.md) - Fine-grained access control

## 🎯 Learning Path

### Beginner (Start Here)
1. [Authentication and Authorization](./authentication-authorization.md) - Secure user access
2. [SQL Injection Prevention](./sql-injection-prevention.md) - Protect against attacks

### Intermediate
3. [Encryption](./encryption.md) - Protect data at rest and in transit
4. [Audit Logging](./audit-logging.md) - Track database activities

### Advanced
5. [Data Masking and Anonymization](./data-masking.md) - Protect sensitive data
6. [Row-Level Security](./row-level-security.md) - Implement fine-grained security

## 💡 Security Principles

### Defense in Depth
- **Multiple security layers**: Network, application, database, and data level security
- **Principle of least privilege**: Grant minimum necessary permissions
- **Regular security assessments**: Continuous monitoring and testing
- **Security by design**: Build security into the system from the start

### Data Protection
- **Encryption**: Protect data at rest and in transit
- **Access control**: Restrict data access to authorized users
- **Data classification**: Identify and protect sensitive data
- **Data retention**: Implement appropriate data lifecycle policies

### Compliance and Governance
- **Regulatory compliance**: Meet industry-specific requirements (GDPR, HIPAA, SOX)
- **Audit trails**: Maintain comprehensive activity logs
- **Data governance**: Establish data management policies
- **Incident response**: Prepare for security incidents

## 🔧 Security Implementation

### Authentication Methods
```sql
-- PostgreSQL: Create users with different authentication methods
-- Password authentication
CREATE USER app_user WITH PASSWORD 'secure_password';

-- Certificate authentication
CREATE USER cert_user;
-- Configure pg_hba.conf for cert authentication

-- LDAP authentication
-- Configure pg_hba.conf:
-- host all all 0.0.0.0/0 ldap ldapserver=ldap.company.com ldapprefix="uid=" ldapsuffix=",ou=people,dc=company,dc=com"

-- SQL Server: Windows Authentication
CREATE LOGIN [DOMAIN\username] FROM WINDOWS;

-- SQL Server: SQL Authentication
CREATE LOGIN sql_user WITH PASSWORD = 'SecurePassword123!';
```

### Role-Based Access Control
```sql
-- Create role hierarchy
CREATE ROLE app_read;
CREATE ROLE app_write;
CREATE ROLE app_admin;

-- Grant permissions to roles
GRANT SELECT ON ALL TABLES IN SCHEMA public TO app_read;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_write;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_admin;

-- Grant roles to users
GRANT app_read TO readonly_user;
GRANT app_write TO app_user;
GRANT app_admin TO admin_user;

-- Role inheritance
GRANT app_read TO app_write;  -- app_write inherits app_read permissions
GRANT app_write TO app_admin; -- app_admin inherits app_write permissions
```

### Column-Level Security
```sql
-- PostgreSQL: Column-level permissions
GRANT SELECT (customer_id, name, email) ON customers TO app_user;
REVOKE SELECT (ssn, credit_card) ON customers FROM app_user;

-- SQL Server: Column-level permissions
GRANT SELECT ON customers (customer_id, name, email) TO app_user;
DENY SELECT ON customers (ssn, credit_card) TO app_user;
```

## 🛡️ Data Protection

### Encryption at Rest
```sql
-- PostgreSQL: Enable TDE (Transparent Data Encryption)
-- Requires enterprise extensions like EDB TDE

-- SQL Server: Enable TDE
-- Create master key
CREATE MASTER KEY ENCRYPTION BY PASSWORD = 'MasterKeyPassword123!';

-- Create certificate
CREATE CERTIFICATE TDECert WITH SUBJECT = 'TDE Certificate';

-- Create database encryption key
USE myapp;
CREATE DATABASE ENCRYPTION KEY
WITH ALGORITHM = AES_256
ENCRYPTION BY SERVER CERTIFICATE TDECert;

-- Enable TDE
ALTER DATABASE myapp SET ENCRYPTION ON;
```

### Column-Level Encryption
```sql
-- PostgreSQL: Using pgcrypto extension
CREATE EXTENSION pgcrypto;

-- Encrypt sensitive data
INSERT INTO customers (name, email, ssn_encrypted)
VALUES ('John Doe', '<EMAIL>', 
        pgp_sym_encrypt('***********', 'encryption_key'));

-- Decrypt data
SELECT name, email, pgp_sym_decrypt(ssn_encrypted, 'encryption_key') as ssn
FROM customers
WHERE customer_id = 1;

-- SQL Server: Always Encrypted
-- Create column master key
CREATE COLUMN MASTER KEY CMK1
WITH (
    KEY_STORE_PROVIDER_NAME = 'MSSQL_CERTIFICATE_STORE',
    KEY_PATH = 'CurrentUser/My/certificate_thumbprint'
);

-- Create column encryption key
CREATE COLUMN ENCRYPTION KEY CEK1
WITH VALUES (
    COLUMN_MASTER_KEY = CMK1,
    ALGORITHM = 'RSA_OAEP'
);

-- Create table with encrypted columns
CREATE TABLE customers (
    customer_id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100),
    ssn CHAR(11) ENCRYPTED WITH (
        COLUMN_ENCRYPTION_KEY = CEK1,
        ENCRYPTION_TYPE = DETERMINISTIC,
        ALGORITHM = 'AEAD_AES_256_CBC_HMAC_SHA_256'
    )
);
```

### Connection Security
```sql
-- Force SSL connections
-- PostgreSQL: postgresql.conf
ssl = on
ssl_cert_file = 'server.crt'
ssl_key_file = 'server.key'

-- pg_hba.conf: Require SSL
hostssl all all 0.0.0.0/0 md5

-- SQL Server: Force encryption
-- In SQL Server Configuration Manager, set "Force Encryption" to Yes
```

## 🔍 Security Monitoring

### Audit Logging
```sql
-- PostgreSQL: Enable audit logging with pgaudit
CREATE EXTENSION pgaudit;

-- Configure audit settings
SET pgaudit.log = 'all';
SET pgaudit.log_catalog = off;
SET pgaudit.log_parameter = on;

-- SQL Server: Enable auditing
-- Create server audit
CREATE SERVER AUDIT SecurityAudit
TO FILE (FILEPATH = 'C:\Audit\');

-- Create database audit specification
CREATE DATABASE AUDIT SPECIFICATION DatabaseAuditSpec
FOR SERVER AUDIT SecurityAudit
ADD (SELECT, INSERT, UPDATE, DELETE ON SCHEMA::dbo BY public);

-- Enable audit
ALTER SERVER AUDIT SecurityAudit WITH (STATE = ON);
ALTER DATABASE AUDIT SPECIFICATION DatabaseAuditSpec WITH (STATE = ON);
```

### Failed Login Monitoring
```sql
-- PostgreSQL: Monitor failed connections
-- Check PostgreSQL logs for authentication failures

-- SQL Server: Monitor failed logins
SELECT 
    login_time,
    login_name,
    source_ip,
    error_number,
    error_message
FROM sys.dm_exec_sessions s
JOIN sys.dm_exec_connections c ON s.session_id = c.session_id
WHERE s.is_user_process = 1;
```

### Privilege Escalation Detection
```sql
-- Monitor privilege changes
-- PostgreSQL: Track role membership changes
SELECT 
    rolname,
    member,
    grantor,
    admin_option
FROM pg_auth_members m
JOIN pg_roles r ON m.roleid = r.oid
JOIN pg_roles mr ON m.member = mr.oid;

-- SQL Server: Track permission changes
SELECT 
    p.permission_name,
    p.state_desc,
    pr.name as principal_name,
    o.name as object_name
FROM sys.database_permissions p
JOIN sys.objects o ON p.major_id = o.object_id
JOIN sys.database_principals pr ON p.grantee_principal_id = pr.principal_id;
```

## 🚨 Threat Prevention

### SQL Injection Prevention
```python
# Bad: Vulnerable to SQL injection
def get_user(user_id):
    query = f"SELECT * FROM users WHERE user_id = {user_id}"
    return execute_query(query)

# Good: Using parameterized queries
def get_user(user_id):
    query = "SELECT * FROM users WHERE user_id = %s"
    return execute_query(query, (user_id,))

# Good: Using ORM
def get_user(user_id):
    return User.objects.filter(user_id=user_id).first()
```

### Input Validation
```python
import re

def validate_email(email):
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_user_input(data):
    # Whitelist validation
    allowed_chars = re.compile(r'^[a-zA-Z0-9\s\-_.]+$')
    
    for field, value in data.items():
        if not allowed_chars.match(str(value)):
            raise ValueError(f"Invalid characters in {field}")
    
    return True
```

### Access Control Implementation
```sql
-- Implement time-based access control
CREATE OR REPLACE FUNCTION check_business_hours()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXTRACT(HOUR FROM CURRENT_TIME) BETWEEN 8 AND 18
       AND EXTRACT(DOW FROM CURRENT_DATE) BETWEEN 1 AND 5;
END;
$$ LANGUAGE plpgsql;

-- Create policy for business hours access
CREATE POLICY business_hours_policy ON sensitive_table
FOR ALL TO app_user
USING (check_business_hours());

-- IP-based access control
CREATE OR REPLACE FUNCTION check_allowed_ip()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN inet_client_addr() << '10.0.0.0/8'::inet
        OR inet_client_addr() << '***********/16'::inet;
END;
$$ LANGUAGE plpgsql;
```

## 📋 Security Checklist

### Authentication and Authorization
- [ ] Strong password policies implemented
- [ ] Multi-factor authentication enabled
- [ ] Role-based access control configured
- [ ] Principle of least privilege applied
- [ ] Regular access reviews conducted

### Data Protection
- [ ] Encryption at rest enabled
- [ ] Encryption in transit configured
- [ ] Sensitive data identified and protected
- [ ] Data masking implemented for non-production
- [ ] Backup encryption enabled

### Network Security
- [ ] Database server properly firewalled
- [ ] SSL/TLS connections enforced
- [ ] VPN or private networks used
- [ ] Database ports not exposed to internet
- [ ] Network segmentation implemented

### Monitoring and Auditing
- [ ] Audit logging enabled
- [ ] Failed login attempts monitored
- [ ] Privilege changes tracked
- [ ] Security events alerted
- [ ] Regular security assessments conducted

### Compliance
- [ ] Regulatory requirements identified
- [ ] Data retention policies implemented
- [ ] Privacy controls in place
- [ ] Incident response plan prepared
- [ ] Regular compliance audits conducted

## 🔗 Related Topics

After mastering database security:
- [Monitoring](../monitoring/README.md) - Security monitoring and alerting
- [Backup and Recovery](../maintenance/backup-recovery.md) - Secure backup practices
- [Compliance](../operations/compliance.md) - Regulatory compliance
- [Database-Specific Security](../databases/) - Platform-specific security features

Start with [Authentication and Authorization](./authentication-authorization.md) to secure user access, then explore [SQL Injection Prevention](./sql-injection-prevention.md) to protect against common attacks!
