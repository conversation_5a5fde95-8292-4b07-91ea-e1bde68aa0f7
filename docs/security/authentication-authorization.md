# Authentication and Authorization

Database authentication and authorization are fundamental security mechanisms that control who can access the database and what they can do. This guide covers comprehensive strategies for securing database access through proper user management and access control.

## 📋 Table of Contents

1. [Authentication Fundamentals](#authentication-fundamentals)
2. [Authorization Models](#authorization-models)
3. [User and Role Management](#user-and-role-management)
4. [Database-Specific Implementation](#database-specific-implementation)
5. [Advanced Authentication Methods](#advanced-authentication-methods)
6. [Best Practices](#best-practices)
7. [Monitoring and Auditing](#monitoring-and-auditing)

## Authentication Fundamentals

### What is Authentication?
Authentication verifies the identity of users or applications attempting to access the database. It answers the question: "Who are you?"

### Authentication Methods

#### Password-Based Authentication
```sql
-- PostgreSQL: Create user with password
CREATE USER app_user WITH PASSWORD 'SecurePassword123!';

-- SQL Server: Create login with password
CREATE LOGIN app_user WITH PASSWORD = 'SecurePassword123!';
CREATE USER app_user FOR LOGIN app_user;

-- MySQL: Create user with password
CREATE USER 'app_user'@'localhost' IDENTIFIED BY 'SecurePassword123!';
```

#### Certificate-Based Authentication
```sql
-- PostgreSQL: Certificate authentication setup
-- 1. Configure pg_hba.conf
-- hostssl all app_user 0.0.0.0/0 cert

-- 2. Create user without password
CREATE USER app_user;

-- 3. Client connects with certificate
-- psql "host=localhost user=app_user sslmode=require sslcert=client.crt sslkey=client.key sslrootcert=ca.crt"
```

#### Integrated Authentication
```sql
-- SQL Server: Windows Authentication
CREATE LOGIN [DOMAIN\username] FROM WINDOWS;
CREATE USER domain_user FOR LOGIN [DOMAIN\username];

-- PostgreSQL: GSSAPI/Kerberos authentication
-- Configure pg_hba.conf:
-- host all all 0.0.0.0/0 gss include_realm=0 krb_realm=COMPANY.COM
```

### Password Security
```sql
-- Strong password policies
-- PostgreSQL: Use password_encryption
SET password_encryption = 'scram-sha-256';
ALTER USER app_user PASSWORD 'NewSecurePassword123!';

-- SQL Server: Password policy
CREATE LOGIN secure_user WITH 
    PASSWORD = 'ComplexPassword123!',
    CHECK_POLICY = ON,
    CHECK_EXPIRATION = ON;

-- MySQL: Password validation
INSTALL COMPONENT 'file://component_validate_password';
SET GLOBAL validate_password.policy = STRONG;
CREATE USER 'secure_user'@'localhost' IDENTIFIED BY 'ComplexPassword123!';
```

## Authorization Models

### Discretionary Access Control (DAC)
Users control access to resources they own.

```sql
-- PostgreSQL: Grant permissions to specific users
GRANT SELECT, INSERT ON customers TO sales_user;
GRANT UPDATE (email, phone) ON customers TO support_user;

-- Transfer ownership
ALTER TABLE customers OWNER TO new_owner;
```

### Role-Based Access Control (RBAC)
Access is controlled through roles assigned to users.

```sql
-- Create roles with specific permissions
CREATE ROLE sales_role;
CREATE ROLE support_role;
CREATE ROLE admin_role;

-- Grant permissions to roles
GRANT SELECT, INSERT ON customers TO sales_role;
GRANT SELECT, UPDATE ON customers TO support_role;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO admin_role;

-- Assign roles to users
GRANT sales_role TO alice;
GRANT support_role TO bob;
GRANT admin_role TO charlie;
```

### Attribute-Based Access Control (ABAC)
Access decisions based on attributes of users, resources, and environment.

```sql
-- PostgreSQL: Row-level security with attributes
CREATE POLICY customer_access_policy ON customers
FOR ALL TO sales_role
USING (region = current_setting('app.user_region'));

-- Set user attributes
ALTER ROLE alice SET app.user_region = 'north';
ALTER ROLE bob SET app.user_region = 'south';
```

## User and Role Management

### Creating a Comprehensive Role Hierarchy
```sql
-- PostgreSQL: Complete role hierarchy
-- Base roles
CREATE ROLE app_read;
CREATE ROLE app_write;
CREATE ROLE app_admin;
CREATE ROLE app_backup;

-- Specific functional roles
CREATE ROLE sales_read INHERIT;
CREATE ROLE sales_write INHERIT;
CREATE ROLE finance_read INHERIT;
CREATE ROLE finance_write INHERIT;
CREATE ROLE hr_read INHERIT;
CREATE ROLE hr_write INHERIT;

-- Grant base permissions
GRANT SELECT ON ALL TABLES IN SCHEMA public TO app_read;
GRANT INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_write;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_admin;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO app_backup;

-- Grant schema-specific permissions
GRANT SELECT ON sales.* TO sales_read;
GRANT INSERT, UPDATE, DELETE ON sales.* TO sales_write;
GRANT SELECT ON finance.* TO finance_read;
GRANT INSERT, UPDATE, DELETE ON finance.* TO finance_write;
GRANT SELECT ON hr.* TO hr_read;
GRANT INSERT, UPDATE, DELETE ON hr.* TO hr_write;

-- Create role inheritance
GRANT app_read TO sales_read;
GRANT sales_read TO sales_write;
GRANT app_read TO finance_read;
GRANT finance_read TO finance_write;
GRANT app_read TO hr_read;
GRANT hr_read TO hr_write;

-- Create users and assign roles
CREATE USER alice WITH PASSWORD 'SecurePass123!';
CREATE USER bob WITH PASSWORD 'SecurePass456!';
CREATE USER charlie WITH PASSWORD 'SecurePass789!';

GRANT sales_write TO alice;
GRANT finance_read TO bob;
GRANT app_admin TO charlie;
```

### Dynamic Role Assignment
```python
# Python example: Dynamic role assignment based on user attributes
import psycopg2

class DatabaseRoleManager:
    def __init__(self, connection_params):
        self.conn_params = connection_params
    
    def assign_user_roles(self, username, user_attributes):
        """Assign roles based on user attributes"""
        roles_to_assign = []
        
        # Determine roles based on department
        department = user_attributes.get('department')
        if department == 'sales':
            roles_to_assign.extend(['app_read', 'sales_read'])
            if user_attributes.get('level') == 'manager':
                roles_to_assign.append('sales_write')
        elif department == 'finance':
            roles_to_assign.extend(['app_read', 'finance_read'])
            if user_attributes.get('level') in ['manager', 'analyst']:
                roles_to_assign.append('finance_write')
        elif department == 'hr':
            roles_to_assign.extend(['app_read', 'hr_read'])
            if user_attributes.get('level') == 'manager':
                roles_to_assign.append('hr_write')
        
        # Admin users get admin role
        if user_attributes.get('is_admin'):
            roles_to_assign.append('app_admin')
        
        # Apply roles
        with psycopg2.connect(**self.conn_params) as conn:
            with conn.cursor() as cur:
                # Revoke all existing roles first
                cur.execute("""
                    SELECT rolname FROM pg_roles 
                    WHERE rolname LIKE 'app_%' OR rolname LIKE '%_read' OR rolname LIKE '%_write'
                """)
                all_roles = [row[0] for row in cur.fetchall()]
                
                for role in all_roles:
                    cur.execute(f"REVOKE {role} FROM {username}")
                
                # Grant new roles
                for role in roles_to_assign:
                    cur.execute(f"GRANT {role} TO {username}")
                
                conn.commit()
    
    def create_user_with_attributes(self, username, password, attributes):
        """Create user and assign appropriate roles"""
        with psycopg2.connect(**self.conn_params) as conn:
            with conn.cursor() as cur:
                # Create user
                cur.execute(f"CREATE USER {username} WITH PASSWORD %s", (password,))
                
                # Set user attributes as role settings
                for key, value in attributes.items():
                    cur.execute(f"ALTER ROLE {username} SET app.{key} = %s", (str(value),))
                
                conn.commit()
        
        # Assign roles based on attributes
        self.assign_user_roles(username, attributes)

# Usage
role_manager = DatabaseRoleManager({
    'host': 'localhost',
    'database': 'myapp',
    'user': 'admin',
    'password': 'admin_password'
})

# Create users with different attributes
role_manager.create_user_with_attributes('alice', 'password123', {
    'department': 'sales',
    'level': 'manager',
    'region': 'north'
})

role_manager.create_user_with_attributes('bob', 'password456', {
    'department': 'finance',
    'level': 'analyst',
    'clearance': 'confidential'
})
```

## Database-Specific Implementation

### PostgreSQL Advanced Authentication
```sql
-- Row-level security
CREATE TABLE sensitive_data (
    id SERIAL PRIMARY KEY,
    user_id INTEGER,
    department VARCHAR(50),
    data TEXT,
    classification VARCHAR(20)
);

-- Enable RLS
ALTER TABLE sensitive_data ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY user_data_policy ON sensitive_data
FOR ALL TO app_user
USING (user_id = current_setting('app.current_user_id')::INTEGER);

CREATE POLICY department_policy ON sensitive_data
FOR SELECT TO department_user
USING (department = current_setting('app.user_department'));

CREATE POLICY classification_policy ON sensitive_data
FOR SELECT TO classified_user
USING (
    CASE current_setting('app.user_clearance')
        WHEN 'top_secret' THEN TRUE
        WHEN 'secret' THEN classification IN ('secret', 'confidential', 'public')
        WHEN 'confidential' THEN classification IN ('confidential', 'public')
        ELSE classification = 'public'
    END
);

-- Set user context
CREATE OR REPLACE FUNCTION set_user_context(user_id INTEGER, department TEXT, clearance TEXT)
RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_user_id', user_id::TEXT, false);
    PERFORM set_config('app.user_department', department, false);
    PERFORM set_config('app.user_clearance', clearance, false);
END;
$$ LANGUAGE plpgsql;
```

### SQL Server Advanced Authorization
```sql
-- Create schemas for different departments
CREATE SCHEMA sales;
CREATE SCHEMA finance;
CREATE SCHEMA hr;

-- Create roles for each schema
CREATE ROLE sales_users;
CREATE ROLE finance_users;
CREATE ROLE hr_users;

-- Grant schema permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::sales TO sales_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::finance TO finance_users;
GRANT SELECT, INSERT, UPDATE, DELETE ON SCHEMA::hr TO hr_users;

-- Create application roles
CREATE APPLICATION ROLE sales_app_role WITH PASSWORD = 'SalesAppPassword123!';
CREATE APPLICATION ROLE finance_app_role WITH PASSWORD = 'FinanceAppPassword123!';

-- Grant permissions to application roles
EXEC sp_addrolemember 'sales_users', 'sales_app_role';
EXEC sp_addrolemember 'finance_users', 'finance_app_role';

-- Dynamic Data Masking
CREATE TABLE customers (
    customer_id INT IDENTITY(1,1) PRIMARY KEY,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    email VARCHAR(100) MASKED WITH (FUNCTION = 'email()'),
    phone VARCHAR(20) MASKED WITH (FUNCTION = 'partial(1,"XXX-XXX-",4)'),
    ssn VARCHAR(11) MASKED WITH (FUNCTION = 'partial(0,"XXX-XX-",4)'),
    credit_score INT MASKED WITH (FUNCTION = 'random(300, 850)')
);

-- Grant UNMASK permission to specific users
GRANT UNMASK TO finance_manager;
```

### MySQL Enterprise Authentication
```sql
-- LDAP authentication plugin
INSTALL PLUGIN authentication_ldap_sasl SONAME 'authentication_ldap_sasl.so';

-- Create user with LDAP authentication
CREATE USER 'ldap_user'@'%' IDENTIFIED WITH authentication_ldap_sasl AS 'uid=ldap_user,ou=people,dc=company,dc=com';

-- Role-based authentication
CREATE ROLE 'app_read', 'app_write', 'app_admin';

-- Grant privileges to roles
GRANT SELECT ON myapp.* TO 'app_read';
GRANT INSERT, UPDATE, DELETE ON myapp.* TO 'app_write';
GRANT ALL PRIVILEGES ON myapp.* TO 'app_admin';

-- Create users and assign roles
CREATE USER 'alice'@'%' IDENTIFIED BY 'password123';
CREATE USER 'bob'@'%' IDENTIFIED BY 'password456';

GRANT 'app_read' TO 'alice'@'%';
GRANT 'app_write' TO 'bob'@'%';

-- Set default roles
SET DEFAULT ROLE 'app_read' TO 'alice'@'%';
SET DEFAULT ROLE 'app_write' TO 'bob'@'%';
```

## Advanced Authentication Methods

### Multi-Factor Authentication (MFA)
```python
# Example: Application-level MFA with database integration
import pyotp
import qrcode
from io import BytesIO
import base64

class DatabaseMFAManager:
    def __init__(self, db_connection):
        self.db = db_connection
    
    def setup_mfa_for_user(self, username):
        """Set up MFA for a database user"""
        # Generate secret key
        secret = pyotp.random_base32()
        
        # Store secret in database (encrypted)
        encrypted_secret = self.encrypt_secret(secret)
        
        with self.db.cursor() as cur:
            cur.execute("""
                INSERT INTO user_mfa (username, secret_key, enabled, created_at)
                VALUES (%s, %s, false, NOW())
                ON CONFLICT (username) 
                DO UPDATE SET secret_key = EXCLUDED.secret_key, enabled = false
            """, (username, encrypted_secret))
        
        # Generate QR code for user
        totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
            name=username,
            issuer_name="MyApp Database"
        )
        
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(totp_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_code = base64.b64encode(buffer.getvalue()).decode()
        
        return {
            'secret': secret,
            'qr_code': qr_code,
            'backup_codes': self.generate_backup_codes(username)
        }
    
    def verify_mfa_token(self, username, token):
        """Verify MFA token for database access"""
        with self.db.cursor() as cur:
            cur.execute("""
                SELECT secret_key, enabled FROM user_mfa 
                WHERE username = %s AND enabled = true
            """, (username,))
            
            result = cur.fetchone()
            if not result:
                return False
            
            encrypted_secret, enabled = result
            if not enabled:
                return False
            
            secret = self.decrypt_secret(encrypted_secret)
            totp = pyotp.TOTP(secret)
            
            return totp.verify(token, valid_window=1)
    
    def create_database_session(self, username, password, mfa_token):
        """Create database session with MFA verification"""
        # First verify MFA
        if not self.verify_mfa_token(username, mfa_token):
            raise AuthenticationError("Invalid MFA token")
        
        # Create time-limited database session
        session_token = self.generate_session_token()
        
        with self.db.cursor() as cur:
            # Create temporary database user or session
            cur.execute("""
                INSERT INTO active_sessions (username, session_token, expires_at)
                VALUES (%s, %s, NOW() + INTERVAL '8 hours')
            """, (username, session_token))
        
        return session_token
```

### Single Sign-On (SSO) Integration
```python
# Example: SAML SSO integration for database access
from onelogin.saml2.auth import OneLogin_Saml2_Auth
from onelogin.saml2.utils import OneLogin_Saml2_Utils

class DatabaseSSOManager:
    def __init__(self, saml_settings, db_connection):
        self.saml_settings = saml_settings
        self.db = db_connection
    
    def process_saml_response(self, request_data):
        """Process SAML response and create database session"""
        auth = OneLogin_Saml2_Auth(request_data, self.saml_settings)
        auth.process_response()
        
        if not auth.is_authenticated():
            raise AuthenticationError("SAML authentication failed")
        
        # Extract user attributes
        attributes = auth.get_attributes()
        username = auth.get_nameid()
        
        # Map SAML attributes to database roles
        db_roles = self.map_saml_attributes_to_roles(attributes)
        
        # Create or update database user
        self.create_or_update_db_user(username, attributes, db_roles)
        
        # Create database session
        return self.create_database_session(username, db_roles)
    
    def map_saml_attributes_to_roles(self, attributes):
        """Map SAML attributes to database roles"""
        roles = ['app_read']  # Default role
        
        # Map based on department
        departments = attributes.get('department', [])
        for dept in departments:
            if dept.lower() == 'finance':
                roles.append('finance_read')
            elif dept.lower() == 'sales':
                roles.append('sales_read')
            elif dept.lower() == 'hr':
                roles.append('hr_read')
        
        # Map based on job title
        titles = attributes.get('title', [])
        for title in titles:
            if 'manager' in title.lower():
                roles.extend(['app_write', 'manager_role'])
            elif 'admin' in title.lower():
                roles.append('app_admin')
        
        return list(set(roles))  # Remove duplicates
    
    def create_or_update_db_user(self, username, attributes, roles):
        """Create or update database user based on SSO attributes"""
        with self.db.cursor() as cur:
            # Check if user exists
            cur.execute("SELECT 1 FROM pg_user WHERE usename = %s", (username,))
            user_exists = cur.fetchone() is not None
            
            if not user_exists:
                # Create user without password (SSO only)
                cur.execute(f"CREATE USER {username}")
            
            # Update user attributes
            for key, values in attributes.items():
                if values:
                    value = values[0] if isinstance(values, list) else values
                    cur.execute(f"ALTER ROLE {username} SET app.{key} = %s", (value,))
            
            # Update roles
            # First revoke all application roles
            cur.execute("""
                SELECT rolname FROM pg_roles 
                WHERE rolname LIKE 'app_%' OR rolname LIKE '%_read' OR rolname LIKE '%_write'
            """)
            all_roles = [row[0] for row in cur.fetchall()]
            
            for role in all_roles:
                cur.execute(f"REVOKE {role} FROM {username}")
            
            # Grant new roles
            for role in roles:
                cur.execute(f"GRANT {role} TO {username}")
```

## Best Practices

### 1. Principle of Least Privilege
```sql
-- Grant minimal necessary permissions
-- Bad: Granting excessive permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_user;

-- Good: Grant specific permissions
GRANT SELECT ON customers TO sales_user;
GRANT INSERT ON orders TO sales_user;
GRANT UPDATE (status) ON orders TO fulfillment_user;
```

### 2. Regular Access Reviews
```python
def perform_access_review():
    """Regular review of user permissions"""
    with db.cursor() as cur:
        # Find users with excessive permissions
        cur.execute("""
            SELECT grantee, privilege_type, table_name
            FROM information_schema.role_table_grants
            WHERE privilege_type = 'ALL PRIVILEGES'
            AND grantee NOT IN ('postgres', 'admin')
        """)
        
        excessive_perms = cur.fetchall()
        if excessive_perms:
            print("Users with excessive permissions:")
            for user, priv, table in excessive_perms:
                print(f"  {user}: {priv} on {table}")
        
        # Find inactive users
        cur.execute("""
            SELECT usename, last_login
            FROM pg_user u
            LEFT JOIN user_login_log l ON u.usename = l.username
            WHERE last_login < NOW() - INTERVAL '90 days'
            OR last_login IS NULL
        """)
        
        inactive_users = cur.fetchall()
        if inactive_users:
            print("Inactive users (>90 days):")
            for user, last_login in inactive_users:
                print(f"  {user}: {last_login or 'Never logged in'}")

# Schedule regular reviews
import schedule
schedule.every().monday.at("09:00").do(perform_access_review)
```

### 3. Secure Password Management
```python
import secrets
import string
import hashlib

class SecurePasswordManager:
    def generate_secure_password(self, length=16):
        """Generate cryptographically secure password"""
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(length))
        
        # Ensure password meets complexity requirements
        if (any(c.islower() for c in password) and
            any(c.isupper() for c in password) and
            any(c.isdigit() for c in password) and
            any(c in "!@#$%^&*" for c in password)):
            return password
        else:
            return self.generate_secure_password(length)  # Retry
    
    def hash_password(self, password, salt=None):
        """Hash password with salt"""
        if salt is None:
            salt = secrets.token_hex(32)
        
        # Use PBKDF2 with SHA-256
        hashed = hashlib.pbkdf2_hmac('sha256', 
                                   password.encode('utf-8'), 
                                   salt.encode('utf-8'), 
                                   100000)  # 100,000 iterations
        
        return salt + hashed.hex()
    
    def verify_password(self, password, hashed_password):
        """Verify password against hash"""
        salt = hashed_password[:64]  # First 64 chars are salt
        stored_hash = hashed_password[64:]
        
        new_hash = hashlib.pbkdf2_hmac('sha256',
                                     password.encode('utf-8'),
                                     salt.encode('utf-8'),
                                     100000)
        
        return new_hash.hex() == stored_hash
```

### 4. Connection Security
```python
# Secure database connection configuration
import ssl

def create_secure_connection():
    """Create secure database connection"""
    connection_params = {
        'host': 'db.company.com',
        'port': 5432,
        'database': 'myapp',
        'user': 'app_user',
        'password': get_password_from_vault(),  # Never hardcode passwords
        
        # SSL/TLS configuration
        'sslmode': 'require',
        'sslcert': '/path/to/client.crt',
        'sslkey': '/path/to/client.key',
        'sslrootcert': '/path/to/ca.crt',
        
        # Connection timeouts
        'connect_timeout': 10,
        'application_name': 'MyApp-Production'
    }
    
    return psycopg2.connect(**connection_params)

def get_password_from_vault():
    """Retrieve password from secure vault"""
    # Integration with HashiCorp Vault, AWS Secrets Manager, etc.
    import hvac
    
    client = hvac.Client(url='https://vault.company.com')
    client.token = os.getenv('VAULT_TOKEN')
    
    secret = client.secrets.kv.v2.read_secret_version(path='database/myapp')
    return secret['data']['data']['password']
```

## Monitoring and Auditing

### Authentication Monitoring
```sql
-- PostgreSQL: Monitor authentication attempts
CREATE TABLE auth_log (
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    username VARCHAR(100),
    database_name VARCHAR(100),
    client_ip INET,
    success BOOLEAN,
    failure_reason TEXT
);

-- Create function to log authentication attempts
CREATE OR REPLACE FUNCTION log_authentication()
RETURNS event_trigger AS $$
BEGIN
    -- This would be triggered by authentication events
    -- Implementation depends on specific logging requirements
END;
$$ LANGUAGE plpgsql;

-- Monitor failed login attempts
SELECT 
    username,
    client_ip,
    COUNT(*) as failed_attempts,
    MAX(timestamp) as last_attempt
FROM auth_log
WHERE success = false
AND timestamp > NOW() - INTERVAL '1 hour'
GROUP BY username, client_ip
HAVING COUNT(*) >= 5
ORDER BY failed_attempts DESC;
```

### Permission Auditing
```sql
-- Audit current permissions
CREATE VIEW user_permissions_audit AS
SELECT 
    r.rolname as username,
    n.nspname as schema_name,
    c.relname as table_name,
    p.privilege_type,
    p.is_grantable,
    p.grantor
FROM information_schema.role_table_grants p
JOIN pg_class c ON c.relname = p.table_name
JOIN pg_namespace n ON n.oid = c.relnamespace
JOIN pg_roles r ON r.rolname = p.grantee
WHERE r.rolcanlogin = true
ORDER BY username, schema_name, table_name;

-- Track permission changes
CREATE TABLE permission_changes (
    change_id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    changed_by VARCHAR(100),
    action VARCHAR(20), -- GRANT, REVOKE
    username VARCHAR(100),
    object_type VARCHAR(50),
    object_name VARCHAR(200),
    privilege VARCHAR(50)
);
```

## 🚀 Next Steps

After mastering authentication and authorization:
- [Encryption](./encryption.md) - Protect data at rest and in transit
- [SQL Injection Prevention](./sql-injection-prevention.md) - Secure against attacks
- [Audit Logging](./audit-logging.md) - Track database activities

## 📝 Practice Exercises

1. Design a role hierarchy for a multi-tenant application
2. Implement row-level security for a healthcare database
3. Create a user provisioning system with LDAP integration
4. Build an access review and compliance reporting system
5. Implement multi-factor authentication for database access

Remember: Security is layered - combine multiple authentication and authorization mechanisms for comprehensive protection!
