# Indexing Strategies

Indexes are database objects that improve query performance by providing fast access paths to data. This guide covers comprehensive indexing strategies, types, and best practices for optimal database performance.

## 📋 Table of Contents

1. [Index Fundamentals](#index-fundamentals)
2. [Index Types](#index-types)
3. [Creating Effective Indexes](#creating-effective-indexes)
4. [Composite Indexes](#composite-indexes)
5. [Index Maintenance](#index-maintenance)
6. [Performance Considerations](#performance-considerations)
7. [Database-Specific Features](#database-specific-features)

## Index Fundamentals

### What Are Indexes?
Indexes are data structures that improve query performance by creating shortcuts to data. Think of them like an index in a book - instead of reading every page to find a topic, you can jump directly to the relevant pages.

### How Indexes Work
```sql
-- Without index: Full table scan
SELECT * FROM employees WHERE last_name = 'Smith';
-- Database must check every row

-- With index on last_name: Index seek
CREATE INDEX idx_employees_last_name ON employees(last_name);
-- Database can jump directly to 'Smith' entries
```

### Index Benefits and Costs
**Benefits:**
- Faster SELECT queries
- Faster JOIN operations
- Faster ORDER BY operations
- Faster GROUP BY operations
- Enforced uniqueness

**Costs:**
- Additional storage space
- Slower INSERT/UPDATE/DELETE operations
- Index maintenance overhead
- Memory usage

## Index Types

### B-Tree Indexes (Most Common)
```sql
-- Standard B-tree index
CREATE INDEX idx_employees_salary ON employees(salary);

-- Characteristics:
-- - Balanced tree structure
-- - Good for equality and range queries
-- - Maintains sorted order
-- - Default index type in most databases
```

### Unique Indexes
```sql
-- Enforce uniqueness
CREATE UNIQUE INDEX idx_employees_email ON employees(email);

-- Primary key automatically creates unique index
ALTER TABLE employees ADD CONSTRAINT pk_employees PRIMARY KEY (employee_id);
```

### Partial Indexes
```sql
-- PostgreSQL: Index only active employees
CREATE INDEX idx_active_employees_salary 
ON employees(salary) 
WHERE status = 'active';

-- SQL Server: Filtered index
CREATE INDEX idx_active_employees_salary 
ON employees(salary) 
WHERE status = 'active';

-- Benefits: Smaller index, faster maintenance
```

### Functional/Expression Indexes
```sql
-- PostgreSQL: Index on expression
CREATE INDEX idx_employees_upper_last_name 
ON employees(UPPER(last_name));

-- SQL Server: Computed column with index
ALTER TABLE employees 
ADD last_name_upper AS UPPER(last_name);
CREATE INDEX idx_employees_upper_last_name 
ON employees(last_name_upper);

-- Use case: Case-insensitive searches
SELECT * FROM employees WHERE UPPER(last_name) = 'SMITH';
```

### Covering Indexes
```sql
-- Include additional columns in index
CREATE INDEX idx_employees_dept_covering 
ON employees(department) 
INCLUDE (first_name, last_name, salary);

-- Benefits: Avoid key lookups
SELECT first_name, last_name, salary 
FROM employees 
WHERE department = 'Engineering';
-- All data available in index, no table access needed
```

## Creating Effective Indexes

### Selectivity Analysis
```sql
-- Check column selectivity
SELECT 
    COUNT(DISTINCT department) as unique_departments,
    COUNT(*) as total_rows,
    COUNT(DISTINCT department) * 100.0 / COUNT(*) as selectivity_percentage
FROM employees;

-- High selectivity (good for indexing): email, employee_id
-- Low selectivity (poor for indexing): gender, status
```

### Query Pattern Analysis
```sql
-- Analyze common query patterns
-- Pattern 1: Single column lookup
SELECT * FROM employees WHERE employee_id = 123;
-- Index: CREATE INDEX idx_employees_id ON employees(employee_id);

-- Pattern 2: Range queries
SELECT * FROM orders WHERE order_date BETWEEN '2024-01-01' AND '2024-12-31';
-- Index: CREATE INDEX idx_orders_date ON orders(order_date);

-- Pattern 3: Multiple conditions
SELECT * FROM employees WHERE department = 'IT' AND salary > 50000;
-- Index: CREATE INDEX idx_employees_dept_salary ON employees(department, salary);
```

### Index Design Guidelines
```sql
-- 1. Most selective column first
CREATE INDEX idx_employees_email_dept ON employees(email, department);
-- email is more selective than department

-- 2. Equality before range
CREATE INDEX idx_orders_status_date ON orders(status, order_date);
-- WHERE status = 'pending' AND order_date > '2024-01-01'

-- 3. Consider sort order
CREATE INDEX idx_employees_dept_salary_desc ON employees(department, salary DESC);
-- For: ORDER BY department, salary DESC
```

## Composite Indexes

### Column Order Importance
```sql
-- Index: (department, salary, hire_date)
CREATE INDEX idx_employees_composite ON employees(department, salary, hire_date);

-- Effective queries:
SELECT * FROM employees WHERE department = 'IT';                                    -- ✓ Uses index
SELECT * FROM employees WHERE department = 'IT' AND salary > 50000;                -- ✓ Uses index
SELECT * FROM employees WHERE department = 'IT' AND salary > 50000 AND hire_date > '2020-01-01'; -- ✓ Uses index

-- Ineffective queries:
SELECT * FROM employees WHERE salary > 50000;                                      -- ✗ Cannot use index
SELECT * FROM employees WHERE hire_date > '2020-01-01';                           -- ✗ Cannot use index
SELECT * FROM employees WHERE salary > 50000 AND hire_date > '2020-01-01';        -- ✗ Cannot use index
```

### Multiple Index Strategy
```sql
-- Instead of one large composite index, consider multiple targeted indexes
CREATE INDEX idx_employees_department ON employees(department);
CREATE INDEX idx_employees_salary ON employees(salary);
CREATE INDEX idx_employees_hire_date ON employees(hire_date);

-- Database can combine indexes for complex queries
SELECT * FROM employees 
WHERE department = 'IT' AND salary > 50000;
-- May use index intersection/merge
```

### Covering Index Design
```sql
-- Analyze query requirements
SELECT employee_id, first_name, last_name, salary
FROM employees
WHERE department = 'Engineering'
ORDER BY salary DESC;

-- Create covering index
CREATE INDEX idx_employees_dept_covering
ON employees(department, salary DESC)
INCLUDE (employee_id, first_name, last_name);

-- Query can be satisfied entirely from index
```

## Index Maintenance

### Monitoring Index Usage
```sql
-- PostgreSQL: Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;

-- SQL Server: Index usage statistics
SELECT 
    i.name as index_name,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates
FROM sys.indexes i
LEFT JOIN sys.dm_db_index_usage_stats s 
    ON i.object_id = s.object_id AND i.index_id = s.index_id
WHERE i.object_id = OBJECT_ID('employees');
```

### Identifying Unused Indexes
```sql
-- PostgreSQL: Find unused indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes
WHERE idx_scan = 0
ORDER BY pg_relation_size(indexrelid) DESC;

-- SQL Server: Unused indexes
SELECT 
    OBJECT_NAME(i.object_id) as table_name,
    i.name as index_name,
    s.user_seeks + s.user_scans + s.user_lookups as total_reads,
    s.user_updates as total_writes
FROM sys.indexes i
LEFT JOIN sys.dm_db_index_usage_stats s 
    ON i.object_id = s.object_id AND i.index_id = s.index_id
WHERE s.user_seeks + s.user_scans + s.user_lookups = 0
    AND i.is_primary_key = 0
    AND i.is_unique_constraint = 0;
```

### Index Fragmentation
```sql
-- SQL Server: Check index fragmentation
SELECT 
    OBJECT_NAME(ips.object_id) as table_name,
    i.name as index_name,
    ips.avg_fragmentation_in_percent,
    ips.page_count
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 10
ORDER BY ips.avg_fragmentation_in_percent DESC;

-- Rebuild fragmented indexes
ALTER INDEX idx_employees_salary ON employees REBUILD;

-- Reorganize lightly fragmented indexes
ALTER INDEX idx_employees_salary ON employees REORGANIZE;
```

### Statistics Updates
```sql
-- SQL Server: Update statistics
UPDATE STATISTICS employees;
UPDATE STATISTICS employees idx_employees_salary;

-- PostgreSQL: Analyze table
ANALYZE employees;

-- Automatic statistics updates
-- SQL Server: AUTO_UPDATE_STATISTICS = ON
-- PostgreSQL: autovacuum = on
```

## Performance Considerations

### Index Selectivity
```sql
-- Calculate index selectivity
SELECT 
    'department' as column_name,
    COUNT(DISTINCT department) * 100.0 / COUNT(*) as selectivity
FROM employees
UNION ALL
SELECT 
    'salary',
    COUNT(DISTINCT salary) * 100.0 / COUNT(*)
FROM employees;

-- Good selectivity: > 5%
-- Poor selectivity: < 1%
```

### Index Size Impact
```sql
-- PostgreSQL: Index sizes
SELECT 
    tablename,
    indexname,
    pg_size_pretty(pg_relation_size(indexrelid)) as size
FROM pg_stat_user_indexes
ORDER BY pg_relation_size(indexrelid) DESC;

-- Consider storage and memory impact
-- Large indexes may not fit in memory
-- Balance between query performance and resource usage
```

### Write Performance Impact
```sql
-- Measure index maintenance overhead
-- More indexes = slower writes

-- Test with different index configurations
-- Measure INSERT/UPDATE/DELETE performance
-- Find optimal balance for your workload

-- Consider batch operations
INSERT INTO employees (first_name, last_name, department, salary)
SELECT first_name, last_name, department, salary
FROM temp_employees;
-- Batch inserts are more efficient than individual inserts
```

## Database-Specific Features

### PostgreSQL Advanced Indexing
```sql
-- GIN indexes for arrays and full-text search
CREATE INDEX idx_employees_skills_gin ON employees USING GIN(skills);

-- GiST indexes for geometric data
CREATE INDEX idx_locations_gist ON locations USING GiST(coordinates);

-- Hash indexes for equality comparisons
CREATE INDEX idx_employees_id_hash ON employees USING HASH(employee_id);

-- Partial unique indexes
CREATE UNIQUE INDEX idx_employees_email_active 
ON employees(email) 
WHERE status = 'active';
```

### SQL Server Advanced Indexing
```sql
-- Columnstore indexes for analytics
CREATE COLUMNSTORE INDEX idx_sales_columnstore ON sales_fact;

-- Spatial indexes
CREATE SPATIAL INDEX idx_locations_spatial ON locations(coordinates);

-- XML indexes
CREATE XML INDEX idx_employees_xml ON employees(profile_xml);

-- Memory-optimized indexes
CREATE NONCLUSTERED INDEX idx_employees_memory 
ON employees_memory(department) 
WITH (BUCKET_COUNT = 1000);
```

### MySQL Indexing Features
```sql
-- Full-text indexes
CREATE FULLTEXT INDEX idx_employees_fulltext ON employees(first_name, last_name);

-- Multi-value indexes (MySQL 8.0+)
CREATE INDEX idx_employees_skills ON employees((CAST(skills->'$[*]' AS CHAR(50) ARRAY)));

-- Invisible indexes (MySQL 8.0+)
CREATE INDEX idx_employees_test ON employees(department) INVISIBLE;
```

## Index Strategy Examples

### E-commerce Application
```sql
-- Products table indexing strategy
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_name_ft ON products(name) -- Full-text
CREATE INDEX idx_products_active_price ON products(price) WHERE active = true;
CREATE INDEX idx_products_category_price ON products(category_id, price DESC);

-- Orders table indexing strategy
CREATE INDEX idx_orders_customer ON orders(customer_id);
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date DESC);
```

### Analytics Workload
```sql
-- Large fact table indexing
CREATE INDEX idx_sales_date ON sales_fact(date_key);
CREATE INDEX idx_sales_product ON sales_fact(product_key);
CREATE INDEX idx_sales_customer ON sales_fact(customer_key);
CREATE INDEX idx_sales_composite ON sales_fact(date_key, product_key, customer_key);

-- Consider columnstore for aggregations
CREATE COLUMNSTORE INDEX idx_sales_columnstore ON sales_fact;
```

### Multi-tenant Application
```sql
-- Ensure tenant isolation in all indexes
CREATE INDEX idx_users_tenant_email ON users(tenant_id, email);
CREATE INDEX idx_orders_tenant_date ON orders(tenant_id, order_date);
CREATE INDEX idx_products_tenant_category ON products(tenant_id, category_id);

-- Partial indexes for active tenants
CREATE INDEX idx_active_tenant_users ON users(email) 
WHERE tenant_status = 'active';
```

## Best Practices Summary

### Do's
- ✅ Analyze query patterns before creating indexes
- ✅ Monitor index usage regularly
- ✅ Consider composite indexes for multi-column queries
- ✅ Use covering indexes to avoid key lookups
- ✅ Update statistics regularly
- ✅ Test index changes in staging first

### Don'ts
- ❌ Create indexes on every column
- ❌ Ignore index maintenance overhead
- ❌ Use functions on indexed columns in WHERE clauses
- ❌ Create duplicate or redundant indexes
- ❌ Forget to drop unused indexes
- ❌ Ignore index fragmentation

## 🚀 Next Steps

After mastering indexing strategies:
- [Query Execution Plans](./execution-plans.md) - Understand how indexes are used
- [Performance Tuning](./performance-tuning.md) - Systematic optimization approach
- [Statistics and Cardinality](./statistics-cardinality.md) - How databases choose indexes

## 📝 Practice Exercises

1. Analyze a slow query and design appropriate indexes
2. Create a composite index strategy for a multi-column search
3. Implement covering indexes for common reporting queries
4. Set up index monitoring and maintenance procedures
5. Compare different indexing strategies for your workload

Remember: Indexing is both an art and a science - practice with real data and queries to develop intuition for effective index design!
