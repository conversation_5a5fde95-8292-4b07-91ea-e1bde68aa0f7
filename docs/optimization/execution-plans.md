# Query Execution Plans

Execution plans show how the database engine executes your queries. Understanding execution plans is crucial for query optimization, as they reveal the actual operations performed and help identify performance bottlenecks.

## 📋 Table of Contents

1. [Execution Plan Basics](#execution-plan-basics)
2. [Reading Execution Plans](#reading-execution-plans)
3. [Common Operations](#common-operations)
4. [Performance Indicators](#performance-indicators)
5. [Database-Specific Plans](#database-specific-plans)
6. [Optimization Techniques](#optimization-techniques)
7. [Advanced Analysis](#advanced-analysis)

## Execution Plan Basics

### What Are Execution Plans?
Execution plans are the database engine's strategy for executing a query. They show:
- Which indexes are used
- How tables are joined
- Order of operations
- Estimated vs actual costs
- Resource consumption

### Types of Execution Plans
- **Estimated Plans**: Generated without executing the query
- **Actual Plans**: Generated during query execution
- **Cached Plans**: Stored plans reused for similar queries
- **Live Query Statistics**: Real-time execution progress

## Reading Execution Plans

### PostgreSQL EXPLAIN
```sql
-- Basic execution plan
EXPLAIN SELECT * FROM employees WHERE department = 'Engineering';

-- Detailed plan with costs
EXPLAIN (ANALYZE, BUFFERS) 
SELECT e.first_name, e.last_name, d.department_name
FROM employees e
JOIN departments d ON e.department_id = d.department_id
WHERE e.salary > 50000;

-- Output interpretation:
-- Nested Loop  (cost=0.29..8.32 rows=1 width=68) (actual time=0.123..0.125 rows=1 loops=1)
--   Buffers: shared hit=4
--   ->  Seq Scan on employees e  (cost=0.00..4.00 rows=1 width=36) (actual time=0.089..0.090 rows=1 loops=1)
--         Filter: (salary > 50000)
--         Rows Removed by Filter: 99
--         Buffers: shared hit=2
--   ->  Index Scan using departments_pkey on departments d  (cost=0.29..4.31 rows=1 width=32) (actual time=0.032..0.033 rows=1 loops=1)
--         Index Cond: (department_id = e.department_id)
--         Buffers: shared hit=2
```

### SQL Server Execution Plans
```sql
-- Enable actual execution plan
SET STATISTICS IO ON;
SET STATISTICS TIME ON;

-- View execution plan
SELECT e.FirstName, e.LastName, d.DepartmentName
FROM Employees e
INNER JOIN Departments d ON e.DepartmentId = d.DepartmentId
WHERE e.Salary > 50000;

-- Text-based plan
SET SHOWPLAN_TEXT ON;
-- Graphical plan available in SSMS

-- Query Store plans
SELECT 
    qp.plan_id,
    qp.query_plan,
    rs.avg_duration,
    rs.execution_count
FROM sys.query_store_plan qp
JOIN sys.query_store_runtime_stats rs ON qp.plan_id = rs.plan_id
WHERE qp.query_id = 123;
```

### MySQL EXPLAIN
```sql
-- Basic EXPLAIN
EXPLAIN SELECT * FROM employees WHERE department = 'Engineering';

-- Extended information
EXPLAIN FORMAT=JSON 
SELECT e.first_name, e.last_name, d.department_name
FROM employees e
JOIN departments d ON e.department_id = d.department_id
WHERE e.salary > 50000;

-- Visual EXPLAIN (MySQL Workbench)
EXPLAIN FORMAT=TREE
SELECT * FROM employees WHERE salary BETWEEN 40000 AND 60000;
```

## Common Operations

### Table Access Methods

#### Sequential Scan / Table Scan
```sql
-- PostgreSQL
Seq Scan on employees  (cost=0.00..4.00 rows=100 width=36)
  Filter: (department = 'Engineering')

-- Indicates: No suitable index, scanning entire table
-- When it's okay: Small tables, high selectivity filters
-- When to optimize: Large tables, low selectivity
```

#### Index Scan
```sql
-- PostgreSQL
Index Scan using idx_employees_department on employees  (cost=0.29..8.30 rows=5 width=36)
  Index Cond: (department = 'Engineering')

-- Indicates: Using index to find rows, then accessing table
-- Good: Selective queries
-- Consider: Covering index to avoid table access
```

#### Index Only Scan
```sql
-- PostgreSQL
Index Only Scan using idx_employees_covering on employees  (cost=0.29..4.31 rows=5 width=36)
  Index Cond: (department = 'Engineering')
  Heap Fetches: 0

-- Indicates: All needed data available in index
-- Excellent: No table access required
-- Heap Fetches: 0 means no visibility map checks needed
```

### Join Operations

#### Nested Loop Join
```sql
-- PostgreSQL
Nested Loop  (cost=0.29..16.32 rows=5 width=68)
  ->  Seq Scan on departments d  (cost=0.00..1.05 rows=5 width=32)
  ->  Index Scan using idx_employees_dept on employees e  (cost=0.29..3.05 rows=1 width=36)
        Index Cond: (department_id = d.department_id)

-- Best for: Small outer table, indexed inner table
-- Complexity: O(n * m) where n is outer, m is inner lookups
-- Optimize: Ensure inner table has good index
```

#### Hash Join
```sql
-- PostgreSQL
Hash Join  (cost=1.06..5.07 rows=5 width=68)
  Hash Cond: (e.department_id = d.department_id)
  ->  Seq Scan on employees e  (cost=0.00..4.00 rows=100 width=36)
  ->  Hash  (cost=1.05..1.05 rows=5 width=32)
        ->  Seq Scan on departments d  (cost=0.00..1.05 rows=5 width=32)

-- Best for: Medium to large tables, equality joins
-- Process: Build hash table from smaller table, probe with larger
-- Memory: Requires sufficient work_mem
```

#### Merge Join
```sql
-- PostgreSQL
Merge Join  (cost=0.29..5.32 rows=5 width=68)
  Merge Cond: (e.department_id = d.department_id)
  ->  Index Scan using idx_employees_dept on employees e  (cost=0.29..4.30 rows=100 width=36)
  ->  Index Scan using departments_pkey on departments d  (cost=0.29..1.31 rows=5 width=32)

-- Best for: Large tables, both sides sorted on join key
-- Requires: Sorted input (indexes or explicit sort)
-- Efficient: Linear scan through both sorted inputs
```

### Aggregation Operations

#### HashAggregate
```sql
-- PostgreSQL
HashAggregate  (cost=4.25..4.27 rows=2 width=40)
  Group Key: department
  ->  Seq Scan on employees  (cost=0.00..4.00 rows=100 width=36)

-- Process: Hash-based grouping
-- Memory: Requires work_mem for hash table
-- Alternative: GroupAggregate for sorted input
```

#### Sort Operations
```sql
-- PostgreSQL
Sort  (cost=4.65..4.90 rows=100 width=36)
  Sort Key: salary DESC
  Sort Method: quicksort  Memory: 25kB
  ->  Seq Scan on employees  (cost=0.00..4.00 rows=100 width=36)

-- Memory usage: In-memory vs disk-based
-- Optimize: Use indexes to avoid sorting
-- Monitor: Sort Method (quicksort vs external merge)
```

## Performance Indicators

### Cost Analysis
```sql
-- PostgreSQL cost components
-- cost=startup_cost..total_cost rows=estimated_rows width=average_row_width

-- Startup cost: Cost before first row
-- Total cost: Cost to process all rows
-- Rows: Estimated number of rows
-- Width: Average row size in bytes
```

### Actual vs Estimated
```sql
-- Look for significant differences
-- Nested Loop  (cost=0.29..8.32 rows=1 width=68) (actual time=0.123..0.125 rows=100 loops=1)
--                                    ^^^^estimated    ^^^^actual

-- Large differences indicate:
-- - Outdated statistics
-- - Data skew
-- - Complex predicates
-- - Correlated columns
```

### Resource Usage
```sql
-- PostgreSQL BUFFERS output
-- Buffers: shared hit=4 read=2 dirtied=1 written=1

-- shared hit: Pages found in buffer cache
-- read: Pages read from disk
-- dirtied: Pages modified
-- written: Pages written to disk

-- High read/written values indicate I/O bottlenecks
```

## Database-Specific Plans

### PostgreSQL Plan Analysis
```sql
-- Detailed analysis with all options
EXPLAIN (
    ANALYZE true,
    BUFFERS true,
    TIMING true,
    COSTS true,
    VERBOSE true,
    FORMAT JSON
) 
SELECT * FROM employees WHERE salary > 50000;

-- Key metrics to watch:
-- - Execution time
-- - Buffer hits vs reads
-- - Rows removed by filter
-- - Loops (for nested operations)
```

### SQL Server Plan Analysis
```sql
-- Detailed execution statistics
SET STATISTICS IO ON;
SET STATISTICS TIME ON;
SET STATISTICS PROFILE ON;

-- Key operators to understand:
-- - Clustered Index Scan vs Seek
-- - Key Lookup (expensive)
-- - Sort operations
-- - Hash Match vs Nested Loops

-- Plan cache analysis
SELECT 
    cp.plan_handle,
    cp.usecounts,
    cp.size_in_bytes,
    st.text
FROM sys.dm_exec_cached_plans cp
CROSS APPLY sys.dm_exec_sql_text(cp.plan_handle) st
WHERE st.text LIKE '%employees%';
```

### MySQL Plan Analysis
```sql
-- Performance Schema for execution statistics
SELECT 
    digest_text,
    count_star,
    avg_timer_wait/1000000000 as avg_time_sec,
    sum_rows_examined,
    sum_rows_sent
FROM performance_schema.events_statements_summary_by_digest
WHERE digest_text LIKE '%employees%'
ORDER BY avg_timer_wait DESC;

-- EXPLAIN ANALYZE (MySQL 8.0.18+)
EXPLAIN ANALYZE
SELECT * FROM employees WHERE salary > 50000;
```

## Optimization Techniques

### Index Usage Optimization
```sql
-- Problem: Index not being used
EXPLAIN SELECT * FROM employees WHERE UPPER(last_name) = 'SMITH';
-- Shows: Seq Scan with Filter

-- Solution: Functional index
CREATE INDEX idx_employees_upper_last_name ON employees(UPPER(last_name));

-- Or: Rewrite query
SELECT * FROM employees WHERE last_name = 'Smith' OR last_name = 'SMITH';
```

### Join Optimization
```sql
-- Problem: Inefficient join order
EXPLAIN 
SELECT *
FROM large_table l
JOIN small_table s ON l.id = s.large_id
WHERE s.status = 'active';

-- Solution: Help optimizer with better statistics or hints
ANALYZE large_table;
ANALYZE small_table;

-- Or: Rewrite to filter early
SELECT *
FROM large_table l
JOIN (SELECT * FROM small_table WHERE status = 'active') s 
ON l.id = s.large_id;
```

### Subquery Optimization
```sql
-- Problem: Correlated subquery
EXPLAIN
SELECT *
FROM employees e
WHERE salary > (
    SELECT AVG(salary) 
    FROM employees e2 
    WHERE e2.department = e.department
);

-- Solution: Window function
EXPLAIN
SELECT *
FROM (
    SELECT *,
           AVG(salary) OVER (PARTITION BY department) as dept_avg
    FROM employees
) e
WHERE salary > dept_avg;
```

## Advanced Analysis

### Plan Stability
```sql
-- PostgreSQL: Plan stability across executions
-- Monitor for plan changes that affect performance

-- SQL Server: Query Store for plan regression detection
SELECT 
    q.query_id,
    qt.query_sql_text,
    p.plan_id,
    rs.avg_duration,
    rs.last_execution_time
FROM sys.query_store_query q
JOIN sys.query_store_query_text qt ON q.query_text_id = qt.query_text_id
JOIN sys.query_store_plan p ON q.query_id = p.query_id
JOIN sys.query_store_runtime_stats rs ON p.plan_id = rs.plan_id
WHERE rs.avg_duration > 1000000  -- 1 second
ORDER BY rs.avg_duration DESC;
```

### Parameter Sniffing
```sql
-- Problem: Plan optimized for specific parameter values
-- SQL Server example
EXEC sp_executesql 
N'SELECT * FROM orders WHERE customer_id = @customer_id',
N'@customer_id INT',
@customer_id = 123;

-- Solution: Use OPTION(RECOMPILE) or optimize for unknown
EXEC sp_executesql 
N'SELECT * FROM orders WHERE customer_id = @customer_id OPTION(OPTIMIZE FOR UNKNOWN)',
N'@customer_id INT',
@customer_id = 123;
```

### Parallel Execution
```sql
-- PostgreSQL: Parallel query execution
SET max_parallel_workers_per_gather = 4;

EXPLAIN (ANALYZE, BUFFERS)
SELECT department, COUNT(*), AVG(salary)
FROM employees
GROUP BY department;

-- Look for:
-- Gather (parallel workers)
-- Parallel Seq Scan
-- Parallel Hash operations
```

### Memory Usage Analysis
```sql
-- PostgreSQL: Work memory usage
SET work_mem = '256MB';

EXPLAIN (ANALYZE, BUFFERS)
SELECT *
FROM large_table
ORDER BY some_column;

-- Monitor:
-- Sort Method: quicksort Memory vs external merge Disk
-- Hash Buckets and Memory usage
-- Materialize operations
```

## Troubleshooting Common Issues

### High CPU Usage
```sql
-- Look for:
-- - Missing indexes (Seq Scan on large tables)
-- - Inefficient joins (Nested Loop with large outer table)
-- - Unnecessary sorting
-- - Complex expressions in WHERE clauses

-- Example fix:
-- Before: WHERE YEAR(order_date) = 2024
-- After:  WHERE order_date >= '2024-01-01' AND order_date < '2025-01-01'
```

### High I/O Usage
```sql
-- Look for:
-- - High buffer reads
-- - Large table scans
-- - Missing covering indexes
-- - Inefficient join algorithms

-- Monitor buffer statistics:
-- Buffers: shared hit=100 read=1000  -- High read indicates I/O
```

### Memory Issues
```sql
-- Look for:
-- - External sorts (disk-based)
-- - Hash operations spilling to disk
-- - Large work_mem requirements

-- Tune memory settings:
-- work_mem, hash_mem_multiplier, maintenance_work_mem
```

## Best Practices

### Regular Plan Analysis
```sql
-- Set up monitoring for:
-- - Plan changes
-- - Performance regressions
-- - Resource usage trends
-- - Long-running queries

-- Automate plan collection and analysis
-- Use database-specific monitoring tools
```

### Plan Forcing (When Appropriate)
```sql
-- SQL Server: Force specific plan
EXEC sp_query_store_force_plan @query_id = 123, @plan_id = 456;

-- PostgreSQL: Use pg_hint_plan extension for hints
/*+ SeqScan(employees) */
SELECT * FROM employees WHERE department = 'IT';
```

### Documentation
```sql
-- Document optimization decisions
-- Include before/after execution plans
-- Note business context and requirements
-- Track performance improvements
```

## 🚀 Next Steps

After mastering execution plans:
- [Performance Tuning](./performance-tuning.md) - Systematic optimization approach
- [Query Profiling](./query-profiling.md) - Identifying bottlenecks
- [Statistics and Cardinality](./statistics-cardinality.md) - Understanding cost estimation

## 📝 Practice Exercises

1. Analyze execution plans for your most common queries
2. Identify and fix table scans in large tables
3. Optimize join operations by understanding join algorithms
4. Compare estimated vs actual rows for accuracy
5. Set up automated plan monitoring and alerting

Remember: Execution plans are your window into database performance - learn to read them fluently to become an effective query optimizer!
