# Performance Tuning

Performance tuning is a systematic approach to optimizing database performance. This guide covers methodologies, techniques, and best practices for identifying and resolving performance issues.

## 📋 Table of Contents

1. [Performance Tuning Methodology](#performance-tuning-methodology)
2. [Performance Baseline](#performance-baseline)
3. [Identifying Bottlenecks](#identifying-bottlenecks)
4. [Query-Level Optimization](#query-level-optimization)
5. [System-Level Optimization](#system-level-optimization)
6. [Monitoring and Alerting](#monitoring-and-alerting)
7. [Case Studies](#case-studies)

## Performance Tuning Methodology

### The PDCA Approach
1. **Plan**: Define performance goals and metrics
2. **Do**: Implement optimization changes
3. **Check**: Measure and validate improvements
4. **Act**: Document and standardize successful changes

### Performance Tuning Process
```
1. Establish Baseline
   ↓
2. Identify Bottlenecks
   ↓
3. Prioritize Issues
   ↓
4. Implement Solutions
   ↓
5. Measure Impact
   ↓
6. Document Results
   ↓
7. Monitor Continuously
```

### Key Performance Metrics
- **Response Time**: Time to complete individual operations
- **Throughput**: Operations per second/minute
- **Resource Utilization**: CPU, memory, I/O, network usage
- **Concurrency**: Number of simultaneous operations
- **Error Rates**: Failed operations percentage

## Performance Baseline

### Establishing Baselines
```sql
-- PostgreSQL: Collect baseline statistics
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del
FROM pg_stat_user_tables
ORDER BY seq_tup_read DESC;

-- Track query performance
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    stddev_time,
    rows
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 20;
```

### SQL Server Baseline Collection
```sql
-- Collect performance counters
SELECT 
    counter_name,
    instance_name,
    cntr_value,
    cntr_type
FROM sys.dm_os_performance_counters
WHERE object_name LIKE '%SQL Statistics%'
   OR object_name LIKE '%Buffer Manager%'
   OR object_name LIKE '%Memory Manager%';

-- Query Store baseline
SELECT 
    qt.query_sql_text,
    rs.count_executions,
    rs.avg_duration,
    rs.avg_cpu_time,
    rs.avg_logical_io_reads
FROM sys.query_store_runtime_stats rs
JOIN sys.query_store_plan p ON rs.plan_id = p.plan_id
JOIN sys.query_store_query q ON p.query_id = q.query_id
JOIN sys.query_store_query_text qt ON q.query_text_id = qt.query_text_id
WHERE rs.last_execution_time >= DATEADD(day, -7, GETDATE());
```

### MySQL Performance Baseline
```sql
-- Performance Schema baseline
SELECT 
    event_name,
    count_star,
    sum_timer_wait/1000000000 as total_time_sec,
    avg_timer_wait/1000000000 as avg_time_sec
FROM performance_schema.events_statements_summary_global_by_event_name
WHERE count_star > 0
ORDER BY sum_timer_wait DESC;

-- InnoDB metrics
SHOW ENGINE INNODB STATUS;

-- Key metrics from status
SELECT 
    VARIABLE_NAME,
    VARIABLE_VALUE
FROM performance_schema.global_status
WHERE VARIABLE_NAME IN (
    'Innodb_buffer_pool_read_requests',
    'Innodb_buffer_pool_reads',
    'Innodb_rows_read',
    'Innodb_rows_inserted',
    'Innodb_rows_updated',
    'Innodb_rows_deleted'
);
```

## Identifying Bottlenecks

### CPU Bottlenecks
```sql
-- PostgreSQL: CPU-intensive queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    (total_time / sum(total_time) OVER()) * 100 as pct_total_time
FROM pg_stat_statements
WHERE calls > 100
ORDER BY total_time DESC
LIMIT 10;

-- SQL Server: CPU usage by query
SELECT TOP 10
    qs.sql_handle,
    qs.total_worker_time/qs.execution_count as avg_cpu_time,
    qs.execution_count,
    SUBSTRING(st.text, (qs.statement_start_offset/2)+1,
        ((CASE qs.statement_end_offset
            WHEN -1 THEN DATALENGTH(st.text)
            ELSE qs.statement_end_offset
        END - qs.statement_start_offset)/2) + 1) as statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) st
ORDER BY qs.total_worker_time/qs.execution_count DESC;
```

### I/O Bottlenecks
```sql
-- PostgreSQL: I/O statistics
SELECT 
    schemaname,
    tablename,
    heap_blks_read,
    heap_blks_hit,
    CASE WHEN heap_blks_hit + heap_blks_read = 0 THEN 0
         ELSE heap_blks_hit::float / (heap_blks_hit + heap_blks_read) * 100
    END as cache_hit_ratio
FROM pg_statio_user_tables
WHERE heap_blks_read > 0
ORDER BY heap_blks_read DESC;

-- SQL Server: I/O statistics by database
SELECT 
    DB_NAME(database_id) as database_name,
    SUM(num_of_reads) as total_reads,
    SUM(num_of_writes) as total_writes,
    SUM(num_of_bytes_read) as total_bytes_read,
    SUM(num_of_bytes_written) as total_bytes_written
FROM sys.dm_io_virtual_file_stats(NULL, NULL)
GROUP BY database_id
ORDER BY total_reads + total_writes DESC;
```

### Memory Bottlenecks
```sql
-- PostgreSQL: Buffer cache analysis
SELECT 
    c.relname,
    pg_size_pretty(count(*) * 8192) as buffered,
    round(100.0 * count(*) / (
        SELECT setting FROM pg_settings WHERE name='shared_buffers'
    )::integer, 1) as buffers_percent,
    round(100.0 * count(*) * 8192 / pg_relation_size(c.oid), 1) as percent_of_relation
FROM pg_class c
INNER JOIN pg_buffercache b ON b.relfilenode = c.relfilenode
INNER JOIN pg_database d ON (b.reldatabase = d.oid AND d.datname = current_database())
GROUP BY c.oid, c.relname
ORDER BY 2 DESC
LIMIT 20;

-- SQL Server: Memory usage
SELECT 
    counter_name,
    cntr_value
FROM sys.dm_os_performance_counters
WHERE object_name = 'SQLServer:Memory Manager'
   OR (object_name = 'SQLServer:Buffer Manager' 
       AND counter_name IN ('Buffer cache hit ratio', 'Page life expectancy'));
```

### Lock Contention
```sql
-- PostgreSQL: Lock monitoring
SELECT 
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks 
    ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- SQL Server: Blocking sessions
SELECT 
    r.session_id,
    r.wait_type,
    r.wait_time,
    r.blocking_session_id,
    s.program_name,
    s.host_name,
    s.login_name,
    t.text as sql_text
FROM sys.dm_exec_requests r
LEFT JOIN sys.dm_exec_sessions s ON r.session_id = s.session_id
CROSS APPLY sys.dm_exec_sql_text(r.sql_handle) t
WHERE r.blocking_session_id <> 0;
```

## Query-Level Optimization

### Query Rewriting Techniques
```sql
-- Optimize EXISTS vs IN
-- Slow: IN with large subquery
SELECT customer_name
FROM customers
WHERE customer_id IN (
    SELECT customer_id FROM orders WHERE order_date >= '2024-01-01'
);

-- Fast: EXISTS
SELECT customer_name
FROM customers c
WHERE EXISTS (
    SELECT 1 FROM orders o 
    WHERE o.customer_id = c.customer_id 
    AND o.order_date >= '2024-01-01'
);

-- Optimize UNION vs UNION ALL
-- Slow: UNION (removes duplicates)
SELECT product_name FROM products WHERE category = 'electronics'
UNION
SELECT product_name FROM products WHERE price < 100;

-- Fast: UNION ALL (if duplicates are acceptable)
SELECT product_name FROM products WHERE category = 'electronics'
UNION ALL
SELECT product_name FROM products WHERE price < 100;
```

### Predicate Optimization
```sql
-- Make predicates sargable (Search ARGument ABLE)
-- Non-sargable: Function on column
SELECT * FROM orders WHERE YEAR(order_date) = 2024;

-- Sargable: Range condition
SELECT * FROM orders 
WHERE order_date >= '2024-01-01' 
  AND order_date < '2025-01-01';

-- Non-sargable: Leading wildcard
SELECT * FROM customers WHERE name LIKE '%smith%';

-- Sargable: Prefix search
SELECT * FROM customers WHERE name LIKE 'smith%';
```

### Join Optimization
```sql
-- Optimize join order and conditions
-- Ensure join conditions use indexes
CREATE INDEX idx_orders_customer_id ON orders(customer_id);
CREATE INDEX idx_customers_id ON customers(customer_id);

-- Filter early to reduce join size
SELECT c.customer_name, o.order_total
FROM customers c
JOIN (
    SELECT customer_id, order_total
    FROM orders 
    WHERE order_date >= '2024-01-01'
) o ON c.customer_id = o.customer_id
WHERE c.status = 'active';
```

### Subquery Optimization
```sql
-- Convert correlated subqueries to joins
-- Slow: Correlated subquery
SELECT customer_name,
       (SELECT COUNT(*) FROM orders o WHERE o.customer_id = c.customer_id) as order_count
FROM customers c;

-- Fast: JOIN with aggregation
SELECT c.customer_name, COALESCE(o.order_count, 0) as order_count
FROM customers c
LEFT JOIN (
    SELECT customer_id, COUNT(*) as order_count
    FROM orders
    GROUP BY customer_id
) o ON c.customer_id = o.customer_id;
```

## System-Level Optimization

### Memory Configuration
```sql
-- PostgreSQL memory settings
-- postgresql.conf
shared_buffers = 256MB          -- 25% of RAM for dedicated server
work_mem = 4MB                  -- Per-operation memory
maintenance_work_mem = 64MB     -- For maintenance operations
effective_cache_size = 1GB      -- OS cache estimate

-- SQL Server memory settings
-- Set max server memory (leave memory for OS)
EXEC sp_configure 'max server memory (MB)', 6144;
RECONFIGURE;

-- MySQL memory settings
-- my.cnf
innodb_buffer_pool_size = 1G    -- 70-80% of RAM for InnoDB
query_cache_size = 64M          -- Query result cache
sort_buffer_size = 2M           -- Per-connection sort buffer
```

### I/O Optimization
```sql
-- PostgreSQL I/O settings
checkpoint_segments = 32        -- Checkpoint frequency
checkpoint_completion_target = 0.9  -- Spread checkpoints
wal_buffers = 16MB             -- WAL buffer size

-- SQL Server I/O optimization
-- Separate data, log, and tempdb on different drives
-- Use multiple data files for tempdb
-- Enable instant file initialization

-- MySQL I/O settings
innodb_log_file_size = 256M     -- Redo log size
innodb_flush_log_at_trx_commit = 2  -- Flush strategy
innodb_io_capacity = 200        -- I/O capacity
```

### Connection Management
```sql
-- PostgreSQL connection settings
max_connections = 100           -- Maximum connections
shared_preload_libraries = 'pg_stat_statements'

-- Connection pooling with PgBouncer
-- pgbouncer.ini
[databases]
mydb = host=localhost port=5432 dbname=mydb

[pgbouncer]
pool_mode = transaction
max_client_conn = 1000
default_pool_size = 25

-- SQL Server connection settings
-- Use connection pooling in application
-- Monitor connection usage
SELECT 
    DB_NAME(database_id) as database_name,
    COUNT(*) as connection_count
FROM sys.dm_exec_sessions
WHERE database_id > 0
GROUP BY database_id;
```

## Monitoring and Alerting

### Automated Monitoring Setup
```sql
-- PostgreSQL monitoring queries
-- Create monitoring views
CREATE VIEW performance_summary AS
SELECT 
    'Connections' as metric,
    COUNT(*) as value
FROM pg_stat_activity
WHERE state = 'active'
UNION ALL
SELECT 
    'Cache Hit Ratio',
    ROUND(
        SUM(heap_blks_hit) * 100.0 / 
        NULLIF(SUM(heap_blks_hit + heap_blks_read), 0), 2
    )
FROM pg_statio_user_tables;

-- SQL Server monitoring
-- Create performance monitoring procedure
CREATE PROCEDURE sp_monitor_performance
AS
BEGIN
    SELECT 
        'CPU Usage %' as metric,
        AVG(cpu_percent) as value
    FROM sys.dm_db_resource_stats
    WHERE end_time >= DATEADD(minute, -5, GETDATE())
    
    UNION ALL
    
    SELECT 
        'Active Connections',
        COUNT(*)
    FROM sys.dm_exec_sessions
    WHERE status = 'running';
END;
```

### Performance Alerts
```sql
-- Set up alerts for key metrics
-- Long-running queries
-- High CPU usage
-- Low cache hit ratios
-- Blocking sessions
-- Disk space usage

-- Example: PostgreSQL long-running query alert
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
  AND state = 'active'
  AND query NOT LIKE '%pg_stat_activity%';
```

## Case Studies

### Case Study 1: Slow Reporting Query
```sql
-- Problem: Monthly sales report taking 10+ minutes
-- Original query
SELECT 
    p.product_name,
    SUM(oi.quantity * oi.unit_price) as total_sales
FROM products p
JOIN order_items oi ON p.product_id = oi.product_id
JOIN orders o ON oi.order_id = o.order_id
WHERE o.order_date BETWEEN '2024-01-01' AND '2024-01-31'
GROUP BY p.product_id, p.product_name
ORDER BY total_sales DESC;

-- Analysis: Execution plan showed table scans
-- Solution 1: Add indexes
CREATE INDEX idx_orders_date ON orders(order_date);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_product_id ON order_items(product_id);

-- Solution 2: Optimize query structure
SELECT 
    p.product_name,
    monthly_sales.total_sales
FROM products p
JOIN (
    SELECT 
        oi.product_id,
        SUM(oi.quantity * oi.unit_price) as total_sales
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.order_id
    WHERE o.order_date BETWEEN '2024-01-01' AND '2024-01-31'
    GROUP BY oi.product_id
) monthly_sales ON p.product_id = monthly_sales.product_id
ORDER BY monthly_sales.total_sales DESC;

-- Result: Query time reduced from 10 minutes to 15 seconds
```

### Case Study 2: High CPU Usage
```sql
-- Problem: Database server showing 90%+ CPU usage
-- Investigation: Found frequent execution of inefficient query

-- Problematic query (executed 1000+ times per minute)
SELECT * FROM users WHERE UPPER(email) = UPPER(@email);

-- Solution 1: Create functional index
CREATE INDEX idx_users_email_upper ON users(UPPER(email));

-- Solution 2: Application-level optimization
-- Normalize email case in application before querying
SELECT * FROM users WHERE email = LOWER(@email);

-- Solution 3: Add covering index
CREATE INDEX idx_users_email_covering 
ON users(email) 
INCLUDE (user_id, first_name, last_name, status);

-- Result: CPU usage dropped to 30%, response time improved 5x
```

### Case Study 3: Lock Contention
```sql
-- Problem: Application timeouts due to lock contention
-- Investigation: Found long-running transactions holding locks

-- Solution 1: Optimize transaction scope
-- Before: Long transaction
BEGIN;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
    -- ... lots of other operations ...
    INSERT INTO order_items (order_id, product_id, quantity) VALUES (456, 123, 1);
COMMIT;

-- After: Shorter transactions
-- Update inventory in separate, quick transaction
BEGIN;
    UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
COMMIT;

-- Other operations...

BEGIN;
    INSERT INTO order_items (order_id, product_id, quantity) VALUES (456, 123, 1);
COMMIT;

-- Solution 2: Change isolation level where appropriate
SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

-- Result: Lock wait times reduced by 80%
```

## Best Practices Summary

### Do's
- ✅ Establish performance baselines
- ✅ Monitor continuously
- ✅ Test changes in staging first
- ✅ Document optimization decisions
- ✅ Focus on high-impact issues first
- ✅ Use appropriate tools for analysis

### Don'ts
- ❌ Optimize without measuring
- ❌ Make multiple changes simultaneously
- ❌ Ignore system-level factors
- ❌ Optimize in production without testing
- ❌ Focus only on individual queries
- ❌ Neglect ongoing monitoring

## 🚀 Next Steps

After mastering performance tuning:
- [Query Profiling](./query-profiling.md) - Advanced bottleneck identification
- [Statistics and Cardinality](./statistics-cardinality.md) - Understanding optimizer decisions
- [Database-Specific Optimization](../databases/) - Platform-specific techniques

## 📝 Practice Exercises

1. Establish performance baselines for your database
2. Identify and optimize your top 10 slowest queries
3. Set up automated performance monitoring
4. Conduct a comprehensive performance audit
5. Create a performance optimization playbook for your team

Remember: Performance tuning is an ongoing process - establish good monitoring and optimization practices to maintain optimal database performance!
