# Query Optimization

Query optimization is the process of improving database query performance through various techniques including indexing, query rewriting, and understanding execution plans. This section covers comprehensive strategies for optimizing database queries.

## 📚 Contents

### Core Optimization Concepts
- [Indexing Strategies](./indexing-strategies.md) - Creating and managing effective indexes
- [Query Execution Plans](./execution-plans.md) - Understanding how databases execute queries
- [Performance Tuning](./performance-tuning.md) - Systematic approach to improving performance
- [Query Profiling](./query-profiling.md) - Identifying and measuring performance bottlenecks
- [Statistics and Cardinality](./statistics-cardinality.md) - How databases estimate query costs
- [Hint Usage](./hints.md) - When and how to use query hints

## 🎯 Learning Path

### Beginner (Start Here)
1. [Query Execution Plans](./execution-plans.md) - Understand how queries are executed
2. [Indexing Strategies](./indexing-strategies.md) - Learn the fundamentals of indexing
3. [Performance Tuning](./performance-tuning.md) - Basic optimization techniques

### Intermediate
4. [Query Profiling](./query-profiling.md) - Identify performance bottlenecks
5. [Statistics and Cardinality](./statistics-cardinality.md) - Understand query cost estimation
6. [Hint Usage](./hints.md) - Advanced query control techniques

## 💡 Key Optimization Principles

### 1. Understand Your Data
- **Data distribution**: Know how your data is distributed
- **Query patterns**: Understand common access patterns
- **Growth trends**: Plan for data growth
- **Business requirements**: Balance performance with functionality

### 2. Index Effectively
- **Selective indexes**: Index columns with high selectivity
- **Composite indexes**: Use multi-column indexes strategically
- **Covering indexes**: Include all needed columns in the index
- **Maintenance overhead**: Balance query performance with update costs

### 3. Write Efficient Queries
- **Sargable conditions**: Use conditions that can leverage indexes
- **Avoid functions on columns**: Keep columns "bare" in WHERE clauses
- **Limit result sets**: Use appropriate LIMIT/TOP clauses
- **Join efficiently**: Understand different join algorithms

### 4. Monitor and Measure
- **Baseline performance**: Establish performance baselines
- **Regular monitoring**: Continuously monitor query performance
- **Proactive optimization**: Address issues before they become problems
- **Test changes**: Always test optimizations in staging environments

## 🔧 Quick Optimization Checklist

### Query Analysis
- [ ] Examine execution plan
- [ ] Identify table scans and seeks
- [ ] Check join algorithms
- [ ] Look for sort operations
- [ ] Identify missing indexes

### Index Review
- [ ] Verify appropriate indexes exist
- [ ] Check index usage statistics
- [ ] Remove unused indexes
- [ ] Consider composite indexes
- [ ] Review index maintenance

### Query Structure
- [ ] Optimize WHERE clauses
- [ ] Review JOIN conditions
- [ ] Minimize SELECT columns
- [ ] Use appropriate data types
- [ ] Avoid unnecessary functions

### Statistics and Maintenance
- [ ] Update table statistics
- [ ] Check data distribution
- [ ] Review query plan cache
- [ ] Monitor resource usage
- [ ] Schedule maintenance tasks

## 📊 Common Performance Patterns

### Anti-Patterns to Avoid
```sql
-- Avoid: Function on column in WHERE clause
SELECT * FROM orders WHERE YEAR(order_date) = 2024;

-- Better: Use range condition
SELECT * FROM orders WHERE order_date >= '2024-01-01' AND order_date < '2025-01-01';

-- Avoid: Leading wildcards in LIKE
SELECT * FROM customers WHERE name LIKE '%smith%';

-- Better: Use full-text search or prefix matching
SELECT * FROM customers WHERE name LIKE 'smith%';

-- Avoid: OR conditions that prevent index usage
SELECT * FROM products WHERE category = 'electronics' OR category = 'computers';

-- Better: Use IN clause
SELECT * FROM products WHERE category IN ('electronics', 'computers');
```

### Efficient Patterns
```sql
-- Use EXISTS instead of IN for large subqueries
SELECT c.customer_name
FROM customers c
WHERE EXISTS (
    SELECT 1 FROM orders o 
    WHERE o.customer_id = c.customer_id 
    AND o.order_date >= '2024-01-01'
);

-- Use appropriate JOIN types
SELECT c.customer_name, COUNT(o.order_id) as order_count
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
GROUP BY c.customer_id, c.customer_name;

-- Limit results early
SELECT TOP 100 *
FROM large_table
WHERE indexed_column = 'value'
ORDER BY date_column DESC;
```

## 🛠️ Tools and Techniques

### Database-Specific Tools
- **PostgreSQL**: EXPLAIN, pg_stat_statements, auto_explain
- **SQL Server**: Query Store, Execution Plans, DMVs
- **MySQL**: Performance Schema, EXPLAIN, slow query log
- **Oracle**: AWR, TKPROF, SQL Tuning Advisor

### Third-Party Tools
- **Database monitoring**: DataDog, New Relic, SolarWinds
- **Query analyzers**: SentryOne, Redgate SQL Monitor
- **Performance testing**: Apache JMeter, LoadRunner
- **Profiling tools**: Application-specific profilers

### Built-in Monitoring
```sql
-- PostgreSQL: Enable query statistics
-- In postgresql.conf:
-- shared_preload_libraries = 'pg_stat_statements'
-- pg_stat_statements.track = all

-- View slow queries
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;

-- SQL Server: Query Store
-- Enable Query Store
ALTER DATABASE YourDatabase SET QUERY_STORE = ON;

-- View top queries by duration
SELECT 
    qt.query_sql_text,
    rs.avg_duration,
    rs.execution_count
FROM sys.query_store_query_text qt
JOIN sys.query_store_query q ON qt.query_text_id = q.query_text_id
JOIN sys.query_store_runtime_stats rs ON q.query_id = rs.query_id
ORDER BY rs.avg_duration DESC;
```

## 🎯 Optimization Strategies by Scenario

### OLTP (Online Transaction Processing)
- **Focus on**: Fast individual queries, concurrent access
- **Key metrics**: Response time, throughput, lock contention
- **Strategies**: Point lookups, efficient indexes, minimal locking

### OLAP (Online Analytical Processing)
- **Focus on**: Complex aggregations, large data scans
- **Key metrics**: Query completion time, resource utilization
- **Strategies**: Columnstore indexes, partitioning, materialized views

### Mixed Workloads
- **Focus on**: Balancing OLTP and OLAP requirements
- **Key metrics**: Overall system performance, resource allocation
- **Strategies**: Read replicas, workload isolation, adaptive indexing

## 📈 Performance Monitoring

### Key Metrics to Track
- **Query execution time**: Average, median, 95th percentile
- **Resource utilization**: CPU, memory, I/O
- **Concurrency**: Active connections, lock waits
- **Index usage**: Seek vs scan ratios, index effectiveness
- **Cache hit ratios**: Buffer pool, query plan cache

### Alerting Thresholds
```sql
-- Example monitoring queries

-- Long-running queries (PostgreSQL)
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
AND state = 'active';

-- Blocking queries (SQL Server)
SELECT 
    blocking_session_id,
    session_id,
    wait_type,
    wait_time,
    wait_resource
FROM sys.dm_exec_requests
WHERE blocking_session_id <> 0;
```

## 🚀 Getting Started

### 1. Establish Baseline
- Document current performance metrics
- Identify most frequent and slowest queries
- Understand current resource utilization
- Map business-critical operations

### 2. Quick Wins
- Add missing indexes for frequent queries
- Update outdated statistics
- Remove unused indexes
- Optimize obvious anti-patterns

### 3. Systematic Optimization
- Prioritize by business impact
- Test changes in staging
- Monitor impact of changes
- Document optimization decisions

### 4. Continuous Improvement
- Regular performance reviews
- Proactive monitoring
- Capacity planning
- Knowledge sharing

## 📝 Best Practices Summary

### Do's
- ✅ Always analyze execution plans
- ✅ Test optimizations thoroughly
- ✅ Monitor performance continuously
- ✅ Document optimization decisions
- ✅ Consider business requirements
- ✅ Use appropriate tools

### Don'ts
- ❌ Optimize without measuring
- ❌ Add indexes without understanding impact
- ❌ Ignore maintenance overhead
- ❌ Optimize in production first
- ❌ Focus only on individual queries
- ❌ Neglect monitoring

## 🔗 Related Topics

After mastering query optimization basics, explore:
- [Database Design](../design/README.md) - Designing for performance
- [Indexing Strategies](../indexing/README.md) - Advanced indexing techniques
- [Performance Tuning](../performance/README.md) - System-level optimization
- [Monitoring](../monitoring/README.md) - Comprehensive monitoring strategies

## 📚 Further Reading

- Database-specific optimization guides
- Query optimization research papers
- Performance tuning case studies
- Industry best practices and benchmarks

Start with [Query Execution Plans](./execution-plans.md) to understand how databases process your queries, then move on to [Indexing Strategies](./indexing-strategies.md) to learn how to make them faster!
