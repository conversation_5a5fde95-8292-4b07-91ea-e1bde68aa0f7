# Monitoring and Maintenance

Database monitoring and maintenance are critical for ensuring optimal performance, reliability, and data integrity. This section covers comprehensive monitoring strategies, maintenance procedures, and best practices for keeping databases healthy.

## 📚 Contents

### Performance Monitoring
- [Performance Metrics](./performance-metrics.md) - Key metrics to track database health
- [Query Monitoring](./query-monitoring.md) - Tracking and optimizing query performance
- [Resource Monitoring](./resource-monitoring.md) - CPU, memory, disk, and network monitoring

### Alerting and Analysis
- [Alerting Systems](./alerting.md) - Setting up proactive alerts and notifications
- [Log Analysis](./log-analysis.md) - Analyzing database logs for insights and issues

### Maintenance Operations
- [Backup Strategies](./backup-strategies.md) - Comprehensive backup and recovery planning
- [Recovery Procedures](./recovery-procedures.md) - Database recovery and disaster response
- [Index Maintenance](./index-maintenance.md) - Keeping indexes optimized and healthy
- [Statistics Updates](./statistics-updates.md) - Maintaining query optimizer statistics
- [Cleanup Procedures](./cleanup-procedures.md) - Regular maintenance and housekeeping

## 🎯 Learning Path

### Beginner (Start Here)
1. [Performance Metrics](./performance-metrics.md) - Understand what to monitor
2. [Backup Strategies](./backup-strategies.md) - Protect your data
3. [Alerting Systems](./alerting.md) - Get notified of issues

### Intermediate
4. [Query Monitoring](./query-monitoring.md) - Optimize query performance
5. [Resource Monitoring](./resource-monitoring.md) - Track system resources
6. [Index Maintenance](./index-maintenance.md) - Keep indexes healthy

### Advanced
7. [Log Analysis](./log-analysis.md) - Deep dive into database logs
8. [Recovery Procedures](./recovery-procedures.md) - Handle disasters
9. [Statistics Updates](./statistics-updates.md) - Optimize query planning
10. [Cleanup Procedures](./cleanup-procedures.md) - Maintain system health

## 💡 Monitoring Philosophy

### Proactive vs Reactive
- **Proactive monitoring**: Identify issues before they impact users
- **Predictive analysis**: Use trends to forecast potential problems
- **Automated responses**: Implement self-healing where possible
- **Continuous improvement**: Regularly review and refine monitoring

### Key Monitoring Areas
- **Performance**: Response times, throughput, resource utilization
- **Availability**: Uptime, connectivity, service health
- **Capacity**: Storage usage, growth trends, resource limits
- **Security**: Access patterns, failed logins, privilege changes
- **Data integrity**: Consistency checks, corruption detection

## 📊 Essential Metrics

### Database Performance Metrics
```sql
-- PostgreSQL: Key performance indicators
SELECT 
    datname as database,
    numbackends as active_connections,
    xact_commit as transactions_committed,
    xact_rollback as transactions_rolled_back,
    blks_read as disk_blocks_read,
    blks_hit as buffer_cache_hits,
    temp_files as temporary_files,
    temp_bytes as temporary_bytes,
    deadlocks
FROM pg_stat_database
WHERE datname NOT IN ('template0', 'template1', 'postgres');

-- Calculate cache hit ratio
SELECT 
    datname,
    ROUND(
        (blks_hit::float / NULLIF(blks_hit + blks_read, 0)) * 100, 2
    ) as cache_hit_ratio_percent
FROM pg_stat_database
WHERE datname NOT IN ('template0', 'template1', 'postgres');
```

### System Resource Metrics
```sql
-- SQL Server: Resource utilization
SELECT 
    counter_name,
    cntr_value,
    cntr_type
FROM sys.dm_os_performance_counters
WHERE object_name IN (
    'SQLServer:Buffer Manager',
    'SQLServer:Memory Manager',
    'SQLServer:SQL Statistics',
    'SQLServer:Databases'
)
AND counter_name IN (
    'Buffer cache hit ratio',
    'Page life expectancy',
    'Memory Grants Pending',
    'Batch Requests/sec',
    'SQL Compilations/sec'
);

-- MySQL: Performance metrics
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_read_requests';
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_reads';
SHOW GLOBAL STATUS LIKE 'Threads_connected';
SHOW GLOBAL STATUS LIKE 'Slow_queries';
```

### Query Performance Metrics
```sql
-- PostgreSQL: Slow query analysis
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    stddev_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 20;

-- SQL Server: Query performance
SELECT TOP 20
    qs.execution_count,
    qs.total_elapsed_time / qs.execution_count as avg_elapsed_time,
    qs.total_cpu_time / qs.execution_count as avg_cpu_time,
    qs.total_logical_reads / qs.execution_count as avg_logical_reads,
    SUBSTRING(st.text, (qs.statement_start_offset/2)+1,
        ((CASE qs.statement_end_offset
            WHEN -1 THEN DATALENGTH(st.text)
            ELSE qs.statement_end_offset
        END - qs.statement_start_offset)/2) + 1) as statement_text
FROM sys.dm_exec_query_stats qs
CROSS APPLY sys.dm_exec_sql_text(qs.sql_handle) st
ORDER BY qs.total_elapsed_time DESC;
```

## 🔧 Monitoring Tools

### Built-in Database Tools
```sql
-- PostgreSQL monitoring views
SELECT * FROM pg_stat_activity;        -- Current activity
SELECT * FROM pg_stat_database;        -- Database statistics
SELECT * FROM pg_stat_user_tables;     -- Table statistics
SELECT * FROM pg_stat_user_indexes;    -- Index usage
SELECT * FROM pg_locks;                -- Lock information

-- SQL Server DMVs (Dynamic Management Views)
SELECT * FROM sys.dm_exec_sessions;           -- Session information
SELECT * FROM sys.dm_exec_requests;           -- Current requests
SELECT * FROM sys.dm_os_wait_stats;           -- Wait statistics
SELECT * FROM sys.dm_db_index_usage_stats;    -- Index usage
SELECT * FROM sys.dm_exec_query_stats;        -- Query statistics
```

### Third-Party Monitoring Solutions
- **Open Source**: Prometheus + Grafana, Zabbix, Nagios
- **Commercial**: DataDog, New Relic, SolarWinds, Quest Foglight
- **Cloud Native**: AWS CloudWatch, Azure Monitor, Google Cloud Monitoring
- **Database Specific**: pgAdmin, SQL Server Management Studio, MySQL Workbench

### Custom Monitoring Scripts
```python
# Example: Python monitoring script
import psycopg2
import time
import json
from datetime import datetime

def collect_postgres_metrics():
    conn = psycopg2.connect(
        host="localhost",
        database="postgres",
        user="monitor_user",
        password="password"
    )
    
    metrics = {}
    
    with conn.cursor() as cur:
        # Database size
        cur.execute("""
            SELECT datname, pg_size_pretty(pg_database_size(datname))
            FROM pg_database
            WHERE datname NOT IN ('template0', 'template1')
        """)
        metrics['database_sizes'] = dict(cur.fetchall())
        
        # Active connections
        cur.execute("""
            SELECT count(*) FROM pg_stat_activity 
            WHERE state = 'active'
        """)
        metrics['active_connections'] = cur.fetchone()[0]
        
        # Cache hit ratio
        cur.execute("""
            SELECT ROUND(
                (sum(blks_hit)::float / NULLIF(sum(blks_hit + blks_read), 0)) * 100, 2
            ) FROM pg_stat_database
        """)
        metrics['cache_hit_ratio'] = cur.fetchone()[0]
    
    conn.close()
    
    # Add timestamp
    metrics['timestamp'] = datetime.now().isoformat()
    
    return metrics

# Collect and store metrics
if __name__ == "__main__":
    while True:
        try:
            metrics = collect_postgres_metrics()
            print(json.dumps(metrics, indent=2))
            
            # Store metrics (database, file, or monitoring system)
            # store_metrics(metrics)
            
        except Exception as e:
            print(f"Error collecting metrics: {e}")
        
        time.sleep(60)  # Collect every minute
```

## 🚨 Alerting Strategies

### Critical Alerts (Immediate Response)
```yaml
# Example: Prometheus alerting rules
groups:
- name: database_critical
  rules:
  - alert: DatabaseDown
    expr: up{job="postgres"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Database instance is down"
      
  - alert: HighConnectionUsage
    expr: postgres_connections_active / postgres_connections_max > 0.9
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "Database connection usage is above 90%"
      
  - alert: LowCacheHitRatio
    expr: postgres_cache_hit_ratio < 0.8
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "Database cache hit ratio is below 80%"
```

### Warning Alerts (Proactive Monitoring)
```sql
-- Example: Custom alert queries
-- Long-running queries
SELECT 
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
  AND state = 'active';

-- High disk usage
SELECT 
    datname,
    pg_size_pretty(pg_database_size(datname)) as size,
    ROUND(
        (pg_database_size(datname)::float / 
         (SELECT sum(pg_database_size(datname)) FROM pg_database)) * 100, 2
    ) as percent_of_total
FROM pg_database
WHERE datname NOT IN ('template0', 'template1')
ORDER BY pg_database_size(datname) DESC;
```

## 🛠️ Maintenance Automation

### Automated Backup Scripts
```bash
#!/bin/bash
# PostgreSQL backup script

DB_NAME="myapp"
BACKUP_DIR="/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_backup_$DATE.sql"

# Create backup directory if it doesn't exist
mkdir -p $BACKUP_DIR

# Perform backup
pg_dump -h localhost -U postgres -d $DB_NAME > $BACKUP_FILE

# Compress backup
gzip $BACKUP_FILE

# Remove backups older than 7 days
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

# Log backup completion
echo "$(date): Backup completed for $DB_NAME" >> /var/log/postgres_backup.log
```

### Index Maintenance Scripts
```sql
-- PostgreSQL: Automated index maintenance
DO $$
DECLARE
    rec RECORD;
BEGIN
    -- Reindex tables with high bloat
    FOR rec IN 
        SELECT schemaname, tablename
        FROM pg_stat_user_tables
        WHERE n_dead_tup > n_live_tup * 0.1  -- 10% dead tuples
    LOOP
        EXECUTE 'REINDEX TABLE ' || rec.schemaname || '.' || rec.tablename;
        RAISE NOTICE 'Reindexed table: %.%', rec.schemaname, rec.tablename;
    END LOOP;
END $$;

-- SQL Server: Index maintenance
DECLARE @sql NVARCHAR(MAX) = '';

SELECT @sql = @sql + 
    CASE 
        WHEN avg_fragmentation_in_percent > 30 THEN
            'ALTER INDEX ' + i.name + ' ON ' + OBJECT_NAME(ips.object_id) + ' REBUILD;' + CHAR(13)
        WHEN avg_fragmentation_in_percent > 10 THEN
            'ALTER INDEX ' + i.name + ' ON ' + OBJECT_NAME(ips.object_id) + ' REORGANIZE;' + CHAR(13)
        ELSE ''
    END
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, 'LIMITED') ips
JOIN sys.indexes i ON ips.object_id = i.object_id AND ips.index_id = i.index_id
WHERE ips.avg_fragmentation_in_percent > 10;

EXEC sp_executesql @sql;
```

## 📈 Capacity Planning

### Growth Trend Analysis
```sql
-- Track database growth over time
CREATE TABLE database_size_history (
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    database_name VARCHAR(100),
    size_bytes BIGINT,
    table_count INTEGER,
    index_count INTEGER
);

-- Collect size data
INSERT INTO database_size_history (database_name, size_bytes, table_count, index_count)
SELECT 
    datname,
    pg_database_size(datname),
    (SELECT count(*) FROM pg_tables WHERE schemaname = 'public'),
    (SELECT count(*) FROM pg_indexes WHERE schemaname = 'public')
FROM pg_database
WHERE datname NOT IN ('template0', 'template1', 'postgres');

-- Analyze growth trends
SELECT 
    database_name,
    DATE_TRUNC('month', recorded_at) as month,
    AVG(size_bytes) as avg_size_bytes,
    MAX(size_bytes) - MIN(size_bytes) as growth_bytes
FROM database_size_history
WHERE recorded_at >= CURRENT_DATE - INTERVAL '12 months'
GROUP BY database_name, DATE_TRUNC('month', recorded_at)
ORDER BY database_name, month;
```

## 📋 Monitoring Checklist

### Performance Monitoring
- [ ] Key performance metrics tracked
- [ ] Query performance monitored
- [ ] Resource utilization tracked
- [ ] Cache hit ratios monitored
- [ ] Wait events analyzed

### Availability Monitoring
- [ ] Database connectivity monitored
- [ ] Service health checks implemented
- [ ] Failover mechanisms tested
- [ ] Recovery procedures documented
- [ ] Backup integrity verified

### Capacity Monitoring
- [ ] Storage usage tracked
- [ ] Growth trends analyzed
- [ ] Capacity planning performed
- [ ] Resource limits monitored
- [ ] Scaling triggers defined

### Security Monitoring
- [ ] Access patterns monitored
- [ ] Failed login attempts tracked
- [ ] Privilege changes audited
- [ ] Security events alerted
- [ ] Compliance requirements met

### Maintenance
- [ ] Regular backups performed
- [ ] Index maintenance scheduled
- [ ] Statistics updates automated
- [ ] Log rotation configured
- [ ] Cleanup procedures implemented

## 🔗 Related Topics

After mastering monitoring and maintenance:
- [Performance Optimization](../performance/README.md) - Optimize based on monitoring data
- [Security](../security/README.md) - Security monitoring and auditing
- [Database Operations](../operations/README.md) - Production operations
- [Troubleshooting](../troubleshooting/README.md) - Resolve issues identified through monitoring

Start with [Performance Metrics](./performance-metrics.md) to understand what to monitor, then explore [Backup Strategies](./backup-strategies.md) to protect your data!
