# Performance Metrics

Database performance metrics provide insights into system health, resource utilization, and query efficiency. This guide covers essential metrics to monitor, how to collect them, and how to interpret the data for optimal database performance.

## 📋 Table of Contents

1. [Core Performance Metrics](#core-performance-metrics)
2. [Database-Specific Metrics](#database-specific-metrics)
3. [Metric Collection Methods](#metric-collection-methods)
4. [Performance Baselines](#performance-baselines)
5. [Alerting Thresholds](#alerting-thresholds)
6. [Metric Analysis](#metric-analysis)
7. [Best Practices](#best-practices)

## Core Performance Metrics

### Response Time Metrics
Response time measures how long operations take to complete.

```sql
-- PostgreSQL: Query execution time tracking
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    stddev_time,
    min_time,
    max_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 20;

-- Key metrics:
-- - Average response time
-- - 95th percentile response time
-- - Maximum response time
-- - Response time distribution
```

### Throughput Metrics
Throughput measures the volume of work completed per unit of time.

```sql
-- PostgreSQL: Transaction throughput
SELECT 
    datname,
    xact_commit + xact_rollback as total_transactions,
    xact_commit,
    xact_rollback,
    (xact_rollback::float / NULLIF(xact_commit + xact_rollback, 0)) * 100 as rollback_percentage
FROM pg_stat_database
WHERE datname NOT IN ('template0', 'template1', 'postgres');

-- Key metrics:
-- - Transactions per second (TPS)
-- - Queries per second (QPS)
-- - Operations per second
-- - Rollback percentage
```

### Resource Utilization Metrics
Monitor how efficiently the database uses system resources.

```sql
-- PostgreSQL: Connection and resource usage
SELECT 
    datname,
    numbackends as active_connections,
    temp_files,
    temp_bytes,
    deadlocks,
    blk_read_time,
    blk_write_time
FROM pg_stat_database;

-- Key metrics:
-- - CPU utilization
-- - Memory usage
-- - Disk I/O
-- - Network I/O
-- - Connection count
```

### Cache Hit Ratios
Cache efficiency directly impacts performance.

```sql
-- PostgreSQL: Buffer cache hit ratio
SELECT 
    datname,
    blks_hit,
    blks_read,
    ROUND(
        (blks_hit::float / NULLIF(blks_hit + blks_read, 0)) * 100, 2
    ) as cache_hit_ratio
FROM pg_stat_database
WHERE datname NOT IN ('template0', 'template1', 'postgres');

-- Target: > 95% for OLTP workloads
-- Target: > 85% for OLAP workloads
```

## Database-Specific Metrics

### PostgreSQL Metrics
```sql
-- Connection statistics
SELECT 
    state,
    COUNT(*) as connection_count
FROM pg_stat_activity
GROUP BY state;

-- Lock statistics
SELECT 
    mode,
    COUNT(*) as lock_count
FROM pg_locks
GROUP BY mode;

-- Table statistics
SELECT 
    schemaname,
    tablename,
    seq_scan,
    seq_tup_read,
    idx_scan,
    idx_tup_fetch,
    n_tup_ins,
    n_tup_upd,
    n_tup_del,
    n_dead_tup
FROM pg_stat_user_tables
ORDER BY seq_tup_read DESC;

-- Index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes
WHERE idx_scan > 0
ORDER BY idx_scan DESC;
```

### SQL Server Metrics
```sql
-- Performance counters
SELECT 
    counter_name,
    cntr_value,
    cntr_type
FROM sys.dm_os_performance_counters
WHERE object_name IN (
    'SQLServer:Buffer Manager',
    'SQLServer:SQL Statistics',
    'SQLServer:Memory Manager',
    'SQLServer:Databases'
);

-- Wait statistics
SELECT TOP 20
    wait_type,
    waiting_tasks_count,
    wait_time_ms,
    max_wait_time_ms,
    signal_wait_time_ms,
    wait_time_ms / waiting_tasks_count as avg_wait_time_ms
FROM sys.dm_os_wait_stats
WHERE waiting_tasks_count > 0
ORDER BY wait_time_ms DESC;

-- Index usage statistics
SELECT 
    i.name as index_name,
    s.user_seeks,
    s.user_scans,
    s.user_lookups,
    s.user_updates,
    s.last_user_seek,
    s.last_user_scan,
    s.last_user_lookup
FROM sys.indexes i
LEFT JOIN sys.dm_db_index_usage_stats s 
    ON i.object_id = s.object_id AND i.index_id = s.index_id
WHERE i.object_id = OBJECT_ID('your_table_name');
```

### MySQL Metrics
```sql
-- Global status variables
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_read_requests';
SHOW GLOBAL STATUS LIKE 'Innodb_buffer_pool_reads';
SHOW GLOBAL STATUS LIKE 'Threads_connected';
SHOW GLOBAL STATUS LIKE 'Threads_running';
SHOW GLOBAL STATUS LIKE 'Slow_queries';
SHOW GLOBAL STATUS LIKE 'Questions';

-- Performance Schema metrics
SELECT 
    event_name,
    count_star,
    sum_timer_wait/1000000000 as total_time_sec,
    avg_timer_wait/1000000000 as avg_time_sec,
    min_timer_wait/1000000000 as min_time_sec,
    max_timer_wait/1000000000 as max_time_sec
FROM performance_schema.events_statements_summary_global_by_event_name
WHERE count_star > 0
ORDER BY sum_timer_wait DESC
LIMIT 20;

-- InnoDB metrics
SELECT 
    NAME,
    COUNT
FROM information_schema.INNODB_METRICS
WHERE STATUS = 'enabled'
ORDER BY NAME;
```

## Metric Collection Methods

### Built-in Database Views
```python
# PostgreSQL metrics collection
import psycopg2
import time
import json

class PostgreSQLMetricsCollector:
    def __init__(self, connection_params):
        self.conn_params = connection_params
    
    def collect_database_metrics(self):
        """Collect comprehensive database metrics"""
        metrics = {}
        
        with psycopg2.connect(**self.conn_params) as conn:
            with conn.cursor() as cur:
                # Database statistics
                cur.execute("""
                    SELECT 
                        datname,
                        numbackends,
                        xact_commit,
                        xact_rollback,
                        blks_read,
                        blks_hit,
                        temp_files,
                        temp_bytes,
                        deadlocks
                    FROM pg_stat_database
                    WHERE datname NOT IN ('template0', 'template1', 'postgres')
                """)
                
                db_stats = {}
                for row in cur.fetchall():
                    db_name = row[0]
                    db_stats[db_name] = {
                        'connections': row[1],
                        'commits': row[2],
                        'rollbacks': row[3],
                        'disk_reads': row[4],
                        'cache_hits': row[5],
                        'temp_files': row[6],
                        'temp_bytes': row[7],
                        'deadlocks': row[8],
                        'cache_hit_ratio': (row[5] / max(row[4] + row[5], 1)) * 100
                    }
                
                metrics['databases'] = db_stats
                
                # Connection statistics
                cur.execute("""
                    SELECT 
                        state,
                        COUNT(*) as count
                    FROM pg_stat_activity
                    GROUP BY state
                """)
                
                connection_stats = dict(cur.fetchall())
                metrics['connections'] = connection_stats
                
                # Top queries by execution time
                cur.execute("""
                    SELECT 
                        query,
                        calls,
                        total_time,
                        mean_time,
                        rows
                    FROM pg_stat_statements
                    ORDER BY total_time DESC
                    LIMIT 10
                """)
                
                top_queries = []
                for row in cur.fetchall():
                    top_queries.append({
                        'query': row[0][:100] + '...' if len(row[0]) > 100 else row[0],
                        'calls': row[1],
                        'total_time': row[2],
                        'mean_time': row[3],
                        'rows': row[4]
                    })
                
                metrics['top_queries'] = top_queries
                
                # Table statistics
                cur.execute("""
                    SELECT 
                        schemaname || '.' || tablename as table_name,
                        seq_scan,
                        seq_tup_read,
                        idx_scan,
                        idx_tup_fetch,
                        n_tup_ins + n_tup_upd + n_tup_del as modifications
                    FROM pg_stat_user_tables
                    ORDER BY seq_tup_read DESC
                    LIMIT 10
                """)
                
                table_stats = []
                for row in cur.fetchall():
                    table_stats.append({
                        'table': row[0],
                        'seq_scans': row[1],
                        'seq_reads': row[2],
                        'idx_scans': row[3],
                        'idx_reads': row[4],
                        'modifications': row[5]
                    })
                
                metrics['table_stats'] = table_stats
        
        metrics['timestamp'] = time.time()
        return metrics
    
    def collect_system_metrics(self):
        """Collect system-level metrics"""
        import psutil
        
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': {
                path: psutil.disk_usage(path).percent 
                for path in ['/var/lib/postgresql']
                if psutil.disk_usage(path)
            },
            'load_average': psutil.getloadavg(),
            'timestamp': time.time()
        }

# Usage
collector = PostgreSQLMetricsCollector({
    'host': 'localhost',
    'database': 'myapp',
    'user': 'monitor_user',
    'password': 'password'
})

db_metrics = collector.collect_database_metrics()
sys_metrics = collector.collect_system_metrics()
```

### External Monitoring Tools
```yaml
# Prometheus configuration for PostgreSQL
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'postgres'
    static_configs:
      - targets: ['localhost:9187']
    scrape_interval: 5s
    metrics_path: /metrics

# postgres_exporter configuration
# .env file
DATA_SOURCE_NAME="postgresql://monitor_user:password@localhost:5432/postgres?sslmode=disable"
PG_EXPORTER_EXTEND_QUERY_PATH="/etc/postgres_exporter/queries.yaml"
```

### Custom Metric Collection
```python
# Custom metrics dashboard
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta

class MetricsDashboard:
    def __init__(self, metrics_collector):
        self.collector = metrics_collector
        self.metrics_history = []
    
    def collect_and_store_metrics(self):
        """Collect metrics and store in history"""
        metrics = self.collector.collect_database_metrics()
        self.metrics_history.append(metrics)
        
        # Keep only last 24 hours of data
        cutoff_time = time.time() - (24 * 3600)
        self.metrics_history = [
            m for m in self.metrics_history 
            if m['timestamp'] > cutoff_time
        ]
    
    def generate_performance_report(self):
        """Generate performance report with visualizations"""
        if not self.metrics_history:
            return "No metrics data available"
        
        # Convert to DataFrame for analysis
        df_data = []
        for metrics in self.metrics_history:
            timestamp = datetime.fromtimestamp(metrics['timestamp'])
            
            for db_name, db_stats in metrics['databases'].items():
                df_data.append({
                    'timestamp': timestamp,
                    'database': db_name,
                    'connections': db_stats['connections'],
                    'cache_hit_ratio': db_stats['cache_hit_ratio'],
                    'commits': db_stats['commits'],
                    'rollbacks': db_stats['rollbacks']
                })
        
        df = pd.DataFrame(df_data)
        
        # Generate plots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Connection count over time
        for db in df['database'].unique():
            db_data = df[df['database'] == db]
            axes[0, 0].plot(db_data['timestamp'], db_data['connections'], label=db)
        axes[0, 0].set_title('Active Connections Over Time')
        axes[0, 0].legend()
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Cache hit ratio
        for db in df['database'].unique():
            db_data = df[df['database'] == db]
            axes[0, 1].plot(db_data['timestamp'], db_data['cache_hit_ratio'], label=db)
        axes[0, 1].set_title('Cache Hit Ratio Over Time')
        axes[0, 1].set_ylabel('Percentage')
        axes[0, 1].legend()
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # Transaction rate
        for db in df['database'].unique():
            db_data = df[df['database'] == db].sort_values('timestamp')
            if len(db_data) > 1:
                db_data['commit_rate'] = db_data['commits'].diff() / db_data['timestamp'].diff().dt.total_seconds()
                axes[1, 0].plot(db_data['timestamp'], db_data['commit_rate'], label=db)
        axes[1, 0].set_title('Commit Rate (commits/sec)')
        axes[1, 0].legend()
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # Rollback percentage
        df['rollback_pct'] = (df['rollbacks'] / (df['commits'] + df['rollbacks'])) * 100
        for db in df['database'].unique():
            db_data = df[df['database'] == db]
            axes[1, 1].plot(db_data['timestamp'], db_data['rollback_pct'], label=db)
        axes[1, 1].set_title('Rollback Percentage')
        axes[1, 1].set_ylabel('Percentage')
        axes[1, 1].legend()
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('database_performance_report.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        return "Performance report generated: database_performance_report.png"
```

## Performance Baselines

### Establishing Baselines
```python
def establish_performance_baseline(duration_hours=24):
    """Establish performance baseline over specified duration"""
    baseline_metrics = {
        'response_times': [],
        'throughput': [],
        'resource_utilization': [],
        'cache_hit_ratios': []
    }
    
    start_time = time.time()
    end_time = start_time + (duration_hours * 3600)
    
    while time.time() < end_time:
        metrics = collector.collect_database_metrics()
        
        # Extract key metrics
        for db_name, db_stats in metrics['databases'].items():
            baseline_metrics['cache_hit_ratios'].append(db_stats['cache_hit_ratio'])
            baseline_metrics['throughput'].append(db_stats['commits'])
        
        # Sleep for 5 minutes between collections
        time.sleep(300)
    
    # Calculate baseline statistics
    baseline_stats = {}
    for metric_type, values in baseline_metrics.items():
        if values:
            baseline_stats[metric_type] = {
                'mean': np.mean(values),
                'median': np.median(values),
                'std': np.std(values),
                'p95': np.percentile(values, 95),
                'p99': np.percentile(values, 99),
                'min': np.min(values),
                'max': np.max(values)
            }
    
    return baseline_stats

def compare_to_baseline(current_metrics, baseline_stats):
    """Compare current metrics to established baseline"""
    alerts = []
    
    # Check cache hit ratio
    current_cache_ratio = current_metrics['databases']['myapp']['cache_hit_ratio']
    baseline_cache = baseline_stats['cache_hit_ratios']
    
    if current_cache_ratio < baseline_cache['mean'] - (2 * baseline_cache['std']):
        alerts.append({
            'type': 'cache_hit_ratio_low',
            'current': current_cache_ratio,
            'baseline_mean': baseline_cache['mean'],
            'severity': 'warning'
        })
    
    # Check throughput
    current_throughput = current_metrics['databases']['myapp']['commits']
    baseline_throughput = baseline_stats['throughput']
    
    if current_throughput < baseline_throughput['mean'] * 0.5:
        alerts.append({
            'type': 'throughput_low',
            'current': current_throughput,
            'baseline_mean': baseline_throughput['mean'],
            'severity': 'critical'
        })
    
    return alerts
```

## Alerting Thresholds

### Critical Thresholds
```python
CRITICAL_THRESHOLDS = {
    'cache_hit_ratio': 85,  # Below 85%
    'connection_utilization': 90,  # Above 90% of max connections
    'disk_usage': 90,  # Above 90% disk usage
    'response_time_p95': 5000,  # Above 5 seconds
    'deadlock_rate': 10,  # More than 10 deadlocks per hour
    'rollback_percentage': 10,  # Above 10% rollback rate
}

WARNING_THRESHOLDS = {
    'cache_hit_ratio': 90,  # Below 90%
    'connection_utilization': 75,  # Above 75% of max connections
    'disk_usage': 80,  # Above 80% disk usage
    'response_time_p95': 2000,  # Above 2 seconds
    'deadlock_rate': 5,  # More than 5 deadlocks per hour
    'rollback_percentage': 5,  # Above 5% rollback rate
}

def check_thresholds(metrics):
    """Check metrics against defined thresholds"""
    alerts = []
    
    for db_name, db_stats in metrics['databases'].items():
        # Cache hit ratio
        cache_ratio = db_stats['cache_hit_ratio']
        if cache_ratio < CRITICAL_THRESHOLDS['cache_hit_ratio']:
            alerts.append({
                'database': db_name,
                'metric': 'cache_hit_ratio',
                'value': cache_ratio,
                'threshold': CRITICAL_THRESHOLDS['cache_hit_ratio'],
                'severity': 'critical'
            })
        elif cache_ratio < WARNING_THRESHOLDS['cache_hit_ratio']:
            alerts.append({
                'database': db_name,
                'metric': 'cache_hit_ratio',
                'value': cache_ratio,
                'threshold': WARNING_THRESHOLDS['cache_hit_ratio'],
                'severity': 'warning'
            })
        
        # Connection utilization
        max_connections = 100  # Get from database configuration
        connection_pct = (db_stats['connections'] / max_connections) * 100
        
        if connection_pct > CRITICAL_THRESHOLDS['connection_utilization']:
            alerts.append({
                'database': db_name,
                'metric': 'connection_utilization',
                'value': connection_pct,
                'threshold': CRITICAL_THRESHOLDS['connection_utilization'],
                'severity': 'critical'
            })
    
    return alerts
```

## Metric Analysis

### Trend Analysis
```python
def analyze_performance_trends(metrics_history, days=7):
    """Analyze performance trends over time"""
    if len(metrics_history) < 2:
        return "Insufficient data for trend analysis"
    
    # Convert to time series
    timestamps = [m['timestamp'] for m in metrics_history]
    
    trends = {}
    
    for db_name in metrics_history[0]['databases'].keys():
        cache_ratios = [m['databases'][db_name]['cache_hit_ratio'] for m in metrics_history]
        connections = [m['databases'][db_name]['connections'] for m in metrics_history]
        commits = [m['databases'][db_name]['commits'] for m in metrics_history]
        
        # Calculate trends using linear regression
        from scipy import stats
        
        # Cache hit ratio trend
        slope, intercept, r_value, p_value, std_err = stats.linregress(timestamps, cache_ratios)
        trends[f'{db_name}_cache_trend'] = {
            'slope': slope,
            'direction': 'improving' if slope > 0 else 'degrading',
            'significance': 'significant' if p_value < 0.05 else 'not_significant'
        }
        
        # Connection count trend
        slope, intercept, r_value, p_value, std_err = stats.linregress(timestamps, connections)
        trends[f'{db_name}_connection_trend'] = {
            'slope': slope,
            'direction': 'increasing' if slope > 0 else 'decreasing',
            'significance': 'significant' if p_value < 0.05 else 'not_significant'
        }
    
    return trends

def detect_anomalies(metrics_history, window_size=24):
    """Detect performance anomalies using statistical methods"""
    if len(metrics_history) < window_size:
        return []
    
    anomalies = []
    
    # Use rolling window to detect anomalies
    for i in range(window_size, len(metrics_history)):
        current_metrics = metrics_history[i]
        historical_window = metrics_history[i-window_size:i]
        
        for db_name, db_stats in current_metrics['databases'].items():
            # Check cache hit ratio anomalies
            historical_cache_ratios = [
                m['databases'][db_name]['cache_hit_ratio'] 
                for m in historical_window
            ]
            
            mean_cache = np.mean(historical_cache_ratios)
            std_cache = np.std(historical_cache_ratios)
            current_cache = db_stats['cache_hit_ratio']
            
            # Anomaly if more than 3 standard deviations from mean
            if abs(current_cache - mean_cache) > 3 * std_cache:
                anomalies.append({
                    'timestamp': current_metrics['timestamp'],
                    'database': db_name,
                    'metric': 'cache_hit_ratio',
                    'value': current_cache,
                    'expected_range': (mean_cache - 2*std_cache, mean_cache + 2*std_cache),
                    'severity': 'anomaly'
                })
    
    return anomalies
```

## Best Practices

### 1. Comprehensive Monitoring
```python
# Monitor all layers of the database stack
monitoring_layers = {
    'application': ['response_time', 'error_rate', 'request_volume'],
    'database': ['query_performance', 'connection_pool', 'cache_efficiency'],
    'system': ['cpu_usage', 'memory_usage', 'disk_io', 'network_io'],
    'storage': ['disk_space', 'iops', 'latency', 'throughput']
}

def comprehensive_health_check():
    """Perform comprehensive database health check"""
    health_status = {}
    
    # Database metrics
    db_metrics = collector.collect_database_metrics()
    health_status['database'] = analyze_database_health(db_metrics)
    
    # System metrics
    sys_metrics = collector.collect_system_metrics()
    health_status['system'] = analyze_system_health(sys_metrics)
    
    # Application metrics (if available)
    app_metrics = collect_application_metrics()
    health_status['application'] = analyze_application_health(app_metrics)
    
    # Overall health score
    health_status['overall_score'] = calculate_overall_health_score(health_status)
    
    return health_status
```

### 2. Automated Alerting
```python
def setup_automated_alerting():
    """Set up automated alerting system"""
    import smtplib
    from email.mime.text import MIMEText
    
    def send_alert(alert_data):
        """Send alert via email"""
        subject = f"Database Alert: {alert_data['severity'].upper()}"
        body = f"""
        Database: {alert_data['database']}
        Metric: {alert_data['metric']}
        Current Value: {alert_data['value']}
        Threshold: {alert_data['threshold']}
        Severity: {alert_data['severity']}
        Timestamp: {datetime.fromtimestamp(alert_data['timestamp'])}
        """
        
        msg = MIMEText(body)
        msg['Subject'] = subject
        msg['From'] = '<EMAIL>'
        msg['To'] = '<EMAIL>'
        
        # Send email (configure SMTP settings)
        # smtp_server.send_message(msg)
    
    def check_and_alert():
        """Check metrics and send alerts if needed"""
        metrics = collector.collect_database_metrics()
        alerts = check_thresholds(metrics)
        
        for alert in alerts:
            alert['timestamp'] = time.time()
            send_alert(alert)
            
            # Log alert
            print(f"ALERT: {alert['severity']} - {alert['metric']} = {alert['value']}")
    
    # Schedule regular checks
    import schedule
    schedule.every(5).minutes.do(check_and_alert)
```

### 3. Performance Reporting
```python
def generate_weekly_performance_report():
    """Generate comprehensive weekly performance report"""
    end_time = time.time()
    start_time = end_time - (7 * 24 * 3600)  # 7 days ago
    
    # Collect metrics for the week
    weekly_metrics = [
        m for m in metrics_history 
        if start_time <= m['timestamp'] <= end_time
    ]
    
    report = {
        'period': f"{datetime.fromtimestamp(start_time)} to {datetime.fromtimestamp(end_time)}",
        'summary': {},
        'trends': analyze_performance_trends(weekly_metrics),
        'anomalies': detect_anomalies(weekly_metrics),
        'top_issues': [],
        'recommendations': []
    }
    
    # Calculate summary statistics
    if weekly_metrics:
        cache_ratios = []
        connection_counts = []
        
        for metrics in weekly_metrics:
            for db_stats in metrics['databases'].values():
                cache_ratios.append(db_stats['cache_hit_ratio'])
                connection_counts.append(db_stats['connections'])
        
        report['summary'] = {
            'avg_cache_hit_ratio': np.mean(cache_ratios),
            'min_cache_hit_ratio': np.min(cache_ratios),
            'avg_connections': np.mean(connection_counts),
            'max_connections': np.max(connection_counts),
            'total_anomalies': len(report['anomalies'])
        }
    
    # Generate recommendations
    if report['summary']['avg_cache_hit_ratio'] < 90:
        report['recommendations'].append(
            "Consider increasing buffer pool size or optimizing queries to improve cache hit ratio"
        )
    
    if report['summary']['max_connections'] > 80:
        report['recommendations'].append(
            "Monitor connection usage - consider connection pooling optimization"
        )
    
    return report

# Schedule weekly reports
schedule.every().monday.at("09:00").do(generate_weekly_performance_report)
```

## 🚀 Next Steps

After mastering performance metrics:
- [Query Monitoring](./query-monitoring.md) - Monitor specific query performance
- [Resource Monitoring](./resource-monitoring.md) - Track system resource usage
- [Alerting Systems](./alerting.md) - Set up comprehensive alerting

## 📝 Practice Exercises

1. Set up comprehensive metrics collection for your database
2. Establish performance baselines for your workload
3. Create automated alerting for critical thresholds
4. Build a performance dashboard with key metrics
5. Implement anomaly detection for your metrics

Remember: Effective monitoring requires understanding your specific workload patterns and establishing appropriate baselines and thresholds for your environment!
