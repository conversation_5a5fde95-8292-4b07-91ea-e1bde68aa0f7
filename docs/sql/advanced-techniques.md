# Advanced SQL Techniques

This guide covers sophisticated SQL patterns and techniques used in complex database operations. These techniques are essential for senior developers and database architects.

## 📋 Table of Contents

1. [Complex Joins and Set Operations](#complex-joins-and-set-operations)
2. [Advanced Subqueries](#advanced-subqueries)
3. [Analytical Functions](#analytical-functions)
4. [Pivot and Unpivot Operations](#pivot-and-unpivot-operations)
5. [Dynamic SQL](#dynamic-sql)
6. [Performance Optimization Techniques](#performance-optimization-techniques)
7. [Advanced Data Manipulation](#advanced-data-manipulation)

## Complex Joins and Set Operations

### Self Joins
```sql
-- Find employees and their managers
SELECT 
    e.first_name AS employee_name,
    m.first_name AS manager_name
FROM employees e
LEFT JOIN employees m ON e.manager_id = m.employee_id;

-- Find employees in the same department
SELECT 
    e1.first_name AS employee1,
    e2.first_name AS employee2,
    e1.department
FROM employees e1
JOIN employees e2 ON e1.department = e2.department 
    AND e1.employee_id < e2.employee_id;
```

### Multiple Table Joins
```sql
SELECT 
    c.customer_name,
    o.order_date,
    p.product_name,
    oi.quantity,
    oi.unit_price
FROM customers c
JOIN orders o ON c.customer_id = o.customer_id
JOIN order_items oi ON o.order_id = oi.order_id
JOIN products p ON oi.product_id = p.product_id
WHERE o.order_date >= '2024-01-01';
```

### Set Operations
```sql
-- UNION - Combine results (removes duplicates)
SELECT first_name, last_name FROM employees
UNION
SELECT first_name, last_name FROM contractors;

-- UNION ALL - Combine results (keeps duplicates)
SELECT department FROM employees
UNION ALL
SELECT department FROM contractors;

-- INTERSECT - Common records
SELECT department FROM employees
INTERSECT
SELECT department FROM contractors;

-- EXCEPT/MINUS - Records in first query but not in second
SELECT department FROM employees
EXCEPT
SELECT department FROM contractors;
```

## Advanced Subqueries

### Correlated Subqueries
```sql
-- Find employees earning above department average
SELECT 
    first_name,
    last_name,
    salary,
    department
FROM employees e1
WHERE salary > (
    SELECT AVG(salary)
    FROM employees e2
    WHERE e2.department = e1.department
);
```

### EXISTS and NOT EXISTS
```sql
-- Find customers who have placed orders
SELECT customer_name
FROM customers c
WHERE EXISTS (
    SELECT 1
    FROM orders o
    WHERE o.customer_id = c.customer_id
);

-- Find products never ordered
SELECT product_name
FROM products p
WHERE NOT EXISTS (
    SELECT 1
    FROM order_items oi
    WHERE oi.product_id = p.product_id
);
```

### Scalar Subqueries
```sql
SELECT 
    product_name,
    price,
    (SELECT AVG(price) FROM products) AS avg_price,
    price - (SELECT AVG(price) FROM products) AS price_difference
FROM products;
```

## Analytical Functions

### Ranking Functions
```sql
SELECT 
    first_name,
    last_name,
    salary,
    department,
    ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) as row_num,
    RANK() OVER (PARTITION BY department ORDER BY salary DESC) as rank,
    DENSE_RANK() OVER (PARTITION BY department ORDER BY salary DESC) as dense_rank,
    NTILE(4) OVER (PARTITION BY department ORDER BY salary DESC) as quartile
FROM employees;
```

### Lead and Lag Functions
```sql
SELECT 
    order_date,
    total_amount,
    LAG(total_amount, 1) OVER (ORDER BY order_date) as previous_order,
    LEAD(total_amount, 1) OVER (ORDER BY order_date) as next_order,
    total_amount - LAG(total_amount, 1) OVER (ORDER BY order_date) as difference
FROM orders
ORDER BY order_date;
```

### Running Totals and Moving Averages
```sql
SELECT 
    order_date,
    daily_sales,
    SUM(daily_sales) OVER (ORDER BY order_date) as running_total,
    AVG(daily_sales) OVER (
        ORDER BY order_date 
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as moving_avg_7_days
FROM daily_sales
ORDER BY order_date;
```

## Pivot and Unpivot Operations

### Manual Pivot
```sql
SELECT 
    year,
    SUM(CASE WHEN quarter = 'Q1' THEN sales ELSE 0 END) as Q1,
    SUM(CASE WHEN quarter = 'Q2' THEN sales ELSE 0 END) as Q2,
    SUM(CASE WHEN quarter = 'Q3' THEN sales ELSE 0 END) as Q3,
    SUM(CASE WHEN quarter = 'Q4' THEN sales ELSE 0 END) as Q4
FROM quarterly_sales
GROUP BY year;
```

### Dynamic Pivot (SQL Server)
```sql
DECLARE @columns NVARCHAR(MAX), @sql NVARCHAR(MAX)

SELECT @columns = COALESCE(@columns + ',','') + QUOTENAME(quarter)
FROM (SELECT DISTINCT quarter FROM quarterly_sales) as quarters

SET @sql = '
SELECT year, ' + @columns + '
FROM (
    SELECT year, quarter, sales
    FROM quarterly_sales
) as source_table
PIVOT (
    SUM(sales)
    FOR quarter IN (' + @columns + ')
) as pivot_table'

EXEC sp_executesql @sql
```

## Dynamic SQL

### Building Dynamic Queries
```sql
-- PostgreSQL example
DO $$
DECLARE
    sql_query TEXT;
    table_name TEXT := 'employees';
    condition TEXT := 'salary > 50000';
BEGIN
    sql_query := 'SELECT * FROM ' || table_name || ' WHERE ' || condition;
    RAISE NOTICE 'Query: %', sql_query;
    EXECUTE sql_query;
END $$;
```

### Parameterized Dynamic SQL
```sql
-- SQL Server example
DECLARE @sql NVARCHAR(MAX)
DECLARE @department NVARCHAR(50) = 'Engineering'
DECLARE @min_salary DECIMAL(10,2) = 50000

SET @sql = '
SELECT first_name, last_name, salary
FROM employees
WHERE department = @dept AND salary >= @min_sal'

EXEC sp_executesql @sql, 
    N'@dept NVARCHAR(50), @min_sal DECIMAL(10,2)',
    @dept = @department,
    @min_sal = @min_salary
```

## Performance Optimization Techniques

### Query Hints and Optimization
```sql
-- Force index usage (SQL Server)
SELECT first_name, last_name
FROM employees WITH (INDEX(IX_employees_department))
WHERE department = 'Engineering';

-- Parallel query hint
SELECT /*+ PARALLEL(4) */ 
    department, COUNT(*)
FROM employees
GROUP BY department;
```

### Efficient Pagination
```sql
-- Offset-based pagination (less efficient for large offsets)
SELECT first_name, last_name, salary
FROM employees
ORDER BY employee_id
OFFSET 1000 ROWS
FETCH NEXT 20 ROWS ONLY;

-- Cursor-based pagination (more efficient)
SELECT first_name, last_name, salary, employee_id
FROM employees
WHERE employee_id > @last_seen_id
ORDER BY employee_id
LIMIT 20;
```

### Bulk Operations
```sql
-- Bulk insert with conflict resolution
INSERT INTO employees (first_name, last_name, email)
VALUES 
    ('John', 'Doe', '<EMAIL>'),
    ('Jane', 'Smith', '<EMAIL>')
ON CONFLICT (email) 
DO UPDATE SET 
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name;

-- Bulk update with JOIN
UPDATE employees 
SET salary = salary * 1.1
FROM employees e
JOIN departments d ON e.department_id = d.department_id
WHERE d.department_name = 'Engineering';
```

## Advanced Data Manipulation

### Recursive Queries (Hierarchical Data)
```sql
WITH RECURSIVE employee_hierarchy AS (
    -- Base case: top-level managers
    SELECT 
        employee_id,
        first_name,
        last_name,
        manager_id,
        0 as level,
        CAST(first_name || ' ' || last_name AS VARCHAR(1000)) as path
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    -- Recursive case: employees with managers
    SELECT 
        e.employee_id,
        e.first_name,
        e.last_name,
        e.manager_id,
        eh.level + 1,
        eh.path || ' -> ' || e.first_name || ' ' || e.last_name
    FROM employees e
    JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id
)
SELECT * FROM employee_hierarchy
ORDER BY level, path;
```

### Advanced String Operations
```sql
-- String aggregation
SELECT 
    department,
    STRING_AGG(first_name || ' ' || last_name, ', ' ORDER BY last_name) as employees
FROM employees
GROUP BY department;

-- Pattern matching with regex
SELECT first_name, last_name, email
FROM employees
WHERE email ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';

-- Text search
SELECT title, content
FROM articles
WHERE to_tsvector('english', title || ' ' || content) @@ to_tsquery('database & optimization');
```

### JSON Operations (PostgreSQL)
```sql
-- Extract JSON data
SELECT 
    id,
    data->>'name' as name,
    data->>'age' as age,
    data->'address'->>'city' as city
FROM users
WHERE data->>'department' = 'Engineering';

-- JSON aggregation
SELECT 
    department,
    json_agg(
        json_build_object(
            'name', first_name || ' ' || last_name,
            'salary', salary
        )
    ) as employees
FROM employees
GROUP BY department;
```

### Temporal Queries
```sql
-- Find overlapping date ranges
SELECT 
    p1.project_name as project1,
    p2.project_name as project2
FROM projects p1
JOIN projects p2 ON p1.project_id < p2.project_id
WHERE p1.start_date <= p2.end_date 
  AND p1.end_date >= p2.start_date;

-- Time-based aggregation
SELECT 
    DATE_TRUNC('month', order_date) as month,
    COUNT(*) as order_count,
    SUM(total_amount) as monthly_revenue
FROM orders
WHERE order_date >= CURRENT_DATE - INTERVAL '12 months'
GROUP BY DATE_TRUNC('month', order_date)
ORDER BY month;
```

## Best Practices for Advanced SQL

### 1. Use CTEs for Complex Logic
```sql
WITH monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', order_date) as month,
        SUM(total_amount) as sales
    FROM orders
    GROUP BY DATE_TRUNC('month', order_date)
),
sales_with_growth AS (
    SELECT 
        month,
        sales,
        LAG(sales) OVER (ORDER BY month) as prev_month_sales,
        (sales - LAG(sales) OVER (ORDER BY month)) / LAG(sales) OVER (ORDER BY month) * 100 as growth_rate
    FROM monthly_sales
)
SELECT * FROM sales_with_growth
WHERE growth_rate > 10;
```

### 2. Optimize Subqueries
```sql
-- Instead of correlated subquery
SELECT * FROM employees e1
WHERE salary > (SELECT AVG(salary) FROM employees e2 WHERE e2.department = e1.department);

-- Use window function
SELECT * FROM (
    SELECT *,
           AVG(salary) OVER (PARTITION BY department) as dept_avg_salary
    FROM employees
) t
WHERE salary > dept_avg_salary;
```

### 3. Handle NULL Values Properly
```sql
SELECT 
    first_name,
    last_name,
    COALESCE(middle_name, '') as middle_name,
    NULLIF(phone, '') as phone,  -- Convert empty string to NULL
    CASE 
        WHEN salary IS NULL THEN 'Not specified'
        WHEN salary = 0 THEN 'Unpaid'
        ELSE salary::TEXT
    END as salary_display
FROM employees;
```

## 🚀 Next Steps

Master these advanced techniques and then explore:
- [Query Optimization](../optimization/README.md) - Performance tuning strategies
- [Database-Specific Features](../databases/) - Leverage specific database capabilities
- [Stored Procedures](./stored-procedures.md) - Reusable database logic

## 📝 Practice Exercises

1. Create a recursive query to find all subordinates of a manager
2. Build a dynamic pivot table for sales data
3. Implement efficient pagination for large datasets
4. Write analytical queries with window functions
5. Create complex reporting queries with multiple CTEs

Remember: Advanced SQL requires practice and understanding of your specific database system's capabilities!
