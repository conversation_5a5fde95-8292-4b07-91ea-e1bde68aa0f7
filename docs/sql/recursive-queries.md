# Recursive Queries

Recursive queries are powerful SQL constructs that allow you to work with hierarchical or tree-structured data. They use Common Table Expressions (CTEs) with the RECURSIVE keyword to traverse data relationships.

## 📋 Table of Contents

1. [Basic Recursive CTE Structure](#basic-recursive-cte-structure)
2. [Hierarchical Data Examples](#hierarchical-data-examples)
3. [Graph Traversal](#graph-traversal)
4. [Time Series and Sequences](#time-series-and-sequences)
5. [Advanced Patterns](#advanced-patterns)
6. [Performance Optimization](#performance-optimization)
7. [Common Pitfalls](#common-pitfalls)

## Basic Recursive CTE Structure

### Anatomy of a Recursive Query
```sql
WITH RECURSIVE recursive_cte AS (
    -- Base case (anchor member)
    SELECT initial_columns
    FROM table_name
    WHERE base_condition
    
    UNION ALL
    
    -- Recursive case (recursive member)
    SELECT recursive_columns
    FROM table_name t
    JOIN recursive_cte r ON join_condition
    WHERE recursive_condition
)
SELECT * FROM recursive_cte;
```

### Simple Number Sequence
```sql
-- Generate numbers 1 to 10
WITH RECURSIVE number_series AS (
    -- Base case
    SELECT 1 as n
    
    UNION ALL
    
    -- Recursive case
    SELECT n + 1
    FROM number_series
    WHERE n < 10
)
SELECT * FROM number_series;
```

## Hierarchical Data Examples

### Employee Organizational Chart
```sql
-- Sample data structure
CREATE TABLE employees (
    employee_id INTEGER PRIMARY KEY,
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    manager_id INTEGER REFERENCES employees(employee_id),
    department VARCHAR(50),
    salary DECIMAL(10,2)
);

-- Find all subordinates of a manager
WITH RECURSIVE employee_hierarchy AS (
    -- Base case: Start with the CEO (no manager)
    SELECT 
        employee_id,
        first_name,
        last_name,
        manager_id,
        0 as level,
        first_name || ' ' || last_name as path,
        ARRAY[employee_id] as path_ids
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    -- Recursive case: Find direct reports
    SELECT 
        e.employee_id,
        e.first_name,
        e.last_name,
        e.manager_id,
        eh.level + 1,
        eh.path || ' -> ' || e.first_name || ' ' || e.last_name,
        eh.path_ids || e.employee_id
    FROM employees e
    JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id
    WHERE e.employee_id != ALL(eh.path_ids)  -- Prevent cycles
)
SELECT 
    level,
    REPEAT('  ', level) || first_name || ' ' || last_name as org_chart,
    path,
    array_length(path_ids, 1) as depth
FROM employee_hierarchy
ORDER BY path;
```

### Find All Managers Above an Employee
```sql
WITH RECURSIVE manager_chain AS (
    -- Base case: Start with specific employee
    SELECT 
        employee_id,
        first_name,
        last_name,
        manager_id,
        0 as level_up
    FROM employees
    WHERE employee_id = 123  -- Specific employee
    
    UNION ALL
    
    -- Recursive case: Find managers
    SELECT 
        e.employee_id,
        e.first_name,
        e.last_name,
        e.manager_id,
        mc.level_up + 1
    FROM employees e
    JOIN manager_chain mc ON e.employee_id = mc.manager_id
)
SELECT 
    level_up,
    first_name || ' ' || last_name as manager_name,
    CASE level_up
        WHEN 0 THEN 'Self'
        WHEN 1 THEN 'Direct Manager'
        WHEN 2 THEN 'Manager''s Manager'
        ELSE 'Level ' || level_up || ' Manager'
    END as relationship
FROM manager_chain
ORDER BY level_up;
```

### Category Tree Navigation
```sql
-- Product category hierarchy
CREATE TABLE categories (
    category_id INTEGER PRIMARY KEY,
    category_name VARCHAR(100),
    parent_category_id INTEGER REFERENCES categories(category_id)
);

-- Get all subcategories of a category
WITH RECURSIVE category_tree AS (
    -- Base case: Root category
    SELECT 
        category_id,
        category_name,
        parent_category_id,
        0 as depth,
        category_name as full_path,
        ARRAY[category_id] as path_ids
    FROM categories
    WHERE category_id = 1  -- Electronics category
    
    UNION ALL
    
    -- Recursive case: Child categories
    SELECT 
        c.category_id,
        c.category_name,
        c.parent_category_id,
        ct.depth + 1,
        ct.full_path || ' > ' || c.category_name,
        ct.path_ids || c.category_id
    FROM categories c
    JOIN category_tree ct ON c.parent_category_id = ct.category_id
    WHERE NOT c.category_id = ANY(ct.path_ids)  -- Prevent cycles
)
SELECT 
    category_id,
    REPEAT('  ', depth) || category_name as indented_name,
    full_path,
    depth
FROM category_tree
ORDER BY full_path;
```

## Graph Traversal

### Friend Network Analysis
```sql
-- Social network connections
CREATE TABLE friendships (
    user_id INTEGER,
    friend_id INTEGER,
    created_at TIMESTAMP,
    PRIMARY KEY (user_id, friend_id)
);

-- Find friends within N degrees of separation
WITH RECURSIVE friend_network AS (
    -- Base case: Direct friends
    SELECT 
        user_id,
        friend_id,
        1 as degree,
        ARRAY[user_id, friend_id] as path
    FROM friendships
    WHERE user_id = 123  -- Starting user
    
    UNION ALL
    
    -- Recursive case: Friends of friends
    SELECT 
        fn.user_id,
        f.friend_id,
        fn.degree + 1,
        fn.path || f.friend_id
    FROM friend_network fn
    JOIN friendships f ON fn.friend_id = f.user_id
    WHERE fn.degree < 3  -- Limit to 3 degrees
      AND NOT f.friend_id = ANY(fn.path)  -- Prevent cycles
      AND f.friend_id != 123  -- Don't include original user
)
SELECT 
    friend_id,
    MIN(degree) as closest_degree,
    COUNT(*) as connection_paths
FROM friend_network
GROUP BY friend_id
ORDER BY closest_degree, friend_id;
```

### Route Finding
```sql
-- Transportation network
CREATE TABLE routes (
    from_city VARCHAR(50),
    to_city VARCHAR(50),
    distance INTEGER,
    travel_time INTEGER
);

-- Find all possible routes between cities
WITH RECURSIVE city_routes AS (
    -- Base case: Direct routes from starting city
    SELECT 
        from_city,
        to_city,
        distance,
        travel_time,
        1 as hops,
        ARRAY[from_city, to_city] as route_path,
        distance as total_distance,
        travel_time as total_time
    FROM routes
    WHERE from_city = 'New York'
    
    UNION ALL
    
    -- Recursive case: Extended routes
    SELECT 
        cr.from_city,
        r.to_city,
        r.distance,
        r.travel_time,
        cr.hops + 1,
        cr.route_path || r.to_city,
        cr.total_distance + r.distance,
        cr.total_time + r.travel_time
    FROM city_routes cr
    JOIN routes r ON cr.to_city = r.from_city
    WHERE cr.hops < 5  -- Limit route complexity
      AND NOT r.to_city = ANY(cr.route_path)  -- Prevent cycles
)
SELECT 
    to_city as destination,
    hops,
    total_distance,
    total_time,
    array_to_string(route_path, ' -> ') as route
FROM city_routes
WHERE to_city = 'Los Angeles'  -- Destination city
ORDER BY total_distance
LIMIT 10;
```

## Time Series and Sequences

### Generate Date Ranges
```sql
-- Generate all dates in a range
WITH RECURSIVE date_series AS (
    -- Base case: Start date
    SELECT DATE '2024-01-01' as date_value
    
    UNION ALL
    
    -- Recursive case: Add one day
    SELECT date_value + INTERVAL '1 day'
    FROM date_series
    WHERE date_value < DATE '2024-12-31'
)
SELECT 
    date_value,
    EXTRACT(DOW FROM date_value) as day_of_week,
    CASE EXTRACT(DOW FROM date_value)
        WHEN 0 THEN 'Sunday'
        WHEN 1 THEN 'Monday'
        WHEN 2 THEN 'Tuesday'
        WHEN 3 THEN 'Wednesday'
        WHEN 4 THEN 'Thursday'
        WHEN 5 THEN 'Friday'
        WHEN 6 THEN 'Saturday'
    END as day_name
FROM date_series
WHERE EXTRACT(DOW FROM date_value) NOT IN (0, 6)  -- Weekdays only
ORDER BY date_value;
```

### Fibonacci Sequence
```sql
-- Generate Fibonacci numbers
WITH RECURSIVE fibonacci AS (
    -- Base case: First two Fibonacci numbers
    SELECT 
        1 as position,
        0 as current_value,
        1 as next_value
    
    UNION ALL
    
    -- Recursive case: Calculate next Fibonacci number
    SELECT 
        position + 1,
        next_value,
        current_value + next_value
    FROM fibonacci
    WHERE position < 20
)
SELECT 
    position,
    current_value as fibonacci_number
FROM fibonacci
ORDER BY position;
```

### Cumulative Calculations
```sql
-- Running totals with recursive CTE
WITH RECURSIVE running_totals AS (
    -- Base case: First record
    SELECT 
        date,
        amount,
        amount as running_total,
        ROW_NUMBER() OVER (ORDER BY date) as rn
    FROM daily_sales
    WHERE ROW_NUMBER() OVER (ORDER BY date) = 1
    
    UNION ALL
    
    -- Recursive case: Add to running total
    SELECT 
        ds.date,
        ds.amount,
        rt.running_total + ds.amount,
        ROW_NUMBER() OVER (ORDER BY ds.date)
    FROM daily_sales ds
    JOIN running_totals rt ON ds.date > rt.date
    WHERE ROW_NUMBER() OVER (ORDER BY ds.date) = rt.rn + 1
)
SELECT 
    date,
    amount,
    running_total
FROM running_totals
ORDER BY date;
```

## Advanced Patterns

### Bill of Materials (BOM) Explosion
```sql
-- Product assembly hierarchy
CREATE TABLE bill_of_materials (
    parent_part_id INTEGER,
    child_part_id INTEGER,
    quantity_required INTEGER,
    PRIMARY KEY (parent_part_id, child_part_id)
);

-- Calculate total materials needed for a product
WITH RECURSIVE bom_explosion AS (
    -- Base case: Top-level product
    SELECT 
        parent_part_id,
        child_part_id,
        quantity_required,
        1 as level,
        quantity_required as total_quantity
    FROM bill_of_materials
    WHERE parent_part_id = 1001  -- Final product
    
    UNION ALL
    
    -- Recursive case: Sub-assemblies
    SELECT 
        bom.parent_part_id,
        bom.child_part_id,
        bom.quantity_required,
        be.level + 1,
        be.total_quantity * bom.quantity_required
    FROM bill_of_materials bom
    JOIN bom_explosion be ON bom.parent_part_id = be.child_part_id
    WHERE be.level < 10  -- Prevent infinite recursion
)
SELECT 
    child_part_id as part_id,
    SUM(total_quantity) as total_required,
    MAX(level) as max_level
FROM bom_explosion
GROUP BY child_part_id
ORDER BY max_level, child_part_id;
```

### Recursive Data Cleanup
```sql
-- Clean up orphaned records recursively
WITH RECURSIVE orphan_cleanup AS (
    -- Base case: Find initial orphans
    SELECT category_id
    FROM categories c
    WHERE parent_category_id IS NOT NULL
      AND NOT EXISTS (
          SELECT 1 FROM categories p 
          WHERE p.category_id = c.parent_category_id
      )
    
    UNION ALL
    
    -- Recursive case: Find categories that become orphans
    SELECT c.category_id
    FROM categories c
    JOIN orphan_cleanup oc ON c.parent_category_id = oc.category_id
)
DELETE FROM categories
WHERE category_id IN (SELECT category_id FROM orphan_cleanup);
```

## Performance Optimization

### Limit Recursion Depth
```sql
WITH RECURSIVE limited_hierarchy AS (
    SELECT 
        employee_id,
        manager_id,
        0 as level
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    SELECT 
        e.employee_id,
        e.manager_id,
        lh.level + 1
    FROM employees e
    JOIN limited_hierarchy lh ON e.manager_id = lh.employee_id
    WHERE lh.level < 10  -- Explicit depth limit
)
SELECT * FROM limited_hierarchy;
```

### Use Indexes Effectively
```sql
-- Ensure proper indexes for recursive queries
CREATE INDEX idx_employees_manager_id ON employees(manager_id);
CREATE INDEX idx_categories_parent_id ON categories(parent_category_id);
CREATE INDEX idx_friendships_user_friend ON friendships(user_id, friend_id);
```

### Cycle Detection
```sql
WITH RECURSIVE safe_hierarchy AS (
    SELECT 
        employee_id,
        manager_id,
        ARRAY[employee_id] as path
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    SELECT 
        e.employee_id,
        e.manager_id,
        sh.path || e.employee_id
    FROM employees e
    JOIN safe_hierarchy sh ON e.manager_id = sh.employee_id
    WHERE NOT e.employee_id = ANY(sh.path)  -- Prevent cycles
      AND array_length(sh.path, 1) < 20    -- Depth limit
)
SELECT * FROM safe_hierarchy;
```

## Common Pitfalls

### 1. Infinite Recursion
```sql
-- Problem: No termination condition
WITH RECURSIVE bad_example AS (
    SELECT 1 as n
    UNION ALL
    SELECT n + 1 FROM bad_example  -- Will run forever!
)
SELECT * FROM bad_example;

-- Solution: Always include termination condition
WITH RECURSIVE good_example AS (
    SELECT 1 as n
    UNION ALL
    SELECT n + 1 FROM good_example WHERE n < 100  -- Stops at 100
)
SELECT * FROM good_example;
```

### 2. Circular References
```sql
-- Problem: Data has cycles
-- Employee A manages Employee B, Employee B manages Employee A

-- Solution: Track visited nodes
WITH RECURSIVE cycle_safe AS (
    SELECT 
        employee_id,
        manager_id,
        ARRAY[employee_id] as visited
    FROM employees
    WHERE employee_id = 1
    
    UNION ALL
    
    SELECT 
        e.employee_id,
        e.manager_id,
        cs.visited || e.employee_id
    FROM employees e
    JOIN cycle_safe cs ON e.manager_id = cs.employee_id
    WHERE NOT e.employee_id = ANY(cs.visited)  -- Skip if already visited
)
SELECT * FROM cycle_safe;
```

### 3. Performance Issues
```sql
-- Problem: Cartesian product in recursive join
-- Solution: Use specific join conditions and limits

WITH RECURSIVE optimized_query AS (
    SELECT id, parent_id, 0 as level
    FROM tree_table
    WHERE parent_id IS NULL
    
    UNION ALL
    
    SELECT t.id, t.parent_id, oq.level + 1
    FROM tree_table t
    JOIN optimized_query oq ON t.parent_id = oq.id  -- Specific join
    WHERE oq.level < 5  -- Reasonable depth limit
)
SELECT * FROM optimized_query;
```

## Database-Specific Considerations

### PostgreSQL
- Supports full recursive CTE functionality
- Good performance with proper indexing
- Can handle complex recursive patterns

### SQL Server
- Supports recursive CTEs with some limitations
- MAXRECURSION option to control depth
- Good integration with other T-SQL features

### MySQL
- Recursive CTEs available from version 8.0
- Similar syntax to PostgreSQL
- Performance considerations for large datasets

### Oracle
- Uses CONNECT BY syntax (legacy) or recursive CTEs
- START WITH and CONNECT BY PRIOR clauses
- SYS_CONNECT_BY_PATH for path building

## 🚀 Next Steps

After mastering recursive queries:
- [Advanced SQL Techniques](./advanced-techniques.md) - Complex query patterns
- [Query Optimization](../optimization/README.md) - Performance tuning
- [Database Design](../design/README.md) - Designing hierarchical structures

## 📝 Practice Exercises

1. Build an organizational chart with salary rollups
2. Create a category navigation system with breadcrumbs
3. Implement a social network analysis tool
4. Design a bill of materials explosion calculator
5. Build a route planning system with multiple criteria

Recursive queries are powerful tools for hierarchical data - practice with real datasets to master their usage and understand their performance characteristics!
