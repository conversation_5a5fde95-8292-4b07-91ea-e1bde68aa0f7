# Stored Procedures and Functions

Stored procedures and functions are reusable database objects that encapsulate business logic, improve performance, and provide better security. This guide covers their creation, usage, and best practices.

## 📋 Table of Contents

1. [Functions vs Stored Procedures](#functions-vs-stored-procedures)
2. [Creating Functions](#creating-functions)
3. [Creating Stored Procedures](#creating-stored-procedures)
4. [Parameters and Return Values](#parameters-and-return-values)
5. [Control Flow Statements](#control-flow-statements)
6. [<PERSON>rror Handling](#error-handling)
7. [Best Practices](#best-practices)
8. [Database-Specific Examples](#database-specific-examples)

## Functions vs Stored Procedures

### Functions
- **Return a single value** or table
- **Can be used in SELECT statements**
- **Cannot modify database state** (in most databases)
- **Must return a value**

### Stored Procedures
- **Can return multiple result sets**
- **Can modify database state**
- **Called with CALL or EXEC**
- **May or may not return values**

## Creating Functions

### Scalar Functions (PostgreSQL)
```sql
-- Simple calculation function
CREATE OR R<PERSON>LACE FUNCTION calculate_tax(amount DECIMAL, tax_rate DECIMAL)
RETURNS DECIMAL AS $$
BEGIN
    RETURN amount * tax_rate;
END;
$$ LANGUAGE plpgsql;

-- Usage
SELECT 
    product_name,
    price,
    calculate_tax(price, 0.08) as tax_amount,
    price + calculate_tax(price, 0.08) as total_price
FROM products;
```

### Table-Valued Functions (PostgreSQL)
```sql
-- Function returning a table
CREATE OR REPLACE FUNCTION get_employees_by_department(dept_name VARCHAR)
RETURNS TABLE(
    employee_id INTEGER,
    first_name VARCHAR,
    last_name VARCHAR,
    salary DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT e.employee_id, e.first_name, e.last_name, e.salary
    FROM employees e
    WHERE e.department = dept_name
    ORDER BY e.salary DESC;
END;
$$ LANGUAGE plpgsql;

-- Usage
SELECT * FROM get_employees_by_department('Engineering');
```

### SQL Server Functions
```sql
-- Scalar function
CREATE FUNCTION dbo.CalculateAge(@BirthDate DATE)
RETURNS INT
AS
BEGIN
    RETURN DATEDIFF(YEAR, @BirthDate, GETDATE()) - 
           CASE 
               WHEN DATEADD(YEAR, DATEDIFF(YEAR, @BirthDate, GETDATE()), @BirthDate) > GETDATE()
               THEN 1 
               ELSE 0 
           END;
END;

-- Table-valued function
CREATE FUNCTION dbo.GetTopCustomers(@TopN INT)
RETURNS TABLE
AS
RETURN (
    SELECT TOP (@TopN)
        customer_id,
        customer_name,
        total_orders,
        total_spent
    FROM (
        SELECT 
            c.customer_id,
            c.customer_name,
            COUNT(o.order_id) as total_orders,
            SUM(o.order_amount) as total_spent
        FROM customers c
        LEFT JOIN orders o ON c.customer_id = o.customer_id
        GROUP BY c.customer_id, c.customer_name
    ) customer_stats
    ORDER BY total_spent DESC
);
```

## Creating Stored Procedures

### PostgreSQL Stored Procedures
```sql
-- Simple procedure
CREATE OR REPLACE PROCEDURE update_employee_salary(
    emp_id INTEGER,
    new_salary DECIMAL
)
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE employees 
    SET salary = new_salary,
        last_modified = CURRENT_TIMESTAMP
    WHERE employee_id = emp_id;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Employee with ID % not found', emp_id;
    END IF;
    
    COMMIT;
END;
$$;

-- Call the procedure
CALL update_employee_salary(123, 75000);
```

### Complex Business Logic Procedure
```sql
CREATE OR REPLACE PROCEDURE process_monthly_payroll(
    process_month DATE
)
LANGUAGE plpgsql
AS $$
DECLARE
    emp_record RECORD;
    total_processed INTEGER := 0;
    total_amount DECIMAL := 0;
BEGIN
    -- Create payroll batch
    INSERT INTO payroll_batches (batch_date, status)
    VALUES (process_month, 'PROCESSING')
    RETURNING batch_id INTO @batch_id;
    
    -- Process each active employee
    FOR emp_record IN 
        SELECT employee_id, first_name, last_name, salary, department
        FROM employees 
        WHERE status = 'ACTIVE'
    LOOP
        -- Calculate gross pay
        DECLARE
            gross_pay DECIMAL := emp_record.salary;
            tax_amount DECIMAL := calculate_tax(gross_pay, 0.25);
            net_pay DECIMAL := gross_pay - tax_amount;
        BEGIN
            -- Insert payroll record
            INSERT INTO payroll_records (
                batch_id, employee_id, gross_pay, tax_amount, net_pay, pay_date
            ) VALUES (
                @batch_id, emp_record.employee_id, gross_pay, tax_amount, net_pay, process_month
            );
            
            total_processed := total_processed + 1;
            total_amount := total_amount + net_pay;
        END;
    END LOOP;
    
    -- Update batch summary
    UPDATE payroll_batches 
    SET 
        employee_count = total_processed,
        total_amount = total_amount,
        status = 'COMPLETED',
        processed_at = CURRENT_TIMESTAMP
    WHERE batch_id = @batch_id;
    
    RAISE NOTICE 'Processed % employees, total amount: %', total_processed, total_amount;
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        UPDATE payroll_batches SET status = 'FAILED' WHERE batch_id = @batch_id;
        RAISE;
END;
$$;
```

### SQL Server Stored Procedures
```sql
-- Procedure with output parameters
CREATE PROCEDURE dbo.GetCustomerStats
    @CustomerId INT,
    @TotalOrders INT OUTPUT,
    @TotalSpent DECIMAL(10,2) OUTPUT,
    @LastOrderDate DATE OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        @TotalOrders = COUNT(order_id),
        @TotalSpent = SUM(order_amount),
        @LastOrderDate = MAX(order_date)
    FROM orders
    WHERE customer_id = @CustomerId;
    
    -- Handle case where customer has no orders
    IF @TotalOrders IS NULL
    BEGIN
        SET @TotalOrders = 0;
        SET @TotalSpent = 0;
        SET @LastOrderDate = NULL;
    END
END;

-- Usage
DECLARE @Orders INT, @Spent DECIMAL(10,2), @LastOrder DATE;
EXEC dbo.GetCustomerStats 
    @CustomerId = 123,
    @TotalOrders = @Orders OUTPUT,
    @TotalSpent = @Spent OUTPUT,
    @LastOrderDate = @LastOrder OUTPUT;

SELECT @Orders as TotalOrders, @Spent as TotalSpent, @LastOrder as LastOrderDate;
```

## Parameters and Return Values

### Input Parameters
```sql
-- PostgreSQL
CREATE OR REPLACE FUNCTION get_employee_info(
    emp_id INTEGER,
    include_salary BOOLEAN DEFAULT FALSE
)
RETURNS TABLE(
    employee_id INTEGER,
    full_name VARCHAR,
    department VARCHAR,
    salary DECIMAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        e.employee_id,
        e.first_name || ' ' || e.last_name as full_name,
        e.department,
        CASE WHEN include_salary THEN e.salary ELSE NULL END as salary
    FROM employees e
    WHERE e.employee_id = emp_id;
END;
$$ LANGUAGE plpgsql;
```

### Output Parameters (SQL Server)
```sql
CREATE PROCEDURE dbo.CalculateOrderTotals
    @OrderId INT,
    @Subtotal DECIMAL(10,2) OUTPUT,
    @TaxAmount DECIMAL(10,2) OUTPUT,
    @Total DECIMAL(10,2) OUTPUT
AS
BEGIN
    SELECT 
        @Subtotal = SUM(quantity * unit_price),
        @TaxAmount = SUM(quantity * unit_price) * 0.08,
        @Total = SUM(quantity * unit_price) * 1.08
    FROM order_items
    WHERE order_id = @OrderId;
END;
```

## Control Flow Statements

### Conditional Logic
```sql
-- PostgreSQL
CREATE OR REPLACE FUNCTION categorize_customer(customer_id INTEGER)
RETURNS VARCHAR AS $$
DECLARE
    total_spent DECIMAL;
    customer_category VARCHAR;
BEGIN
    SELECT SUM(order_amount) INTO total_spent
    FROM orders
    WHERE customer_id = customer_id;
    
    IF total_spent IS NULL THEN
        customer_category := 'New';
    ELSIF total_spent < 100 THEN
        customer_category := 'Bronze';
    ELSIF total_spent < 500 THEN
        customer_category := 'Silver';
    ELSIF total_spent < 1000 THEN
        customer_category := 'Gold';
    ELSE
        customer_category := 'Platinum';
    END IF;
    
    RETURN customer_category;
END;
$$ LANGUAGE plpgsql;
```

### Loops
```sql
-- PostgreSQL - FOR loop
CREATE OR REPLACE PROCEDURE generate_monthly_reports(start_month DATE, end_month DATE)
LANGUAGE plpgsql
AS $$
DECLARE
    current_month DATE := start_month;
BEGIN
    WHILE current_month <= end_month LOOP
        -- Generate report for current_month
        INSERT INTO monthly_reports (report_month, total_sales, total_orders)
        SELECT 
            current_month,
            SUM(order_amount),
            COUNT(*)
        FROM orders
        WHERE DATE_TRUNC('month', order_date) = current_month;
        
        -- Move to next month
        current_month := current_month + INTERVAL '1 month';
    END LOOP;
END;
$$;

-- SQL Server - WHILE loop
CREATE PROCEDURE dbo.ProcessBacklog
AS
BEGIN
    DECLARE @OrderId INT;
    DECLARE @ProcessedCount INT = 0;
    
    WHILE EXISTS (SELECT 1 FROM orders WHERE status = 'PENDING')
    BEGIN
        SELECT TOP 1 @OrderId = order_id
        FROM orders
        WHERE status = 'PENDING'
        ORDER BY order_date;
        
        -- Process the order
        EXEC dbo.ProcessOrder @OrderId;
        
        SET @ProcessedCount = @ProcessedCount + 1;
        
        -- Safety check to prevent infinite loop
        IF @ProcessedCount > 1000
            BREAK;
    END
    
    PRINT 'Processed ' + CAST(@ProcessedCount AS VARCHAR) + ' orders';
END;
```

## Error Handling

### PostgreSQL Exception Handling
```sql
CREATE OR REPLACE PROCEDURE transfer_funds(
    from_account INTEGER,
    to_account INTEGER,
    amount DECIMAL
)
LANGUAGE plpgsql
AS $$
DECLARE
    from_balance DECIMAL;
BEGIN
    -- Start transaction
    BEGIN
        -- Check source account balance
        SELECT balance INTO from_balance
        FROM accounts
        WHERE account_id = from_account
        FOR UPDATE;
        
        IF from_balance < amount THEN
            RAISE EXCEPTION 'Insufficient funds. Balance: %, Required: %', from_balance, amount;
        END IF;
        
        -- Perform transfer
        UPDATE accounts SET balance = balance - amount WHERE account_id = from_account;
        UPDATE accounts SET balance = balance + amount WHERE account_id = to_account;
        
        -- Log transaction
        INSERT INTO transaction_log (from_account, to_account, amount, transaction_date)
        VALUES (from_account, to_account, amount, CURRENT_TIMESTAMP);
        
        COMMIT;
        RAISE NOTICE 'Transfer completed successfully';
        
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE NOTICE 'Transfer failed: %', SQLERRM;
            RAISE;
    END;
END;
$$;
```

### SQL Server Error Handling
```sql
CREATE PROCEDURE dbo.ProcessOrder
    @OrderId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate order exists
        IF NOT EXISTS (SELECT 1 FROM orders WHERE order_id = @OrderId)
        BEGIN
            RAISERROR('Order %d not found', 16, 1, @OrderId);
            RETURN;
        END
        
        -- Process order items
        UPDATE inventory 
        SET quantity = quantity - oi.quantity
        FROM inventory i
        INNER JOIN order_items oi ON i.product_id = oi.product_id
        WHERE oi.order_id = @OrderId;
        
        -- Check for negative inventory
        IF EXISTS (SELECT 1 FROM inventory WHERE quantity < 0)
        BEGIN
            RAISERROR('Insufficient inventory for order %d', 16, 1, @OrderId);
            RETURN;
        END
        
        -- Update order status
        UPDATE orders 
        SET status = 'PROCESSED', processed_date = GETDATE()
        WHERE order_id = @OrderId;
        
        COMMIT TRANSACTION;
        PRINT 'Order ' + CAST(@OrderId AS VARCHAR) + ' processed successfully';
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
    END CATCH
END;
```

## Best Practices

### 1. Use Meaningful Names
```sql
-- Good
CREATE FUNCTION calculate_customer_lifetime_value(customer_id INTEGER)
CREATE PROCEDURE process_monthly_invoices(billing_month DATE)

-- Avoid
CREATE FUNCTION calc_clv(cid INTEGER)
CREATE PROCEDURE proc_inv(bm DATE)
```

### 2. Validate Input Parameters
```sql
CREATE OR REPLACE FUNCTION get_order_details(order_id INTEGER)
RETURNS TABLE(...) AS $$
BEGIN
    -- Validate input
    IF order_id IS NULL OR order_id <= 0 THEN
        RAISE EXCEPTION 'Invalid order_id: %', order_id;
    END IF;
    
    -- Check if order exists
    IF NOT EXISTS (SELECT 1 FROM orders WHERE order_id = order_id) THEN
        RAISE EXCEPTION 'Order % not found', order_id;
    END IF;
    
    -- Return results
    RETURN QUERY SELECT ...;
END;
$$ LANGUAGE plpgsql;
```

### 3. Use Transactions Appropriately
```sql
CREATE OR REPLACE PROCEDURE update_inventory_and_orders()
LANGUAGE plpgsql
AS $$
BEGIN
    -- Start explicit transaction
    BEGIN
        -- Multiple related operations
        UPDATE inventory SET quantity = quantity - 1 WHERE product_id = 123;
        INSERT INTO order_items (order_id, product_id, quantity) VALUES (456, 123, 1);
        UPDATE orders SET total_amount = total_amount + 99.99 WHERE order_id = 456;
        
        COMMIT;
    EXCEPTION
        WHEN OTHERS THEN
            ROLLBACK;
            RAISE;
    END;
END;
$$;
```

### 4. Document Your Procedures
```sql
/*
Purpose: Calculate and update customer loyalty points based on purchase history
Parameters: 
    - customer_id: The ID of the customer to process
    - calculation_date: The date to calculate points up to (default: current date)
Returns: The total points awarded
Author: Database Team
Created: 2024-01-01
Modified: 2024-01-15 - Added bonus point calculation
*/
CREATE OR REPLACE FUNCTION update_loyalty_points(
    customer_id INTEGER,
    calculation_date DATE DEFAULT CURRENT_DATE
)
RETURNS INTEGER AS $$
DECLARE
    total_points INTEGER := 0;
    -- ... other declarations
BEGIN
    -- Implementation
END;
$$ LANGUAGE plpgsql;
```

### 5. Handle Edge Cases
```sql
CREATE OR REPLACE FUNCTION calculate_discount(
    order_amount DECIMAL,
    customer_tier VARCHAR
)
RETURNS DECIMAL AS $$
BEGIN
    -- Handle null/invalid inputs
    IF order_amount IS NULL OR order_amount <= 0 THEN
        RETURN 0;
    END IF;
    
    IF customer_tier IS NULL THEN
        customer_tier := 'REGULAR';
    END IF;
    
    -- Calculate discount based on tier
    RETURN CASE customer_tier
        WHEN 'VIP' THEN order_amount * 0.15
        WHEN 'PREMIUM' THEN order_amount * 0.10
        WHEN 'REGULAR' THEN order_amount * 0.05
        ELSE 0
    END;
END;
$$ LANGUAGE plpgsql;
```

## Database-Specific Features

### MySQL Stored Procedures
```sql
DELIMITER //

CREATE PROCEDURE GetCustomerOrders(
    IN customer_id INT,
    IN start_date DATE,
    OUT total_orders INT,
    OUT total_amount DECIMAL(10,2)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    SELECT 
        COUNT(*),
        COALESCE(SUM(order_amount), 0)
    INTO total_orders, total_amount
    FROM orders
    WHERE customer_id = customer_id
      AND order_date >= start_date;
    
    COMMIT;
END //

DELIMITER ;
```

### Oracle PL/SQL
```sql
CREATE OR REPLACE FUNCTION get_employee_bonus(
    p_employee_id IN NUMBER,
    p_year IN NUMBER
) RETURN NUMBER
IS
    v_salary NUMBER;
    v_performance_rating NUMBER;
    v_bonus NUMBER := 0;
BEGIN
    SELECT salary, performance_rating
    INTO v_salary, v_performance_rating
    FROM employees
    WHERE employee_id = p_employee_id;
    
    IF v_performance_rating >= 4.5 THEN
        v_bonus := v_salary * 0.15;
    ELSIF v_performance_rating >= 3.5 THEN
        v_bonus := v_salary * 0.10;
    ELSIF v_performance_rating >= 2.5 THEN
        v_bonus := v_salary * 0.05;
    END IF;
    
    RETURN v_bonus;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN 0;
    WHEN OTHERS THEN
        RAISE;
END;
/
```

## 🚀 Next Steps

After mastering stored procedures and functions:
- [Triggers](./triggers.md) - Automated database responses
- [Advanced SQL Techniques](./advanced-techniques.md) - Complex query patterns
- [Database Security](../security/README.md) - Securing your procedures

## 📝 Practice Exercises

1. Create a function to calculate shipping costs based on weight and distance
2. Build a procedure to process customer refunds with proper error handling
3. Implement a loyalty points calculation system
4. Create a data archiving procedure with transaction management
5. Build a reporting procedure that generates monthly sales summaries

Stored procedures and functions are powerful tools for encapsulating business logic - practice with real scenarios to master their usage!
