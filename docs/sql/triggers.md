# Triggers

Triggers are special stored procedures that automatically execute (or "fire") in response to specific database events. They are powerful tools for maintaining data integrity, auditing, and implementing complex business rules.

## 📋 Table of Contents

1. [Types of Triggers](#types-of-triggers)
2. [Creating Triggers](#creating-triggers)
3. [Trigger Events](#trigger-events)
4. [Common Use Cases](#common-use-cases)
5. [Best Practices](#best-practices)
6. [Performance Considerations](#performance-considerations)
7. [Database-Specific Examples](#database-specific-examples)

## Types of Triggers

### By Timing
- **BEFORE**: Executes before the triggering event
- **AFTER**: Executes after the triggering event
- **INSTEAD OF**: Replaces the triggering event (mainly for views)

### By Event
- **INSERT**: Fires when new rows are added
- **UPDATE**: Fires when existing rows are modified
- **DELETE**: Fires when rows are removed

## Creating Triggers

### PostgreSQL Triggers

#### Basic Audit Trigger
```sql
-- Create audit table
CREATE TABLE employee_audit (
    audit_id SERIAL PRIMARY KEY,
    employee_id INTEGER,
    action VARCHAR(10),
    old_values JSONB,
    new_values JSONB,
    changed_by VARCHAR(100),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger function
CREATE OR REPLACE FUNCTION audit_employee_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO employee_audit (employee_id, action, old_values, changed_by)
        VALUES (OLD.employee_id, 'DELETE', row_to_json(OLD), current_user);
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO employee_audit (employee_id, action, old_values, new_values, changed_by)
        VALUES (NEW.employee_id, 'UPDATE', row_to_json(OLD), row_to_json(NEW), current_user);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO employee_audit (employee_id, action, new_values, changed_by)
        VALUES (NEW.employee_id, 'INSERT', row_to_json(NEW), current_user);
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER employee_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON employees
    FOR EACH ROW
    EXECUTE FUNCTION audit_employee_changes();
```

#### Data Validation Trigger
```sql
-- Trigger function for salary validation
CREATE OR REPLACE FUNCTION validate_salary_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Prevent salary decrease of more than 20%
    IF TG_OP = 'UPDATE' AND OLD.salary IS NOT NULL THEN
        IF NEW.salary < OLD.salary * 0.8 THEN
            RAISE EXCEPTION 'Salary cannot be decreased by more than 20%%. Old: %, New: %', 
                OLD.salary, NEW.salary;
        END IF;
    END IF;
    
    -- Ensure salary is within reasonable range
    IF NEW.salary < 0 OR NEW.salary > 1000000 THEN
        RAISE EXCEPTION 'Salary must be between 0 and 1,000,000. Provided: %', NEW.salary;
    END IF;
    
    -- Auto-update last_modified timestamp
    NEW.last_modified = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER salary_validation_trigger
    BEFORE UPDATE ON employees
    FOR EACH ROW
    EXECUTE FUNCTION validate_salary_change();
```

### SQL Server Triggers

#### Audit Trigger
```sql
-- Create audit table
CREATE TABLE EmployeeAudit (
    AuditId INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeId INT,
    Action VARCHAR(10),
    OldValues NVARCHAR(MAX),
    NewValues NVARCHAR(MAX),
    ChangedBy NVARCHAR(100),
    ChangedAt DATETIME2 DEFAULT GETDATE()
);

-- Create trigger
CREATE TRIGGER tr_Employee_Audit
ON Employees
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Handle DELETE
    IF EXISTS (SELECT * FROM deleted) AND NOT EXISTS (SELECT * FROM inserted)
    BEGIN
        INSERT INTO EmployeeAudit (EmployeeId, Action, OldValues, ChangedBy)
        SELECT 
            EmployeeId,
            'DELETE',
            (SELECT * FROM deleted d WHERE d.EmployeeId = deleted.EmployeeId FOR JSON AUTO),
            SYSTEM_USER
        FROM deleted;
    END
    
    -- Handle INSERT
    IF EXISTS (SELECT * FROM inserted) AND NOT EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO EmployeeAudit (EmployeeId, Action, NewValues, ChangedBy)
        SELECT 
            EmployeeId,
            'INSERT',
            (SELECT * FROM inserted i WHERE i.EmployeeId = inserted.EmployeeId FOR JSON AUTO),
            SYSTEM_USER
        FROM inserted;
    END
    
    -- Handle UPDATE
    IF EXISTS (SELECT * FROM inserted) AND EXISTS (SELECT * FROM deleted)
    BEGIN
        INSERT INTO EmployeeAudit (EmployeeId, Action, OldValues, NewValues, ChangedBy)
        SELECT 
            i.EmployeeId,
            'UPDATE',
            (SELECT * FROM deleted d WHERE d.EmployeeId = i.EmployeeId FOR JSON AUTO),
            (SELECT * FROM inserted ins WHERE ins.EmployeeId = i.EmployeeId FOR JSON AUTO),
            SYSTEM_USER
        FROM inserted i;
    END
END;
```

#### Business Logic Trigger
```sql
CREATE TRIGGER tr_Order_StatusUpdate
ON Orders
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Update inventory when order status changes to 'SHIPPED'
    IF UPDATE(Status)
    BEGIN
        -- Reduce inventory for newly shipped orders
        UPDATE Inventory
        SET Quantity = Inventory.Quantity - oi.Quantity,
            LastModified = GETDATE()
        FROM Inventory
        INNER JOIN OrderItems oi ON Inventory.ProductId = oi.ProductId
        INNER JOIN inserted i ON oi.OrderId = i.OrderId
        INNER JOIN deleted d ON i.OrderId = d.OrderId
        WHERE i.Status = 'SHIPPED' AND d.Status != 'SHIPPED';
        
        -- Send notification for completed orders
        INSERT INTO Notifications (OrderId, NotificationType, Message, CreatedAt)
        SELECT 
            i.OrderId,
            'ORDER_COMPLETED',
            'Your order #' + CAST(i.OrderId AS VARCHAR) + ' has been completed.',
            GETDATE()
        FROM inserted i
        INNER JOIN deleted d ON i.OrderId = d.OrderId
        WHERE i.Status = 'COMPLETED' AND d.Status != 'COMPLETED';
    END
END;
```

## Trigger Events

### Row-Level vs Statement-Level

#### Row-Level Triggers (PostgreSQL)
```sql
-- Fires once for each affected row
CREATE TRIGGER update_modified_time
    BEFORE UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
```

#### Statement-Level Triggers (PostgreSQL)
```sql
-- Fires once per SQL statement
CREATE OR REPLACE FUNCTION log_bulk_changes()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO change_log (table_name, operation, row_count, timestamp)
    VALUES (TG_TABLE_NAME, TG_OP, 
            CASE TG_OP 
                WHEN 'INSERT' THEN (SELECT COUNT(*) FROM NEW)
                WHEN 'DELETE' THEN (SELECT COUNT(*) FROM OLD)
                WHEN 'UPDATE' THEN (SELECT COUNT(*) FROM NEW)
            END,
            CURRENT_TIMESTAMP);
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER bulk_change_log
    AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH STATEMENT
    EXECUTE FUNCTION log_bulk_changes();
```

### Conditional Triggers
```sql
-- PostgreSQL - Trigger with WHEN condition
CREATE TRIGGER high_value_order_alert
    AFTER INSERT ON orders
    FOR EACH ROW
    WHEN (NEW.total_amount > 10000)
    EXECUTE FUNCTION send_high_value_alert();

-- SQL Server - Conditional logic within trigger
CREATE TRIGGER tr_HighValueOrder
ON Orders
AFTER INSERT
AS
BEGIN
    IF EXISTS (SELECT 1 FROM inserted WHERE TotalAmount > 10000)
    BEGIN
        -- Send alert for high-value orders
        INSERT INTO Alerts (AlertType, Message, CreatedAt)
        SELECT 
            'HIGH_VALUE_ORDER',
            'High value order detected: $' + CAST(TotalAmount AS VARCHAR),
            GETDATE()
        FROM inserted
        WHERE TotalAmount > 10000;
    END
END;
```

## Common Use Cases

### 1. Audit Trails
```sql
-- Generic audit trigger function (PostgreSQL)
CREATE OR REPLACE FUNCTION generic_audit_trigger()
RETURNS TRIGGER AS $$
DECLARE
    audit_table_name TEXT;
BEGIN
    audit_table_name := TG_TABLE_NAME || '_audit';
    
    IF TG_OP = 'DELETE' THEN
        EXECUTE format('INSERT INTO %I (original_id, action, old_data, changed_by, changed_at) 
                       VALUES ($1, $2, $3, $4, $5)', audit_table_name)
        USING OLD.id, 'DELETE', row_to_json(OLD), current_user, CURRENT_TIMESTAMP;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        EXECUTE format('INSERT INTO %I (original_id, action, old_data, new_data, changed_by, changed_at) 
                       VALUES ($1, $2, $3, $4, $5, $6)', audit_table_name)
        USING NEW.id, 'UPDATE', row_to_json(OLD), row_to_json(NEW), current_user, CURRENT_TIMESTAMP;
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        EXECUTE format('INSERT INTO %I (original_id, action, new_data, changed_by, changed_at) 
                       VALUES ($1, $2, $3, $4, $5)', audit_table_name)
        USING NEW.id, 'INSERT', row_to_json(NEW), current_user, CURRENT_TIMESTAMP;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;
```

### 2. Data Synchronization
```sql
-- Keep denormalized fields in sync
CREATE OR REPLACE FUNCTION sync_order_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Update order total when order items change
    UPDATE orders 
    SET total_amount = (
        SELECT COALESCE(SUM(quantity * unit_price), 0)
        FROM order_items 
        WHERE order_id = COALESCE(NEW.order_id, OLD.order_id)
    ),
    last_modified = CURRENT_TIMESTAMP
    WHERE order_id = COALESCE(NEW.order_id, OLD.order_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER sync_order_totals_trigger
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION sync_order_totals();
```

### 3. Business Rule Enforcement
```sql
-- Prevent deletion of orders with certain status
CREATE OR REPLACE FUNCTION prevent_order_deletion()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IN ('SHIPPED', 'DELIVERED', 'COMPLETED') THEN
        RAISE EXCEPTION 'Cannot delete order with status: %', OLD.status;
    END IF;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER prevent_order_deletion_trigger
    BEFORE DELETE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION prevent_order_deletion();
```

### 4. Automatic Timestamping
```sql
-- Universal timestamp trigger
CREATE OR REPLACE FUNCTION update_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    
    -- Set created_at only for INSERT
    IF TG_OP = 'INSERT' THEN
        NEW.created_at = CURRENT_TIMESTAMP;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to multiple tables
CREATE TRIGGER update_timestamps_employees
    BEFORE INSERT OR UPDATE ON employees
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamps();

CREATE TRIGGER update_timestamps_orders
    BEFORE INSERT OR UPDATE ON orders
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamps();
```

### 5. Cache Invalidation
```sql
-- Invalidate cache when product data changes
CREATE OR REPLACE FUNCTION invalidate_product_cache()
RETURNS TRIGGER AS $$
BEGIN
    -- Insert cache invalidation job
    INSERT INTO cache_invalidation_queue (cache_key, action, created_at)
    VALUES ('product_' || COALESCE(NEW.product_id, OLD.product_id), 'INVALIDATE', CURRENT_TIMESTAMP);
    
    -- Also invalidate category cache if category changed
    IF TG_OP = 'UPDATE' AND OLD.category_id != NEW.category_id THEN
        INSERT INTO cache_invalidation_queue (cache_key, action, created_at)
        VALUES ('category_' || OLD.category_id, 'INVALIDATE', CURRENT_TIMESTAMP);
        INSERT INTO cache_invalidation_queue (cache_key, action, created_at)
        VALUES ('category_' || NEW.category_id, 'INVALIDATE', CURRENT_TIMESTAMP);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER product_cache_invalidation
    AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH ROW
    EXECUTE FUNCTION invalidate_product_cache();
```

## Best Practices

### 1. Keep Triggers Simple and Fast
```sql
-- Good: Simple, focused trigger
CREATE OR REPLACE FUNCTION update_last_login()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_login = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Avoid: Complex business logic in triggers
-- Instead, use stored procedures called from application code
```

### 2. Handle Errors Gracefully
```sql
CREATE OR REPLACE FUNCTION safe_audit_trigger()
RETURNS TRIGGER AS $$
BEGIN
    BEGIN
        INSERT INTO audit_log (table_name, action, data, timestamp)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW), CURRENT_TIMESTAMP);
    EXCEPTION
        WHEN OTHERS THEN
            -- Log error but don't fail the main operation
            INSERT INTO error_log (error_message, occurred_at)
            VALUES ('Audit trigger failed: ' || SQLERRM, CURRENT_TIMESTAMP);
    END;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

### 3. Avoid Recursive Triggers
```sql
-- Use flags to prevent recursion
CREATE OR REPLACE FUNCTION update_with_recursion_check()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if this is a recursive call
    IF NEW.trigger_flag = TRUE THEN
        RETURN NEW;
    END IF;
    
    -- Set flag to prevent recursion
    NEW.trigger_flag = TRUE;
    
    -- Perform update logic
    NEW.calculated_field = NEW.field1 + NEW.field2;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 4. Document Trigger Dependencies
```sql
/*
Trigger: order_status_cascade
Purpose: Updates related tables when order status changes
Dependencies: 
  - inventory table (for stock updates)
  - notifications table (for customer alerts)
  - shipping_log table (for tracking)
Affects:
  - Customer notifications
  - Inventory levels
  - Shipping workflow
*/
CREATE TRIGGER order_status_cascade
    AFTER UPDATE OF status ON orders
    FOR EACH ROW
    EXECUTE FUNCTION handle_order_status_change();
```

## Performance Considerations

### 1. Minimize Trigger Logic
```sql
-- Instead of complex calculations in trigger
CREATE OR REPLACE FUNCTION queue_calculation()
RETURNS TRIGGER AS $$
BEGIN
    -- Queue the calculation for background processing
    INSERT INTO calculation_queue (entity_type, entity_id, calculation_type)
    VALUES ('order', NEW.order_id, 'total_recalc');
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 2. Use Conditional Logic
```sql
-- Only fire trigger when relevant columns change
CREATE TRIGGER update_search_index
    AFTER UPDATE OF title, description, tags ON products
    FOR EACH ROW
    EXECUTE FUNCTION update_product_search_index();
```

### 3. Batch Operations
```sql
-- Statement-level trigger for bulk operations
CREATE OR REPLACE FUNCTION batch_update_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update statistics once per statement, not per row
    INSERT INTO stats_update_queue (table_name, update_type, timestamp)
    VALUES (TG_TABLE_NAME, 'bulk_update', CURRENT_TIMESTAMP);
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER batch_stats_update
    AFTER INSERT OR UPDATE OR DELETE ON large_table
    FOR EACH STATEMENT
    EXECUTE FUNCTION batch_update_stats();
```

## Database-Specific Features

### MySQL Triggers
```sql
DELIMITER //

CREATE TRIGGER before_employee_update
    BEFORE UPDATE ON employees
    FOR EACH ROW
BEGIN
    -- Validate salary increase
    IF NEW.salary > OLD.salary * 1.5 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Salary increase cannot exceed 50%';
    END IF;
    
    -- Update timestamp
    SET NEW.last_modified = NOW();
END //

DELIMITER ;
```

### Oracle Triggers
```sql
CREATE OR REPLACE TRIGGER employee_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON employees
    FOR EACH ROW
DECLARE
    v_action VARCHAR2(10);
BEGIN
    IF INSERTING THEN
        v_action := 'INSERT';
        INSERT INTO employee_audit (employee_id, action, new_values, changed_by)
        VALUES (:NEW.employee_id, v_action, 
                'Name: ' || :NEW.first_name || ' ' || :NEW.last_name, USER);
    ELSIF UPDATING THEN
        v_action := 'UPDATE';
        INSERT INTO employee_audit (employee_id, action, old_values, new_values, changed_by)
        VALUES (:NEW.employee_id, v_action,
                'Old Salary: ' || :OLD.salary,
                'New Salary: ' || :NEW.salary, USER);
    ELSIF DELETING THEN
        v_action := 'DELETE';
        INSERT INTO employee_audit (employee_id, action, old_values, changed_by)
        VALUES (:OLD.employee_id, v_action,
                'Name: ' || :OLD.first_name || ' ' || :OLD.last_name, USER);
    END IF;
END;
/
```

## Debugging and Troubleshooting

### 1. Trigger Logging
```sql
-- Add logging to triggers for debugging
CREATE OR REPLACE FUNCTION debug_trigger()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO trigger_debug_log (trigger_name, operation, table_name, row_data, timestamp)
    VALUES (TG_NAME, TG_OP, TG_TABLE_NAME, 
            CASE WHEN NEW IS NOT NULL THEN row_to_json(NEW) ELSE row_to_json(OLD) END,
            CURRENT_TIMESTAMP);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;
```

### 2. Disable/Enable Triggers
```sql
-- Disable trigger temporarily
ALTER TABLE employees DISABLE TRIGGER employee_audit_trigger;

-- Re-enable trigger
ALTER TABLE employees ENABLE TRIGGER employee_audit_trigger;

-- Disable all triggers on a table
ALTER TABLE employees DISABLE TRIGGER ALL;
```

## 🚀 Next Steps

After mastering triggers:
- [Advanced SQL Techniques](./advanced-techniques.md) - Complex query patterns
- [Database Security](../security/README.md) - Securing your database
- [Performance Optimization](../optimization/README.md) - Tuning database performance

## 📝 Practice Exercises

1. Create an audit system using triggers for a multi-table application
2. Implement automatic data validation triggers with proper error handling
3. Build a cache invalidation system using triggers
4. Create triggers for maintaining denormalized data consistency
5. Implement a comprehensive logging system with triggers

Triggers are powerful but should be used judiciously - practice with real scenarios to understand their impact on performance and maintainability!
