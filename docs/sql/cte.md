# Common Table Expressions (CTEs) and Subqueries

Common Table Expressions (CTEs) provide a way to write more readable and maintainable SQL by creating temporary named result sets. This guide covers CTEs, subqueries, and their practical applications.

## 📋 Table of Contents

1. [Basic CTE Syntax](#basic-cte-syntax)
2. [Multiple CTEs](#multiple-ctes)
3. [Recursive CTEs](#recursive-ctes)
4. [Subqueries vs CTEs](#subqueries-vs-ctes)
5. [Advanced CTE Patterns](#advanced-cte-patterns)
6. [Performance Considerations](#performance-considerations)
7. [Real-World Examples](#real-world-examples)

## Basic CTE Syntax

### Simple CTE
```sql
WITH cte_name AS (
    SELECT column1, column2, ...
    FROM table_name
    WHERE condition
)
SELECT *
FROM cte_name
WHERE another_condition;
```

### Example: Employee Analysis
```sql
WITH high_earners AS (
    SELECT 
        first_name,
        last_name,
        department,
        salary
    FROM employees
    WHERE salary > 75000
)
SELECT 
    department,
    COUNT(*) as high_earner_count,
    AVG(salary) as avg_high_earner_salary
FROM high_earners
GROUP BY department;
```

## Multiple CTEs

You can define multiple CTEs in a single query:

```sql
WITH 
department_stats AS (
    SELECT 
        department,
        COUNT(*) as employee_count,
        AVG(salary) as avg_salary,
        MAX(salary) as max_salary
    FROM employees
    GROUP BY department
),
high_performers AS (
    SELECT 
        employee_id,
        first_name,
        last_name,
        department,
        salary,
        performance_rating
    FROM employees
    WHERE performance_rating >= 4.0
)
SELECT 
    ds.department,
    ds.employee_count,
    ds.avg_salary,
    COUNT(hp.employee_id) as high_performers,
    COUNT(hp.employee_id)::FLOAT / ds.employee_count * 100 as high_performer_percentage
FROM department_stats ds
LEFT JOIN high_performers hp ON ds.department = hp.department
GROUP BY ds.department, ds.employee_count, ds.avg_salary
ORDER BY high_performer_percentage DESC;
```

## Recursive CTEs

Recursive CTEs are used for hierarchical or tree-structured data:

### Basic Recursive Structure
```sql
WITH RECURSIVE cte_name AS (
    -- Base case (anchor)
    SELECT initial_columns
    FROM table_name
    WHERE base_condition
    
    UNION ALL
    
    -- Recursive case
    SELECT recursive_columns
    FROM table_name t
    JOIN cte_name c ON join_condition
    WHERE recursive_condition
)
SELECT * FROM cte_name;
```

### Example: Employee Hierarchy
```sql
WITH RECURSIVE employee_hierarchy AS (
    -- Base case: Find all top-level managers (no manager)
    SELECT 
        employee_id,
        first_name,
        last_name,
        manager_id,
        0 as level,
        first_name || ' ' || last_name as path
    FROM employees
    WHERE manager_id IS NULL
    
    UNION ALL
    
    -- Recursive case: Find employees who report to someone in the hierarchy
    SELECT 
        e.employee_id,
        e.first_name,
        e.last_name,
        e.manager_id,
        eh.level + 1,
        eh.path || ' -> ' || e.first_name || ' ' || e.last_name
    FROM employees e
    JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id
    WHERE eh.level < 10  -- Prevent infinite recursion
)
SELECT 
    level,
    REPEAT('  ', level) || first_name || ' ' || last_name as indented_name,
    path
FROM employee_hierarchy
ORDER BY level, last_name;
```

### Example: Category Tree
```sql
WITH RECURSIVE category_tree AS (
    -- Base case: Root categories
    SELECT 
        category_id,
        category_name,
        parent_category_id,
        0 as depth,
        category_name as full_path
    FROM categories
    WHERE parent_category_id IS NULL
    
    UNION ALL
    
    -- Recursive case: Child categories
    SELECT 
        c.category_id,
        c.category_name,
        c.parent_category_id,
        ct.depth + 1,
        ct.full_path || ' > ' || c.category_name
    FROM categories c
    JOIN category_tree ct ON c.parent_category_id = ct.category_id
)
SELECT 
    category_id,
    REPEAT('  ', depth) || category_name as indented_name,
    full_path,
    depth
FROM category_tree
ORDER BY full_path;
```

## Subqueries vs CTEs

### Scalar Subqueries
```sql
-- Subquery approach
SELECT 
    first_name,
    last_name,
    salary,
    (SELECT AVG(salary) FROM employees) as company_avg_salary
FROM employees
WHERE salary > (SELECT AVG(salary) FROM employees);

-- CTE approach (more readable for complex logic)
WITH company_stats AS (
    SELECT AVG(salary) as avg_salary
    FROM employees
)
SELECT 
    e.first_name,
    e.last_name,
    e.salary,
    cs.avg_salary as company_avg_salary
FROM employees e
CROSS JOIN company_stats cs
WHERE e.salary > cs.avg_salary;
```

### Correlated Subqueries
```sql
-- Find employees earning above their department average
-- Subquery approach
SELECT 
    first_name,
    last_name,
    department,
    salary
FROM employees e1
WHERE salary > (
    SELECT AVG(salary)
    FROM employees e2
    WHERE e2.department = e1.department
);

-- CTE approach
WITH department_averages AS (
    SELECT 
        department,
        AVG(salary) as avg_salary
    FROM employees
    GROUP BY department
)
SELECT 
    e.first_name,
    e.last_name,
    e.department,
    e.salary
FROM employees e
JOIN department_averages da ON e.department = da.department
WHERE e.salary > da.avg_salary;
```

## Advanced CTE Patterns

### Data Transformation Pipeline
```sql
WITH 
-- Step 1: Clean and standardize data
cleaned_data AS (
    SELECT 
        customer_id,
        UPPER(TRIM(customer_name)) as customer_name,
        LOWER(TRIM(email)) as email,
        order_date,
        order_amount
    FROM raw_orders
    WHERE order_amount > 0
      AND email IS NOT NULL
),
-- Step 2: Add calculated fields
enriched_data AS (
    SELECT 
        *,
        EXTRACT(YEAR FROM order_date) as order_year,
        EXTRACT(MONTH FROM order_date) as order_month,
        CASE 
            WHEN order_amount < 100 THEN 'Small'
            WHEN order_amount < 500 THEN 'Medium'
            ELSE 'Large'
        END as order_size
    FROM cleaned_data
),
-- Step 3: Aggregate by customer
customer_summary AS (
    SELECT 
        customer_id,
        customer_name,
        email,
        COUNT(*) as total_orders,
        SUM(order_amount) as total_spent,
        AVG(order_amount) as avg_order_value,
        MIN(order_date) as first_order_date,
        MAX(order_date) as last_order_date
    FROM enriched_data
    GROUP BY customer_id, customer_name, email
)
SELECT 
    *,
    CASE 
        WHEN total_spent > 1000 THEN 'VIP'
        WHEN total_spent > 500 THEN 'Premium'
        ELSE 'Regular'
    END as customer_tier
FROM customer_summary
ORDER BY total_spent DESC;
```

### Window Functions with CTEs
```sql
WITH 
monthly_sales AS (
    SELECT 
        DATE_TRUNC('month', order_date) as month,
        SUM(order_amount) as monthly_total
    FROM orders
    GROUP BY DATE_TRUNC('month', order_date)
),
sales_with_trends AS (
    SELECT 
        month,
        monthly_total,
        LAG(monthly_total) OVER (ORDER BY month) as prev_month,
        AVG(monthly_total) OVER (
            ORDER BY month 
            ROWS BETWEEN 2 PRECEDING AND CURRENT ROW
        ) as three_month_avg
    FROM monthly_sales
)
SELECT 
    month,
    monthly_total,
    prev_month,
    monthly_total - prev_month as month_over_month_change,
    CASE 
        WHEN prev_month IS NULL THEN NULL
        ELSE (monthly_total - prev_month) / prev_month * 100
    END as month_over_month_pct,
    three_month_avg,
    monthly_total - three_month_avg as deviation_from_avg
FROM sales_with_trends
ORDER BY month;
```

### Pivot Operations with CTEs
```sql
WITH 
quarterly_sales AS (
    SELECT 
        product_category,
        EXTRACT(YEAR FROM order_date) as year,
        CASE 
            WHEN EXTRACT(MONTH FROM order_date) BETWEEN 1 AND 3 THEN 'Q1'
            WHEN EXTRACT(MONTH FROM order_date) BETWEEN 4 AND 6 THEN 'Q2'
            WHEN EXTRACT(MONTH FROM order_date) BETWEEN 7 AND 9 THEN 'Q3'
            ELSE 'Q4'
        END as quarter,
        SUM(order_amount) as sales
    FROM orders
    GROUP BY product_category, year, quarter
)
SELECT 
    product_category,
    year,
    SUM(CASE WHEN quarter = 'Q1' THEN sales ELSE 0 END) as Q1_sales,
    SUM(CASE WHEN quarter = 'Q2' THEN sales ELSE 0 END) as Q2_sales,
    SUM(CASE WHEN quarter = 'Q3' THEN sales ELSE 0 END) as Q3_sales,
    SUM(CASE WHEN quarter = 'Q4' THEN sales ELSE 0 END) as Q4_sales,
    SUM(sales) as total_annual_sales
FROM quarterly_sales
GROUP BY product_category, year
ORDER BY product_category, year;
```

## Performance Considerations

### 1. CTE Materialization
```sql
-- Some databases materialize CTEs, others inline them
-- PostgreSQL: CTEs are materialized by default (can be inefficient)
-- SQL Server: CTEs are inlined (can be more efficient)

-- Force inlining in PostgreSQL (12+)
WITH cte_name AS NOT MATERIALIZED (
    SELECT * FROM large_table WHERE condition
)
SELECT * FROM cte_name WHERE another_condition;

-- Force materialization in PostgreSQL
WITH cte_name AS MATERIALIZED (
    SELECT * FROM table_name WHERE expensive_function(column) > 0
)
SELECT * FROM cte_name WHERE simple_condition;
```

### 2. Recursive CTE Limits
```sql
-- Always include a termination condition
WITH RECURSIVE hierarchy AS (
    SELECT id, parent_id, name, 0 as level
    FROM categories
    WHERE parent_id IS NULL
    
    UNION ALL
    
    SELECT c.id, c.parent_id, c.name, h.level + 1
    FROM categories c
    JOIN hierarchy h ON c.parent_id = h.id
    WHERE h.level < 100  -- Prevent infinite recursion
)
SELECT * FROM hierarchy;
```

### 3. Index Usage
```sql
-- Ensure proper indexes for CTE queries
CREATE INDEX idx_employees_department ON employees(department);
CREATE INDEX idx_employees_manager_id ON employees(manager_id);
CREATE INDEX idx_orders_date ON orders(order_date);

-- CTEs can benefit from the same indexes as regular queries
WITH recent_orders AS (
    SELECT * 
    FROM orders 
    WHERE order_date >= CURRENT_DATE - INTERVAL '30 days'  -- Uses idx_orders_date
)
SELECT * FROM recent_orders;
```

## Real-World Examples

### Customer Lifetime Value Analysis
```sql
WITH 
customer_orders AS (
    SELECT 
        customer_id,
        order_date,
        order_amount,
        ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY order_date) as order_number
    FROM orders
),
customer_metrics AS (
    SELECT 
        customer_id,
        COUNT(*) as total_orders,
        SUM(order_amount) as total_spent,
        AVG(order_amount) as avg_order_value,
        MIN(order_date) as first_order_date,
        MAX(order_date) as last_order_date,
        MAX(order_date) - MIN(order_date) as customer_lifespan_days
    FROM customer_orders
    GROUP BY customer_id
),
customer_segments AS (
    SELECT 
        *,
        CASE 
            WHEN total_orders = 1 THEN 'One-time'
            WHEN total_orders BETWEEN 2 AND 5 THEN 'Occasional'
            WHEN total_orders BETWEEN 6 AND 15 THEN 'Regular'
            ELSE 'Frequent'
        END as customer_segment,
        CASE 
            WHEN customer_lifespan_days = 0 THEN NULL
            ELSE total_spent / (customer_lifespan_days + 1) * 365
        END as estimated_annual_value
    FROM customer_metrics
)
SELECT 
    customer_segment,
    COUNT(*) as customer_count,
    AVG(total_orders) as avg_orders_per_customer,
    AVG(total_spent) as avg_total_spent,
    AVG(avg_order_value) as avg_order_value,
    AVG(estimated_annual_value) as avg_estimated_annual_value
FROM customer_segments
GROUP BY customer_segment
ORDER BY avg_estimated_annual_value DESC NULLS LAST;
```

### Inventory Analysis
```sql
WITH 
current_inventory AS (
    SELECT 
        product_id,
        product_name,
        category,
        current_stock,
        reorder_level,
        unit_cost
    FROM products
    WHERE active = true
),
recent_sales AS (
    SELECT 
        product_id,
        SUM(quantity) as units_sold_30d,
        COUNT(DISTINCT order_date) as days_with_sales
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.order_id
    WHERE o.order_date >= CURRENT_DATE - INTERVAL '30 days'
    GROUP BY product_id
),
inventory_analysis AS (
    SELECT 
        ci.*,
        COALESCE(rs.units_sold_30d, 0) as units_sold_30d,
        COALESCE(rs.days_with_sales, 0) as days_with_sales,
        CASE 
            WHEN COALESCE(rs.units_sold_30d, 0) = 0 THEN NULL
            ELSE ci.current_stock / (rs.units_sold_30d / 30.0)
        END as days_of_inventory,
        ci.current_stock * ci.unit_cost as inventory_value
    FROM current_inventory ci
    LEFT JOIN recent_sales rs ON ci.product_id = rs.product_id
)
SELECT 
    category,
    COUNT(*) as product_count,
    SUM(inventory_value) as total_inventory_value,
    SUM(CASE WHEN current_stock <= reorder_level THEN 1 ELSE 0 END) as products_need_reorder,
    SUM(CASE WHEN days_of_inventory < 7 THEN 1 ELSE 0 END) as products_low_stock,
    SUM(CASE WHEN days_of_inventory > 90 THEN 1 ELSE 0 END) as products_excess_stock,
    AVG(days_of_inventory) as avg_days_of_inventory
FROM inventory_analysis
GROUP BY category
ORDER BY total_inventory_value DESC;
```

## Best Practices

### 1. Use Descriptive Names
```sql
-- Good
WITH high_value_customers AS (
    SELECT customer_id, total_spent
    FROM customer_summary
    WHERE total_spent > 1000
)

-- Avoid
WITH cte1 AS (
    SELECT c, ts
    FROM cs
    WHERE ts > 1000
)
```

### 2. Break Complex Logic into Steps
```sql
WITH 
-- Step 1: Data cleaning
clean_data AS (...),
-- Step 2: Calculations
calculated_data AS (...),
-- Step 3: Aggregations
summary_data AS (...)
SELECT * FROM summary_data;
```

### 3. Comment Complex CTEs
```sql
WITH 
-- Calculate customer metrics for the last 12 months
customer_metrics AS (
    SELECT 
        customer_id,
        COUNT(*) as order_count,
        SUM(order_amount) as total_spent
    FROM orders
    WHERE order_date >= CURRENT_DATE - INTERVAL '12 months'
    GROUP BY customer_id
),
-- Identify high-value customers (top 20% by spending)
high_value_customers AS (
    SELECT 
        customer_id,
        total_spent,
        NTILE(5) OVER (ORDER BY total_spent DESC) as spending_quintile
    FROM customer_metrics
)
SELECT * FROM high_value_customers WHERE spending_quintile = 1;
```

## 🚀 Next Steps

After mastering CTEs, explore:
- [Recursive Queries](./recursive-queries.md) - Deep dive into hierarchical data
- [Advanced SQL Techniques](./advanced-techniques.md) - Complex query patterns
- [Query Optimization](../optimization/README.md) - Performance tuning

## 📝 Practice Exercises

1. Create a multi-step data transformation pipeline using CTEs
2. Build a recursive CTE for organizational hierarchy analysis
3. Implement a customer segmentation analysis with multiple CTEs
4. Create a sales trend analysis using CTEs and window functions
5. Build an inventory management report with complex business logic

CTEs are powerful tools for writing maintainable and readable SQL - practice with real datasets to master their usage!
