# SQL Fundamentals

This section covers SQL (Structured Query Language) from basic commands to advanced techniques. Whether you're a beginner or looking to master complex SQL operations, you'll find comprehensive guides here.

## 📚 Contents

### Basic SQL
- [Basic SQL Commands](./basic-commands.md) - SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, DROP
- [Data Types and Constraints](./data-types.md) - Understanding different data types and constraints
- [Joins and Relationships](./joins.md) - INNER, LEFT, RIGHT, FULL OUTER joins

### Intermediate SQL
- [Subqueries and CTEs](./cte.md) - Common Table Expressions and subquery patterns
- [Window Functions](./window-functions.md) - ROW_NUMBER, RANK, LAG, LEAD, and more
- [Aggregate Functions](./aggregates.md) - GROUP BY, HAVING, and aggregate operations

### Advanced SQL
- [Advanced SQL Techniques](./advanced-techniques.md) - Complex queries and optimization
- [Recursive Queries](./recursive-queries.md) - Working with hierarchical data
- [JSON Operations](./json-operations.md) - JSON functions and operations
- [Stored Procedures and Functions](./stored-procedures.md) - Creating reusable database logic
- [Triggers](./triggers.md) - Automated database responses to events

## 🎯 Learning Path

### Beginner (Start Here)
1. [Basic SQL Commands](./basic-commands.md)
2. [Data Types and Constraints](./data-types.md)
3. [Joins and Relationships](./joins.md)

### Intermediate
4. [Aggregate Functions](./aggregates.md)
5. [Subqueries and CTEs](./cte.md)
6. [Window Functions](./window-functions.md)

### Advanced
7. [Advanced SQL Techniques](./advanced-techniques.md)
8. [Recursive Queries](./recursive-queries.md)
9. [JSON Operations](./json-operations.md)
10. [Stored Procedures and Functions](./stored-procedures.md)
11. [Triggers](./triggers.md)

## 💡 Key Concepts

### SQL Standards
- **ANSI SQL**: Standard SQL syntax that works across different databases
- **Database-Specific Extensions**: Each database has its own extensions and optimizations
- **SQL Dialects**: PostgreSQL, MySQL, SQL Server, Oracle each have slight variations

### Best Practices
- Use meaningful table and column names
- Always use proper indentation and formatting
- Comment complex queries
- Use parameterized queries to prevent SQL injection
- Optimize queries for performance
- Use appropriate data types
- Implement proper indexing strategies

### Common Patterns
- **CRUD Operations**: Create, Read, Update, Delete
- **Data Aggregation**: Summarizing data with GROUP BY
- **Data Transformation**: Converting data formats and structures
- **Data Validation**: Ensuring data integrity with constraints
- **Reporting Queries**: Complex queries for business intelligence

## 🔧 Tools and Resources

### SQL Editors and IDEs
- **Database-specific tools**: pgAdmin (PostgreSQL), MySQL Workbench, SQL Server Management Studio
- **Universal tools**: DBeaver, DataGrip, Azure Data Studio
- **Online tools**: DB Fiddle, SQLiteOnline, W3Schools SQL Tryit

### Practice Platforms
- LeetCode Database problems
- HackerRank SQL challenges
- SQLBolt interactive lessons
- W3Schools SQL Tutorial

## 📖 Quick Reference

### Basic Syntax
```sql
-- Select data
SELECT column1, column2 FROM table_name WHERE condition;

-- Insert data
INSERT INTO table_name (column1, column2) VALUES (value1, value2);

-- Update data
UPDATE table_name SET column1 = value1 WHERE condition;

-- Delete data
DELETE FROM table_name WHERE condition;
```

### Common Functions
```sql
-- String functions
UPPER(), LOWER(), SUBSTRING(), CONCAT(), LENGTH()

-- Date functions
NOW(), CURRENT_DATE, DATE_ADD(), DATE_SUB(), EXTRACT()

-- Numeric functions
COUNT(), SUM(), AVG(), MIN(), MAX(), ROUND(), ABS()

-- Conditional functions
CASE WHEN ... THEN ... ELSE ... END
COALESCE(), NULLIF(), ISNULL()
```

## 🚀 Next Steps

After mastering SQL fundamentals, consider exploring:
- [Query Optimization](../optimization/README.md) - Making your queries faster
- [Database Design](../design/README.md) - Designing efficient database schemas
- [Database-Specific Features](../databases/) - Leveraging specific database capabilities

## 📝 Examples and Exercises

Each guide includes:
- **Practical examples** with real-world scenarios
- **Hands-on exercises** to practice concepts
- **Common mistakes** and how to avoid them
- **Performance tips** for each technique
- **Database-specific variations** where applicable

Start with [Basic SQL Commands](./basic-commands.md) to begin your SQL journey!
