# JSON Operations in SQL

Modern databases provide extensive support for JSON data types and operations. This guide covers JSON handling across different database systems, from basic operations to advanced querying and manipulation.

## 📋 Table of Contents

1. [JSON Data Types](#json-data-types)
2. [Creating and Inserting JSON](#creating-and-inserting-json)
3. [Querying JSON Data](#querying-json-data)
4. [Modifying JSON Data](#modifying-json-data)
5. [JSON Aggregation](#json-aggregation)
6. [Indexing JSON Data](#indexing-json-data)
7. [Database-Specific Features](#database-specific-features)

## JSON Data Types

### PostgreSQL
```sql
-- JSON vs JSONB
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    attributes JSON,        -- Stores exact text representation
    metadata JSONB         -- Stores binary representation (recommended)
);

-- JSONB advantages:
-- - Faster processing
-- - Supports indexing
-- - Automatic validation
-- - Removes duplicate keys and whitespace
```

### MySQL
```sql
-- JSON data type (MySQL 5.7+)
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    attributes JSON
);
```

### SQL Server
```sql
-- No native JSON type, but JSON functions work with NVARCHAR
CREATE TABLE products (
    id INT IDENTITY(1,1) PRIMARY KEY,
    name NVARCHAR(100),
    attributes NVARCHAR(MAX) CHECK (ISJSON(attributes) = 1)
);
```

## Creating and Inserting JSON

### PostgreSQL
```sql
-- Insert JSON data
INSERT INTO products (name, attributes, metadata) VALUES
('Laptop', 
 '{"brand": "Dell", "cpu": "Intel i7", "ram": "16GB"}',
 '{"category": "electronics", "tags": ["computer", "portable"], "price": 999.99}'
),
('Phone',
 '{"brand": "Apple", "model": "iPhone 14", "storage": "128GB"}',
 '{"category": "electronics", "tags": ["mobile", "smartphone"], "price": 799.99}'
);

-- Build JSON from SQL data
SELECT 
    json_build_object(
        'id', id,
        'name', name,
        'created_at', created_at
    ) as product_json
FROM products;

-- Build JSON array
SELECT json_agg(
    json_build_object(
        'name', name,
        'price', (metadata->>'price')::numeric
    )
) as products_array
FROM products;
```

### MySQL
```sql
-- Insert JSON data
INSERT INTO products (name, attributes) VALUES
('Laptop', JSON_OBJECT('brand', 'Dell', 'cpu', 'Intel i7', 'ram', '16GB')),
('Phone', JSON_OBJECT('brand', 'Apple', 'model', 'iPhone 14', 'storage', '128GB'));

-- Build JSON from columns
SELECT JSON_OBJECT(
    'id', id,
    'name', name,
    'attributes', attributes
) as product_json
FROM products;

-- Create JSON array
SELECT JSON_ARRAYAGG(
    JSON_OBJECT('name', name, 'brand', JSON_EXTRACT(attributes, '$.brand'))
) as products
FROM products;
```

### SQL Server
```sql
-- Insert JSON data
INSERT INTO products (name, attributes) VALUES
('Laptop', '{"brand": "Dell", "cpu": "Intel i7", "ram": "16GB"}'),
('Phone', '{"brand": "Apple", "model": "iPhone 14", "storage": "128GB"}');

-- Build JSON from columns
SELECT 
    id,
    name,
    attributes,
    (
        SELECT id, name, attributes
        FOR JSON PATH, WITHOUT_ARRAY_WRAPPER
    ) as product_json
FROM products;
```

## Querying JSON Data

### PostgreSQL JSON Operators
```sql
-- Extract JSON field (returns JSON)
SELECT name, attributes->'brand' as brand_json
FROM products;

-- Extract JSON field as text (returns TEXT)
SELECT name, attributes->>'brand' as brand_text
FROM products;

-- Extract nested values
SELECT name, metadata->'tags'->0 as first_tag
FROM products;

-- Path-based extraction
SELECT name, metadata#>'{tags,0}' as first_tag_path
FROM products;

-- Check if key exists
SELECT name
FROM products
WHERE metadata ? 'price';

-- Check if any of the keys exist
SELECT name
FROM products
WHERE metadata ?| array['price', 'cost'];

-- Check if all keys exist
SELECT name
FROM products
WHERE metadata ?& array['category', 'tags'];

-- Contains operator
SELECT name
FROM products
WHERE metadata @> '{"category": "electronics"}';

-- Contained by operator
SELECT name
FROM products
WHERE '{"category": "electronics"}' <@ metadata;
```

### Advanced PostgreSQL JSON Queries
```sql
-- Extract array elements
SELECT 
    name,
    jsonb_array_elements_text(metadata->'tags') as tag
FROM products
WHERE jsonb_typeof(metadata->'tags') = 'array';

-- JSON path queries (PostgreSQL 12+)
SELECT name, jsonb_path_query(metadata, '$.tags[*]') as tags
FROM products;

-- Filter with JSON path
SELECT name
FROM products
WHERE jsonb_path_exists(metadata, '$.tags[*] ? (@ == "computer")');

-- Complex path expressions
SELECT 
    name,
    jsonb_path_query_array(metadata, '$.tags[*] ? (@ like_regex "^[cm]")') as filtered_tags
FROM products;
```

### MySQL JSON Functions
```sql
-- Extract values
SELECT 
    name,
    JSON_EXTRACT(attributes, '$.brand') as brand,
    attributes->'$.brand' as brand_shorthand,
    attributes->>'$.brand' as brand_unquoted
FROM products;

-- Search in JSON arrays
SELECT name
FROM products
WHERE JSON_SEARCH(attributes, 'one', 'Apple') IS NOT NULL;

-- Check if path exists
SELECT name
FROM products
WHERE JSON_EXTRACT(attributes, '$.brand') IS NOT NULL;

-- JSON table function
SELECT p.name, jt.*
FROM products p
JOIN JSON_TABLE(
    attributes,
    '$' COLUMNS (
        brand VARCHAR(50) PATH '$.brand',
        model VARCHAR(50) PATH '$.model',
        storage VARCHAR(20) PATH '$.storage'
    )
) as jt;
```

### SQL Server JSON Functions
```sql
-- Extract values
SELECT 
    name,
    JSON_VALUE(attributes, '$.brand') as brand,
    JSON_QUERY(attributes, '$.specs') as specs_object
FROM products;

-- Check if valid JSON
SELECT name, attributes
FROM products
WHERE ISJSON(attributes) = 1;

-- Parse JSON into rows
SELECT p.name, j.*
FROM products p
CROSS APPLY OPENJSON(attributes) 
WITH (
    brand NVARCHAR(50) '$.brand',
    model NVARCHAR(50) '$.model',
    storage NVARCHAR(20) '$.storage'
) as j;

-- Extract array elements
SELECT p.name, tag.value as tag
FROM products p
CROSS APPLY OPENJSON(JSON_QUERY(attributes, '$.tags')) as tag;
```

## Modifying JSON Data

### PostgreSQL JSON Modifications
```sql
-- Add/update field
UPDATE products 
SET metadata = metadata || '{"warranty": "2 years"}'
WHERE id = 1;

-- Remove field
UPDATE products 
SET metadata = metadata - 'warranty'
WHERE id = 1;

-- Update nested value
UPDATE products 
SET metadata = jsonb_set(metadata, '{price}', '899.99')
WHERE id = 1;

-- Update array element
UPDATE products 
SET metadata = jsonb_set(metadata, '{tags,0}', '"laptop"')
WHERE id = 1;

-- Insert into array
UPDATE products 
SET metadata = jsonb_insert(metadata, '{tags,1}', '"portable"')
WHERE id = 1;

-- Complex updates with jsonb_set
UPDATE products 
SET metadata = jsonb_set(
    jsonb_set(metadata, '{specs,cpu}', '"Intel i9"'),
    '{specs,ram}', '"32GB"'
)
WHERE id = 1;
```

### MySQL JSON Modifications
```sql
-- Add/update field
UPDATE products 
SET attributes = JSON_SET(attributes, '$.warranty', '2 years')
WHERE id = 1;

-- Remove field
UPDATE products 
SET attributes = JSON_REMOVE(attributes, '$.warranty')
WHERE id = 1;

-- Insert field (only if doesn't exist)
UPDATE products 
SET attributes = JSON_INSERT(attributes, '$.new_field', 'new_value')
WHERE id = 1;

-- Replace field (only if exists)
UPDATE products 
SET attributes = JSON_REPLACE(attributes, '$.brand', 'New Brand')
WHERE id = 1;

-- Array operations
UPDATE products 
SET attributes = JSON_ARRAY_APPEND(attributes, '$.tags', 'new_tag')
WHERE id = 1;

UPDATE products 
SET attributes = JSON_ARRAY_INSERT(attributes, '$.tags[1]', 'inserted_tag')
WHERE id = 1;
```

### SQL Server JSON Modifications
```sql
-- Update JSON (replace entire object)
UPDATE products 
SET attributes = JSON_MODIFY(attributes, '$.warranty', '2 years')
WHERE id = 1;

-- Remove field
UPDATE products 
SET attributes = JSON_MODIFY(attributes, '$.warranty', NULL)
WHERE id = 1;

-- Add to array
UPDATE products 
SET attributes = JSON_MODIFY(
    attributes, 
    'append $.tags', 
    'new_tag'
)
WHERE id = 1;

-- Complex modifications
UPDATE products 
SET attributes = JSON_MODIFY(
    JSON_MODIFY(attributes, '$.specs.cpu', 'Intel i9'),
    '$.specs.ram', '32GB'
)
WHERE id = 1;
```

## JSON Aggregation

### PostgreSQL JSON Aggregation
```sql
-- Aggregate rows into JSON array
SELECT 
    category,
    json_agg(
        json_build_object(
            'name', name,
            'price', (metadata->>'price')::numeric
        ) ORDER BY name
    ) as products
FROM products p
JOIN (
    SELECT id, metadata->>'category' as category 
    FROM products
) cat ON p.id = cat.id
GROUP BY category;

-- Aggregate into JSON object
SELECT jsonb_object_agg(name, metadata->'price') as price_lookup
FROM products;

-- Complex aggregation with filtering
SELECT 
    metadata->>'category' as category,
    jsonb_agg(
        jsonb_build_object(
            'name', name,
            'tags', metadata->'tags'
        )
    ) FILTER (WHERE (metadata->>'price')::numeric > 500) as expensive_products
FROM products
GROUP BY metadata->>'category';
```

### MySQL JSON Aggregation
```sql
-- Aggregate into JSON array
SELECT 
    JSON_EXTRACT(attributes, '$.category') as category,
    JSON_ARRAYAGG(
        JSON_OBJECT(
            'name', name,
            'brand', JSON_EXTRACT(attributes, '$.brand')
        )
    ) as products
FROM products
GROUP BY JSON_EXTRACT(attributes, '$.category');

-- Aggregate with conditions
SELECT JSON_ARRAYAGG(
    CASE 
        WHEN JSON_EXTRACT(attributes, '$.price') > 500 
        THEN JSON_OBJECT('name', name, 'price', JSON_EXTRACT(attributes, '$.price'))
        ELSE NULL
    END
) as expensive_products
FROM products;
```

## Indexing JSON Data

### PostgreSQL JSON Indexes
```sql
-- GIN index on entire JSONB column
CREATE INDEX idx_products_metadata_gin ON products USING GIN (metadata);

-- GIN index on specific JSON path
CREATE INDEX idx_products_category ON products USING GIN ((metadata->'category'));

-- B-tree index on extracted value
CREATE INDEX idx_products_price ON products ((metadata->>'price')::numeric);

-- Partial index with JSON condition
CREATE INDEX idx_electronics_price ON products ((metadata->>'price')::numeric)
WHERE metadata->>'category' = 'electronics';

-- Expression index for complex queries
CREATE INDEX idx_products_tags ON products USING GIN ((metadata->'tags'));
```

### MySQL JSON Indexes
```sql
-- Functional index on JSON path (MySQL 8.0+)
CREATE INDEX idx_products_brand ON products ((CAST(attributes->>'$.brand' AS CHAR(50))));

-- Multi-value index for JSON arrays (MySQL 8.0.17+)
CREATE INDEX idx_products_tags ON products ((CAST(attributes->'$.tags[*]' AS CHAR(50) ARRAY)));

-- Generated column with index
ALTER TABLE products 
ADD COLUMN brand VARCHAR(50) AS (attributes->>'$.brand') STORED;

CREATE INDEX idx_products_brand_generated ON products (brand);
```

### SQL Server JSON Indexes
```sql
-- Computed column with index
ALTER TABLE products 
ADD brand AS JSON_VALUE(attributes, '$.brand');

CREATE INDEX idx_products_brand ON products (brand);

-- Full-text index for JSON content
CREATE FULLTEXT INDEX idx_products_attributes_ft 
ON products (attributes);
```

## Database-Specific Features

### PostgreSQL Advanced Features
```sql
-- JSON Schema validation (with extension)
CREATE EXTENSION IF NOT EXISTS jsonschema;

-- Custom operators
SELECT * FROM products 
WHERE metadata @@ '$.price > 500';

-- JSON pretty printing
SELECT jsonb_pretty(metadata) FROM products;

-- Convert between JSON and other formats
SELECT 
    name,
    hstore(metadata) as metadata_hstore,
    metadata::text as metadata_text
FROM products;
```

### MySQL JSON Features
```sql
-- JSON schema validation (MySQL 8.0.17+)
ALTER TABLE products 
ADD CONSTRAINT chk_attributes_schema 
CHECK (JSON_SCHEMA_VALID('{
    "type": "object",
    "properties": {
        "brand": {"type": "string"},
        "price": {"type": "number", "minimum": 0}
    },
    "required": ["brand"]
}', attributes));

-- JSON merge operations
SELECT JSON_MERGE_PATCH(
    '{"brand": "Dell", "model": "XPS"}',
    '{"model": "Inspiron", "price": 999}'
) as merged_json;
```

### SQL Server JSON Features
```sql
-- Format query results as JSON
SELECT id, name, attributes
FROM products
FOR JSON PATH;

-- Include null values
SELECT id, name, attributes
FROM products
FOR JSON PATH, INCLUDE_NULL_VALUES;

-- Custom root element
SELECT id, name
FROM products
FOR JSON PATH, ROOT('products');

-- Validate JSON with schema (custom function)
CREATE FUNCTION dbo.ValidateProductJSON(@json NVARCHAR(MAX))
RETURNS BIT
AS
BEGIN
    DECLARE @isValid BIT = 0;
    
    IF ISJSON(@json) = 1 
    AND JSON_VALUE(@json, '$.brand') IS NOT NULL
    AND ISNUMERIC(JSON_VALUE(@json, '$.price')) = 1
        SET @isValid = 1;
    
    RETURN @isValid;
END;
```

## Performance Best Practices

### 1. Use Appropriate Indexes
```sql
-- Index frequently queried paths
CREATE INDEX idx_product_category ON products ((metadata->>'category'));

-- Use partial indexes for filtered queries
CREATE INDEX idx_expensive_products ON products ((metadata->>'price')::numeric)
WHERE (metadata->>'price')::numeric > 1000;
```

### 2. Avoid Deep Nesting
```sql
-- Good: Flat structure
{"name": "Product", "category": "electronics", "price": 999}

-- Avoid: Deep nesting
{"product": {"details": {"info": {"category": "electronics"}}}}
```

### 3. Use Appropriate Data Types
```sql
-- Store numbers as numbers, not strings
UPDATE products 
SET metadata = jsonb_set(metadata, '{price}', '999.99'::jsonb)
WHERE metadata->>'price' = '"999.99"';
```

### 4. Batch JSON Operations
```sql
-- Update multiple JSON fields in one operation
UPDATE products 
SET metadata = metadata || jsonb_build_object(
    'updated_at', CURRENT_TIMESTAMP,
    'version', COALESCE((metadata->>'version')::int, 0) + 1,
    'status', 'active'
)
WHERE id IN (1, 2, 3);
```

## 🚀 Next Steps

After mastering JSON operations:
- [Advanced SQL Techniques](./advanced-techniques.md) - Complex query patterns
- [Database Design](../design/README.md) - When to use JSON vs relational design
- [Performance Optimization](../optimization/README.md) - Optimizing JSON queries

## 📝 Practice Exercises

1. Design a product catalog using JSON for flexible attributes
2. Implement a configuration management system with JSON
3. Create a user preferences system with nested JSON
4. Build a dynamic form system using JSON schema
5. Implement a logging system with structured JSON data

JSON support in SQL opens up new possibilities for flexible data modeling - practice with real use cases to understand when and how to use JSON effectively!
