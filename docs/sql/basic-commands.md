# Basic SQL Commands

This guide covers the fundamental SQL commands that every developer and database professional should know. These commands form the foundation of all database operations.

## 📋 Table of Contents

1. [Data Query Language (DQL)](#data-query-language-dql)
2. [Data Manipulation Language (DML)](#data-manipulation-language-dml)
3. [Data Definition Language (DDL)](#data-definition-language-ddl)
4. [Data Control Language (DCL)](#data-control-language-dcl)
5. [Common Patterns](#common-patterns)
6. [Best Practices](#best-practices)

## Data Query Language (DQL)

### SELECT - Retrieving Data

The SELECT statement is used to query data from one or more tables.

#### Basic Syntax
```sql
SELECT column1, column2, ...
FROM table_name
WHERE condition
ORDER BY column1 ASC/DESC
LIMIT number;
```

#### Examples

**Select all columns:**
```sql
SELECT * FROM employees;
```

**Select specific columns:**
```sql
SELECT first_name, last_name, email 
FROM employees;
```

**Select with conditions:**
```sql
SELECT first_name, last_name, salary
FROM employees
WHERE salary > 50000
  AND department = 'Engineering';
```

**Select with ordering:**
```sql
SELECT first_name, last_name, hire_date
FROM employees
ORDER BY hire_date DESC
LIMIT 10;
```

**Select with aliases:**
```sql
SELECT 
    first_name AS "First Name",
    last_name AS "Last Name",
    salary * 12 AS "Annual Salary"
FROM employees;
```

#### WHERE Clause Operators
```sql
-- Comparison operators
WHERE salary = 50000          -- Equal
WHERE salary != 50000         -- Not equal
WHERE salary <> 50000         -- Not equal (alternative)
WHERE salary > 50000          -- Greater than
WHERE salary >= 50000         -- Greater than or equal
WHERE salary < 50000          -- Less than
WHERE salary <= 50000         -- Less than or equal

-- Logical operators
WHERE salary > 40000 AND department = 'IT'
WHERE salary > 60000 OR department = 'Sales'
WHERE NOT department = 'HR'

-- Pattern matching
WHERE first_name LIKE 'John%'     -- Starts with 'John'
WHERE first_name LIKE '%son'      -- Ends with 'son'
WHERE first_name LIKE '%oh%'      -- Contains 'oh'
WHERE first_name LIKE 'J_hn'      -- J, any character, hn

-- Range and list
WHERE salary BETWEEN 40000 AND 60000
WHERE department IN ('IT', 'Sales', 'Marketing')
WHERE manager_id IS NULL
WHERE manager_id IS NOT NULL
```

## Data Manipulation Language (DML)

### INSERT - Adding Data

#### Insert Single Row
```sql
INSERT INTO employees (first_name, last_name, email, hire_date, salary, department)
VALUES ('John', 'Doe', '<EMAIL>', '2024-01-15', 55000, 'Engineering');
```

#### Insert Multiple Rows
```sql
INSERT INTO employees (first_name, last_name, email, hire_date, salary, department)
VALUES 
    ('Jane', 'Smith', '<EMAIL>', '2024-01-16', 60000, 'Marketing'),
    ('Bob', 'Johnson', '<EMAIL>', '2024-01-17', 52000, 'Sales'),
    ('Alice', 'Brown', '<EMAIL>', '2024-01-18', 58000, 'Engineering');
```

#### Insert from Another Table
```sql
INSERT INTO employees_backup (first_name, last_name, email, salary)
SELECT first_name, last_name, email, salary
FROM employees
WHERE hire_date < '2023-01-01';
```

### UPDATE - Modifying Data

#### Update Single Column
```sql
UPDATE employees
SET salary = 65000
WHERE employee_id = 1;
```

#### Update Multiple Columns
```sql
UPDATE employees
SET 
    salary = salary * 1.1,
    last_modified = CURRENT_TIMESTAMP
WHERE department = 'Engineering';
```

#### Update with Conditions
```sql
UPDATE employees
SET department = 'Senior Engineering'
WHERE department = 'Engineering' 
  AND salary > 70000;
```

### DELETE - Removing Data

#### Delete Specific Rows
```sql
DELETE FROM employees
WHERE employee_id = 1;
```

#### Delete with Conditions
```sql
DELETE FROM employees
WHERE hire_date < '2020-01-01'
  AND status = 'inactive';
```

#### Delete All Rows (Use with Caution!)
```sql
DELETE FROM employees;  -- Removes all data but keeps table structure
```

## Data Definition Language (DDL)

### CREATE - Creating Database Objects

#### Create Database
```sql
CREATE DATABASE company_db;
```

#### Create Table
```sql
CREATE TABLE employees (
    employee_id SERIAL PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    hire_date DATE NOT NULL,
    salary DECIMAL(10,2) CHECK (salary > 0),
    department VARCHAR(50),
    manager_id INTEGER REFERENCES employees(employee_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Create Index
```sql
CREATE INDEX idx_employee_department ON employees(department);
CREATE INDEX idx_employee_name ON employees(last_name, first_name);
```

### ALTER - Modifying Database Objects

#### Add Column
```sql
ALTER TABLE employees
ADD COLUMN phone VARCHAR(20);
```

#### Modify Column
```sql
ALTER TABLE employees
ALTER COLUMN salary TYPE DECIMAL(12,2);
```

#### Drop Column
```sql
ALTER TABLE employees
DROP COLUMN phone;
```

#### Add Constraint
```sql
ALTER TABLE employees
ADD CONSTRAINT chk_salary_positive CHECK (salary > 0);
```

### DROP - Removing Database Objects

#### Drop Table
```sql
DROP TABLE employees;
```

#### Drop Index
```sql
DROP INDEX idx_employee_department;
```

#### Drop Database
```sql
DROP DATABASE company_db;
```

## Data Control Language (DCL)

### GRANT - Giving Permissions
```sql
GRANT SELECT, INSERT ON employees TO user_name;
GRANT ALL PRIVILEGES ON employees TO admin_user;
```

### REVOKE - Removing Permissions
```sql
REVOKE INSERT ON employees FROM user_name;
REVOKE ALL PRIVILEGES ON employees FROM user_name;
```

## Common Patterns

### CRUD Operations
```sql
-- Create
INSERT INTO products (name, price, category) VALUES ('Laptop', 999.99, 'Electronics');

-- Read
SELECT * FROM products WHERE category = 'Electronics';

-- Update
UPDATE products SET price = 899.99 WHERE name = 'Laptop';

-- Delete
DELETE FROM products WHERE name = 'Laptop';
```

### Working with Dates
```sql
-- Current date/time
SELECT CURRENT_DATE, CURRENT_TIME, CURRENT_TIMESTAMP;

-- Date arithmetic
SELECT * FROM orders WHERE order_date >= CURRENT_DATE - INTERVAL '30 days';

-- Date formatting
SELECT TO_CHAR(order_date, 'YYYY-MM-DD') FROM orders;
```

### Handling NULL Values
```sql
-- Check for NULL
SELECT * FROM employees WHERE manager_id IS NULL;

-- Replace NULL with default value
SELECT first_name, COALESCE(middle_name, '') AS middle_name FROM employees;

-- Conditional logic
SELECT 
    first_name,
    CASE 
        WHEN salary > 60000 THEN 'High'
        WHEN salary > 40000 THEN 'Medium'
        ELSE 'Low'
    END AS salary_grade
FROM employees;
```

## Best Practices

### 1. Use Proper Formatting
```sql
-- Good
SELECT 
    first_name,
    last_name,
    salary
FROM employees
WHERE department = 'Engineering'
ORDER BY salary DESC;

-- Avoid
select first_name,last_name,salary from employees where department='Engineering' order by salary desc;
```

### 2. Use Meaningful Names
```sql
-- Good
SELECT emp.first_name, dept.department_name
FROM employees emp
JOIN departments dept ON emp.department_id = dept.department_id;

-- Avoid
SELECT e.fn, d.dn FROM employees e JOIN departments d ON e.did = d.did;
```

### 3. Always Use WHERE Clauses for Updates/Deletes
```sql
-- Always specify conditions
UPDATE employees SET salary = 60000 WHERE employee_id = 1;
DELETE FROM employees WHERE status = 'inactive';

-- Never do this in production
UPDATE employees SET salary = 60000;  -- Updates ALL rows!
DELETE FROM employees;                -- Deletes ALL rows!
```

### 4. Use Transactions for Multiple Operations
```sql
BEGIN;
    UPDATE accounts SET balance = balance - 100 WHERE account_id = 1;
    UPDATE accounts SET balance = balance + 100 WHERE account_id = 2;
COMMIT;
```

## 🚀 Next Steps

Now that you understand basic SQL commands, explore:
- [Joins and Relationships](./joins.md) - Combining data from multiple tables
- [Aggregate Functions](./aggregates.md) - Summarizing and grouping data
- [Subqueries and CTEs](./cte.md) - Complex query structures

## 📝 Practice Exercises

Try these exercises to reinforce your learning:

1. Create a simple table structure for a library system
2. Insert sample data for books, authors, and borrowers
3. Write queries to find books by specific authors
4. Update book availability status
5. Delete overdue records

Remember: Practice makes perfect! Start with simple queries and gradually work your way up to more complex operations.
