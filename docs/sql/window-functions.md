# Window Functions

Window functions perform calculations across a set of table rows that are related to the current row. Unlike aggregate functions, window functions don't group rows into a single output row — they retain the individual row identity.

## 📋 Table of Contents

1. [Basic Syntax](#basic-syntax)
2. [Ranking Functions](#ranking-functions)
3. [Aggregate Window Functions](#aggregate-window-functions)
4. [Value Functions](#value-functions)
5. [Frame Specifications](#frame-specifications)
6. [Practical Examples](#practical-examples)
7. [Performance Considerations](#performance-considerations)

## Basic Syntax

```sql
SELECT 
    column1,
    column2,
    WINDOW_FUNCTION() OVER (
        [PARTITION BY column1, column2, ...]
        [ORDER BY column3, column4, ...]
        [ROWS/RANGE BETWEEN ... AND ...]
    ) AS window_result
FROM table_name;
```

### Key Components:
- **PARTITION BY**: Divides rows into groups (like GROUP BY but doesn't collapse rows)
- **ORDER BY**: Defines the order within each partition
- **Frame Specification**: Defines which rows to include in the calculation

## Ranking Functions

### ROW_NUMBER()
Assigns a unique sequential integer to each row within a partition.

```sql
SELECT 
    first_name,
    last_name,
    department,
    salary,
    ROW_NUMBER() OVER (
        PARTITION BY department 
        ORDER BY salary DESC
    ) as row_num
FROM employees;
```

### RANK()
Assigns ranks with gaps for tied values.

```sql
SELECT 
    first_name,
    last_name,
    department,
    salary,
    RANK() OVER (
        PARTITION BY department 
        ORDER BY salary DESC
    ) as rank
FROM employees;

-- Result example:
-- John    | Engineering | 80000 | 1
-- Jane    | Engineering | 75000 | 2
-- Bob     | Engineering | 75000 | 2
-- Alice   | Engineering | 70000 | 4  (note the gap)
```

### DENSE_RANK()
Assigns ranks without gaps for tied values.

```sql
SELECT 
    first_name,
    last_name,
    department,
    salary,
    DENSE_RANK() OVER (
        PARTITION BY department 
        ORDER BY salary DESC
    ) as dense_rank
FROM employees;

-- Result example:
-- John    | Engineering | 80000 | 1
-- Jane    | Engineering | 75000 | 2
-- Bob     | Engineering | 75000 | 2
-- Alice   | Engineering | 70000 | 3  (no gap)
```

### NTILE()
Distributes rows into a specified number of groups.

```sql
SELECT 
    first_name,
    last_name,
    salary,
    NTILE(4) OVER (ORDER BY salary DESC) as quartile
FROM employees;
```

## Aggregate Window Functions

### Running Totals
```sql
SELECT 
    order_date,
    daily_sales,
    SUM(daily_sales) OVER (
        ORDER BY order_date
        ROWS UNBOUNDED PRECEDING
    ) as running_total
FROM daily_sales
ORDER BY order_date;
```

### Moving Averages
```sql
SELECT 
    order_date,
    daily_sales,
    AVG(daily_sales) OVER (
        ORDER BY order_date
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as moving_avg_7_days,
    AVG(daily_sales) OVER (
        ORDER BY order_date
        ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING
    ) as centered_avg_5_days
FROM daily_sales
ORDER BY order_date;
```

### Percentage of Total
```sql
SELECT 
    department,
    salary,
    salary / SUM(salary) OVER () * 100 as pct_of_total_payroll,
    salary / SUM(salary) OVER (PARTITION BY department) * 100 as pct_of_dept_payroll
FROM employees;
```

## Value Functions

### LAG() and LEAD()
Access data from previous or next rows.

```sql
SELECT 
    order_date,
    total_amount,
    LAG(total_amount, 1) OVER (ORDER BY order_date) as previous_order,
    LEAD(total_amount, 1) OVER (ORDER BY order_date) as next_order,
    total_amount - LAG(total_amount, 1) OVER (ORDER BY order_date) as change_from_previous
FROM orders
ORDER BY order_date;
```

### FIRST_VALUE() and LAST_VALUE()
Get the first or last value in a window.

```sql
SELECT 
    employee_id,
    first_name,
    hire_date,
    salary,
    FIRST_VALUE(salary) OVER (
        PARTITION BY department 
        ORDER BY hire_date
        ROWS UNBOUNDED PRECEDING
    ) as first_hire_salary,
    LAST_VALUE(salary) OVER (
        PARTITION BY department 
        ORDER BY hire_date
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as latest_hire_salary
FROM employees;
```

### NTH_VALUE()
Get the nth value in a window.

```sql
SELECT 
    product_name,
    price,
    category,
    NTH_VALUE(price, 2) OVER (
        PARTITION BY category 
        ORDER BY price DESC
        ROWS UNBOUNDED PRECEDING
    ) as second_highest_price
FROM products;
```

## Frame Specifications

### ROWS vs RANGE
```sql
-- ROWS: Physical number of rows
SELECT 
    order_date,
    amount,
    SUM(amount) OVER (
        ORDER BY order_date
        ROWS BETWEEN 2 PRECEDING AND 2 FOLLOWING
    ) as sum_5_rows
FROM orders;

-- RANGE: Logical range based on values
SELECT 
    order_date,
    amount,
    SUM(amount) OVER (
        ORDER BY order_date
        RANGE BETWEEN INTERVAL '2 days' PRECEDING 
                  AND INTERVAL '2 days' FOLLOWING
    ) as sum_5_day_range
FROM orders;
```

### Frame Boundaries
```sql
-- Different frame specifications
SELECT 
    order_date,
    amount,
    -- From start of partition to current row
    SUM(amount) OVER (
        ORDER BY order_date
        ROWS UNBOUNDED PRECEDING
    ) as running_total,
    
    -- From current row to end of partition
    SUM(amount) OVER (
        ORDER BY order_date
        ROWS BETWEEN CURRENT ROW AND UNBOUNDED FOLLOWING
    ) as remaining_total,
    
    -- Entire partition
    SUM(amount) OVER (
        ORDER BY order_date
        ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
    ) as total_all,
    
    -- 3 rows centered on current row
    AVG(amount) OVER (
        ORDER BY order_date
        ROWS BETWEEN 1 PRECEDING AND 1 FOLLOWING
    ) as centered_avg_3
FROM orders;
```

## Practical Examples

### Top N per Group
```sql
-- Top 3 highest paid employees per department
SELECT *
FROM (
    SELECT 
        first_name,
        last_name,
        department,
        salary,
        ROW_NUMBER() OVER (
            PARTITION BY department 
            ORDER BY salary DESC
        ) as rn
    FROM employees
) ranked
WHERE rn <= 3;
```

### Running Difference
```sql
SELECT 
    month,
    revenue,
    revenue - LAG(revenue) OVER (ORDER BY month) as month_over_month_change,
    (revenue - LAG(revenue) OVER (ORDER BY month)) / 
    LAG(revenue) OVER (ORDER BY month) * 100 as pct_change
FROM monthly_revenue
ORDER BY month;
```

### Cumulative Distribution
```sql
SELECT 
    employee_id,
    salary,
    CUME_DIST() OVER (ORDER BY salary) as cumulative_distribution,
    PERCENT_RANK() OVER (ORDER BY salary) as percent_rank
FROM employees;
```

### Gap and Island Problem
```sql
-- Find consecutive sequences
WITH numbered_data AS (
    SELECT 
        id,
        value,
        ROW_NUMBER() OVER (ORDER BY id) as rn,
        id - ROW_NUMBER() OVER (ORDER BY id) as grp
    FROM sequence_table
    WHERE condition = 'active'
)
SELECT 
    MIN(id) as start_id,
    MAX(id) as end_id,
    COUNT(*) as sequence_length
FROM numbered_data
GROUP BY grp
ORDER BY start_id;
```

### Sales Performance Analysis
```sql
SELECT 
    salesperson,
    month,
    sales_amount,
    -- Rank within month
    RANK() OVER (PARTITION BY month ORDER BY sales_amount DESC) as monthly_rank,
    -- Running total for the year
    SUM(sales_amount) OVER (
        PARTITION BY salesperson 
        ORDER BY month
        ROWS UNBOUNDED PRECEDING
    ) as ytd_sales,
    -- 3-month moving average
    AVG(sales_amount) OVER (
        PARTITION BY salesperson 
        ORDER BY month
        ROWS BETWEEN 2 PRECEDING AND CURRENT ROW
    ) as moving_avg_3m,
    -- Percentage of total monthly sales
    sales_amount / SUM(sales_amount) OVER (PARTITION BY month) * 100 as pct_of_monthly_total
FROM sales_data
ORDER BY salesperson, month;
```

## Performance Considerations

### 1. Indexing for Window Functions
```sql
-- Create appropriate indexes for PARTITION BY and ORDER BY columns
CREATE INDEX idx_employees_dept_salary ON employees (department, salary DESC);
CREATE INDEX idx_orders_date ON orders (order_date);
```

### 2. Limit Window Function Usage
```sql
-- Instead of multiple window functions with same OVER clause
SELECT 
    employee_id,
    salary,
    AVG(salary) OVER (PARTITION BY department) as dept_avg,
    MAX(salary) OVER (PARTITION BY department) as dept_max,
    MIN(salary) OVER (PARTITION BY department) as dept_min
FROM employees;

-- Consider using a single window function with subquery
WITH dept_stats AS (
    SELECT 
        department,
        AVG(salary) as dept_avg,
        MAX(salary) as dept_max,
        MIN(salary) as dept_min
    FROM employees
    GROUP BY department
)
SELECT 
    e.employee_id,
    e.salary,
    ds.dept_avg,
    ds.dept_max,
    ds.dept_min
FROM employees e
JOIN dept_stats ds ON e.department = ds.department;
```

### 3. Use Appropriate Frame Specifications
```sql
-- Be specific about frame boundaries
-- This is more efficient:
SUM(amount) OVER (ORDER BY date ROWS UNBOUNDED PRECEDING)

-- Than this (if you don't need the full range):
SUM(amount) OVER (ORDER BY date RANGE UNBOUNDED PRECEDING)
```

## Common Patterns and Use Cases

### 1. Deduplication
```sql
-- Remove duplicates keeping the latest record
DELETE FROM table_name
WHERE id NOT IN (
    SELECT id
    FROM (
        SELECT id,
               ROW_NUMBER() OVER (
                   PARTITION BY unique_key 
                   ORDER BY created_at DESC
               ) as rn
        FROM table_name
    ) t
    WHERE rn = 1
);
```

### 2. Time Series Analysis
```sql
SELECT 
    date,
    value,
    -- Previous value
    LAG(value) OVER (ORDER BY date) as prev_value,
    -- Change from previous
    value - LAG(value) OVER (ORDER BY date) as change,
    -- 7-day moving average
    AVG(value) OVER (
        ORDER BY date 
        ROWS BETWEEN 6 PRECEDING AND CURRENT ROW
    ) as moving_avg_7d,
    -- Percentile rank
    PERCENT_RANK() OVER (ORDER BY value) as percentile_rank
FROM time_series_data
ORDER BY date;
```

### 3. Cohort Analysis
```sql
WITH user_cohorts AS (
    SELECT 
        user_id,
        DATE_TRUNC('month', first_purchase_date) as cohort_month,
        DATE_TRUNC('month', purchase_date) as purchase_month
    FROM user_purchases
),
cohort_data AS (
    SELECT 
        cohort_month,
        purchase_month,
        COUNT(DISTINCT user_id) as users,
        EXTRACT(MONTH FROM AGE(purchase_month, cohort_month)) as period_number
    FROM user_cohorts
    GROUP BY cohort_month, purchase_month
)
SELECT 
    cohort_month,
    period_number,
    users,
    FIRST_VALUE(users) OVER (
        PARTITION BY cohort_month 
        ORDER BY period_number
    ) as cohort_size,
    users::FLOAT / FIRST_VALUE(users) OVER (
        PARTITION BY cohort_month 
        ORDER BY period_number
    ) * 100 as retention_rate
FROM cohort_data
ORDER BY cohort_month, period_number;
```

## 🚀 Next Steps

After mastering window functions, explore:
- [Common Table Expressions (CTEs)](./cte.md) - Structuring complex queries
- [Recursive Queries](./recursive-queries.md) - Working with hierarchical data
- [Query Optimization](../optimization/README.md) - Performance tuning

## 📝 Practice Exercises

1. Calculate running totals and moving averages for sales data
2. Find the top N records per category using ranking functions
3. Implement gap and island analysis for time series data
4. Create a cohort analysis for user retention
5. Build a sales performance dashboard with various window functions

Window functions are powerful tools for analytical queries - practice with real datasets to master their usage!
