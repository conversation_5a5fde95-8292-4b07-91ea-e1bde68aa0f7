# Entity Relationship Diagrams (ERD)

Entity Relationship Diagrams are visual representations of database structures that show entities, attributes, and relationships. This guide covers ERD creation, notation, and best practices for effective database design communication.

## 📋 Table of Contents

1. [ERD Fundamentals](#erd-fundamentals)
2. [ERD Components](#erd-components)
3. [Notation Systems](#notation-systems)
4. [Creating Effective ERDs](#creating-effective-erds)
5. [ERD Tools and Software](#erd-tools-and-software)
6. [Common Patterns](#common-patterns)
7. [Best Practices](#best-practices)

## ERD Fundamentals

### What is an ERD?
An Entity Relationship Diagram is a visual representation that shows:
- **Entities**: Things or objects in the business domain
- **Attributes**: Properties or characteristics of entities
- **Relationships**: Connections between entities
- **Cardinality**: The nature of relationships (one-to-one, one-to-many, etc.)

### Types of ERDs

#### Conceptual ERD
- High-level business view
- Shows major entities and relationships
- No technical details
- Used for stakeholder communication

#### Logical ERD
- Detailed structure
- All entities, attributes, and relationships
- Data types and constraints
- Platform-independent

#### Physical ERD
- Implementation-specific
- Database tables and columns
- Indexes and constraints
- Performance considerations

## ERD Components

### Entities
Entities represent real-world objects or concepts:

```
┌─────────────┐
│   Customer  │
├─────────────┤
│ customer_id │
│ first_name  │
│ last_name   │
│ email       │
│ phone       │
└─────────────┘
```

#### Entity Types
- **Strong Entity**: Can exist independently
- **Weak Entity**: Depends on another entity for existence
- **Associative Entity**: Represents a many-to-many relationship

### Attributes
Properties that describe entities:

#### Attribute Types
- **Simple**: Cannot be divided (first_name)
- **Composite**: Can be divided (full_name = first_name + last_name)
- **Single-valued**: One value per entity (customer_id)
- **Multi-valued**: Multiple values per entity (phone_numbers)
- **Derived**: Calculated from other attributes (age from birth_date)

#### Key Attributes
- **Primary Key**: Uniquely identifies entity instances
- **Foreign Key**: References primary key of another entity
- **Candidate Key**: Potential primary keys
- **Composite Key**: Multiple attributes forming a key

### Relationships
Connections between entities:

```
Customer ──────── Places ──────── Order
   │                                │
   │                                │
   └── Has ──── Address            Product
```

#### Relationship Types
- **One-to-One (1:1)**: Each instance relates to exactly one instance
- **One-to-Many (1:M)**: One instance relates to many instances
- **Many-to-Many (M:N)**: Many instances relate to many instances

## Notation Systems

### Chen Notation
Traditional ERD notation:

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Customer  │────│   Places    │────│    Order    │
└─────────────┘    └─────────────┘    └─────────────┘
       1                               M

Entities: Rectangles
Relationships: Diamonds
Attributes: Ovals
Cardinality: Numbers or letters
```

### Crow's Foot Notation
Modern, widely-used notation:

```
Customer ||──────o{ Order
         ||      o{
         ||      
         ||──────o{ Address

|| = One (mandatory)
|o = One (optional)
}o = Many (optional)
}| = Many (mandatory)
```

### UML Class Diagram Notation
Object-oriented approach:

```
┌─────────────────┐
│    Customer     │
├─────────────────┤
│ - customer_id   │
│ - first_name    │
│ - last_name     │
│ - email         │
├─────────────────┤
│ + getOrders()   │
│ + updateEmail() │
└─────────────────┘
```

## Creating Effective ERDs

### Step-by-Step Process

#### 1. Identify Entities
```sql
-- Business Domain: E-commerce
-- Entities identified:
-- - Customer (person who buys)
-- - Product (item for sale)
-- - Order (purchase transaction)
-- - Category (product grouping)
-- - Address (location information)
-- - Payment (payment information)
```

#### 2. Define Attributes
```sql
-- Customer Entity
Customer:
- customer_id (PK)
- first_name
- last_name
- email (unique)
- phone
- date_of_birth
- registration_date
- status

-- Product Entity
Product:
- product_id (PK)
- product_name
- description
- price
- stock_quantity
- category_id (FK)
- sku (unique)
- weight
- dimensions
```

#### 3. Establish Relationships
```sql
-- Relationships identified:
-- Customer → Order (1:M) - One customer can have many orders
-- Order → Product (M:N) - Many orders can contain many products
-- Product → Category (M:1) - Many products belong to one category
-- Customer → Address (1:M) - One customer can have many addresses
-- Order → Payment (1:1) - One order has one payment
```

#### 4. Determine Cardinality
```
Customer ||──────o{ Order
         ||
         ||──────o{ Address

Order }o──────o{ Product
      }o      o{
      
Product }|──────|| Category

Order ||──────|| Payment
```

### Sample ERD: E-commerce System

```
                    ┌─────────────┐
                    │  Category   │
                    ├─────────────┤
                    │ category_id │ PK
                    │ name        │
                    │ description │
                    └─────────────┘
                           ||
                           ||
                           o{
                    ┌─────────────┐
                    │   Product   │
                    ├─────────────┤
                    │ product_id  │ PK
                    │ name        │
                    │ description │
                    │ price       │
                    │ stock_qty   │
                    │ category_id │ FK
                    └─────────────┘
                           o{
                           ||
                           ||
┌─────────────┐           ||           ┌─────────────┐
│  Customer   │           ||           │ Order_Item  │
├─────────────┤           ||           ├─────────────┤
│ customer_id │ PK        ||           │ order_id    │ PK,FK
│ first_name  │           ||           │ product_id  │ PK,FK
│ last_name   │           ||           │ quantity    │
│ email       │           ||           │ unit_price  │
│ phone       │           ||           └─────────────┘
└─────────────┘           ||                  ||
       ||                 ||                  ||
       ||                 ||                  ||
       o{                 ||                  ||
┌─────────────┐           ||                  ||
│    Order    │           ||                  ||
├─────────────┤           ||                  ||
│ order_id    │ PK        ||                  ||
│ customer_id │ FK        ||                  ||
│ order_date  │           ||                  ||
│ status      │           ||                  ||
│ total       │           ||                  ||
└─────────────┘           ||                  ||
       ||                 ||                  ||
       ||─────────────────||──────────────────||
       ||
       ||
       ||
┌─────────────┐
│   Address   │
├─────────────┤
│ address_id  │ PK
│ customer_id │ FK
│ type        │
│ street      │
│ city        │
│ state       │
│ zip_code    │
└─────────────┘
```

## ERD Tools and Software

### Free Tools
- **Draw.io (diagrams.net)**: Web-based, comprehensive
- **Lucidchart**: Professional diagramming
- **MySQL Workbench**: Database-specific
- **pgAdmin**: PostgreSQL ERD tool
- **DBeaver**: Universal database tool

### Commercial Tools
- **ERwin**: Enterprise data modeling
- **PowerDesigner**: SAP data modeling tool
- **Visio**: Microsoft diagramming
- **OmniGraffle**: Mac-based diagramming

### Code-Based Tools
- **Mermaid**: Text-based diagrams
- **PlantUML**: UML diagrams from text
- **dbdiagram.io**: Database diagrams from code

### Mermaid ERD Example
```mermaid
erDiagram
    Customer ||--o{ Order : places
    Customer ||--o{ Address : has
    Order ||--o{ OrderItem : contains
    Product ||--o{ OrderItem : "ordered in"
    Category ||--o{ Product : contains
    
    Customer {
        int customer_id PK
        string first_name
        string last_name
        string email UK
        string phone
        date registration_date
    }
    
    Order {
        int order_id PK
        int customer_id FK
        date order_date
        string status
        decimal total_amount
    }
    
    Product {
        int product_id PK
        string product_name
        text description
        decimal price
        int stock_quantity
        int category_id FK
    }
```

## Common Patterns

### Master-Detail Pattern
```
┌─────────────┐
│   Invoice   │ ||
├─────────────┤ ||
│ invoice_id  │ PK
│ customer_id │ FK
│ date        │
│ total       │
└─────────────┘
       ||
       ||
       o{
┌─────────────┐
│InvoiceItem  │
├─────────────┤
│ invoice_id  │ PK,FK
│ line_number │ PK
│ product_id  │ FK
│ quantity    │
│ unit_price  │
└─────────────┘
```

### Lookup Table Pattern
```
┌─────────────┐
│   Status    │ ||
├─────────────┤ ||
│ status_id   │ PK
│ status_name │
│ description │
└─────────────┘
       ||
       ||
       o{
┌─────────────┐
│    Order    │
├─────────────┤
│ order_id    │ PK
│ status_id   │ FK
│ order_date  │
└─────────────┘
```

### Hierarchy Pattern
```
┌─────────────┐
│  Category   │ ||
├─────────────┤ ||
│ category_id │ PK  ||
│ name        │     ||
│ parent_id   │ FK──||
└─────────────┘
```

### Audit Trail Pattern
```
┌─────────────┐
│  Customer   │ ||
├─────────────┤ ||
│ customer_id │ PK
│ name        │
│ email       │
└─────────────┘
       ||
       ||
       o{
┌─────────────┐
│CustomerAudit│
├─────────────┤
│ audit_id    │ PK
│ customer_id │ FK
│ action      │
│ old_values  │
│ new_values  │
│ timestamp   │
│ user_id     │
└─────────────┘
```

## Best Practices

### Design Guidelines

#### 1. Use Clear Naming Conventions
```sql
-- Good: Descriptive names
Customer, Order, Product, OrderItem

-- Avoid: Abbreviated names
Cust, Ord, Prod, OI
```

#### 2. Show Cardinality Clearly
```
-- Clear cardinality notation
Customer ||──────o{ Order
         1        0..*

-- Avoid ambiguous notation
Customer ──────── Order
```

#### 3. Group Related Entities
```
-- Logical grouping in ERD layout
[Customer Group]     [Product Group]     [Order Group]
- Customer           - Product           - Order
- Address            - Category          - OrderItem
- CustomerType       - Brand             - Payment
```

#### 4. Use Consistent Formatting
```
-- Consistent entity formatting
┌─────────────┐
│ Entity_Name │
├─────────────┤
│ primary_key │ PK
│ foreign_key │ FK
│ attribute   │
└─────────────┘
```

### Documentation Standards

#### Entity Documentation
```sql
-- Entity: Customer
-- Purpose: Stores customer information for order processing
-- Business Rules:
--   - Email must be unique
--   - Customer must have at least one address
--   - Inactive customers cannot place orders
-- Relationships:
--   - One customer can have many orders (1:M)
--   - One customer can have many addresses (1:M)
```

#### Attribute Documentation
```sql
-- Attribute: customer_status
-- Type: VARCHAR(20)
-- Values: 'active', 'inactive', 'suspended'
-- Business Rule: Only active customers can place orders
-- Default: 'active'
```

### ERD Review Checklist

#### Completeness
- [ ] All entities identified
- [ ] All attributes defined
- [ ] All relationships established
- [ ] Cardinality specified
- [ ] Keys identified

#### Accuracy
- [ ] Entity names match business terminology
- [ ] Relationships reflect business rules
- [ ] Cardinality is correct
- [ ] Attributes are in correct entities
- [ ] Keys are properly identified

#### Clarity
- [ ] Diagram is easy to read
- [ ] Layout is logical
- [ ] Notation is consistent
- [ ] Labels are clear
- [ ] Grouping makes sense

#### Validation
- [ ] Stakeholders have reviewed
- [ ] Business rules are captured
- [ ] Technical team has validated
- [ ] Performance implications considered
- [ ] Future requirements addressed

## Advanced ERD Techniques

### Subtype/Supertype Modeling
```
┌─────────────┐
│   Person    │
├─────────────┤
│ person_id   │ PK
│ first_name  │
│ last_name   │
│ person_type │
└─────────────┘
       ||
       ||
    ┌──||──┐
    │     │
    ▼     ▼
┌─────────────┐  ┌─────────────┐
│  Customer   │  │  Employee   │
├─────────────┤  ├─────────────┤
│ person_id   │PK│ person_id   │PK
│ credit_limit│  │ employee_id │
│ join_date   │  │ hire_date   │
└─────────────┘  │ salary      │
                 └─────────────┘
```

### Temporal Modeling
```
┌─────────────┐
│   Product   │ ||
├─────────────┤ ||
│ product_id  │ PK
│ name        │
│ description │
└─────────────┘
       ||
       ||
       o{
┌─────────────┐
│ProductPrice │
├─────────────┤
│ price_id    │ PK
│ product_id  │ FK
│ price       │
│ effective_from│
│ effective_to│
└─────────────┘
```

## 🚀 Next Steps

After mastering ERDs:
- [Normalization Techniques](./normalization.md) - Organizing your data structure
- [Schema Design Patterns](./schema-patterns.md) - Common design solutions
- [Data Modeling Best Practices](./data-modeling.md) - Effective modeling techniques

## 📝 Practice Exercises

1. Create an ERD for a library management system
2. Design an ERD for a hospital patient management system
3. Model a social media platform with users, posts, and relationships
4. Create an ERD for a project management application
5. Design a comprehensive ERD for a multi-tenant SaaS application

Remember: ERDs are communication tools - make them clear, accurate, and useful for both technical and non-technical stakeholders!
