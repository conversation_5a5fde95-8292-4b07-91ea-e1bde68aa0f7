# Database Design

Database design is the process of creating a detailed data model that defines how data is stored, organized, and accessed. Good database design is crucial for performance, maintainability, and scalability.

## 📚 Contents

### Core Design Concepts
- [Normalization Techniques](./normalization.md) - Organizing data to reduce redundancy
- [Denormalization Strategies](./denormalization.md) - Strategic redundancy for performance
- [Schema Design Patterns](./schema-patterns.md) - Common design patterns and solutions
- [Data Modeling Best Practices](./data-modeling.md) - Effective data modeling techniques
- [Entity Relationship Diagrams](./erd.md) - Visual database design representation
- [Constraint Design](./constraints.md) - Ensuring data integrity and business rules

## 🎯 Learning Path

### Beginner (Start Here)
1. [Data Modeling Best Practices](./data-modeling.md) - Understand the fundamentals
2. [Entity Relationship Diagrams](./erd.md) - Learn to visualize database structure
3. [Normalization Techniques](./normalization.md) - Master data organization principles

### Intermediate
4. [Constraint Design](./constraints.md) - Implement data integrity rules
5. [Schema Design Patterns](./schema-patterns.md) - Learn common solutions
6. [Denormalization Strategies](./denormalization.md) - Balance performance and normalization

## 💡 Key Design Principles

### 1. Data Integrity
- **Accuracy**: Data must be correct and valid
- **Consistency**: Data must be consistent across the database
- **Completeness**: Required data must be present
- **Uniqueness**: Unique constraints where appropriate

### 2. Performance Considerations
- **Query patterns**: Design for common access patterns
- **Indexing strategy**: Plan indexes during design phase
- **Scalability**: Consider future growth requirements
- **Read vs write optimization**: Balance based on workload

### 3. Maintainability
- **Clear naming conventions**: Use descriptive, consistent names
- **Documentation**: Document design decisions and business rules
- **Flexibility**: Design for future changes
- **Simplicity**: Keep design as simple as possible

### 4. Business Alignment
- **Requirements mapping**: Ensure design meets business needs
- **Future requirements**: Consider anticipated changes
- **Compliance**: Meet regulatory and audit requirements
- **Security**: Implement appropriate access controls

## 🔧 Design Process

### 1. Requirements Analysis
```
Business Requirements
        ↓
Functional Requirements
        ↓
Data Requirements
        ↓
Performance Requirements
        ↓
Security Requirements
```

### 2. Conceptual Design
- Identify entities and relationships
- Define business rules
- Create high-level ERD
- Validate with stakeholders

### 3. Logical Design
- Normalize to appropriate level
- Define attributes and data types
- Specify constraints and rules
- Create detailed ERD

### 4. Physical Design
- Choose storage structures
- Design indexes
- Partition strategies
- Performance optimization

## 📊 Common Design Patterns

### Master-Detail Pattern
```sql
-- Master table
CREATE TABLE orders (
    order_id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL,
    order_date DATE NOT NULL,
    total_amount DECIMAL(10,2),
    status VARCHAR(20) DEFAULT 'pending'
);

-- Detail table
CREATE TABLE order_items (
    order_item_id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL REFERENCES orders(order_id),
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL
);
```

### Lookup Table Pattern
```sql
-- Lookup table for categories
CREATE TABLE categories (
    category_id SERIAL PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    active BOOLEAN DEFAULT true
);

-- Main table referencing lookup
CREATE TABLE products (
    product_id SERIAL PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    category_id INTEGER NOT NULL REFERENCES categories(category_id),
    price DECIMAL(10,2) NOT NULL
);
```

### Audit Trail Pattern
```sql
-- Main table
CREATE TABLE customers (
    customer_id SERIAL PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Audit table
CREATE TABLE customers_audit (
    audit_id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL,
    action VARCHAR(10) NOT NULL, -- INSERT, UPDATE, DELETE
    old_values JSONB,
    new_values JSONB,
    changed_by VARCHAR(100),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Hierarchical Data Pattern
```sql
-- Adjacency List Model
CREATE TABLE categories (
    category_id SERIAL PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    parent_category_id INTEGER REFERENCES categories(category_id),
    level INTEGER,
    sort_order INTEGER
);

-- Nested Set Model (for read-heavy workloads)
CREATE TABLE categories_nested (
    category_id SERIAL PRIMARY KEY,
    category_name VARCHAR(100) NOT NULL,
    left_bound INTEGER NOT NULL,
    right_bound INTEGER NOT NULL,
    level INTEGER NOT NULL
);
```

## 🛠️ Design Tools and Techniques

### Entity Relationship Modeling
- **Entities**: Things or objects (Customer, Order, Product)
- **Attributes**: Properties of entities (name, price, date)
- **Relationships**: Connections between entities (Customer places Order)
- **Cardinality**: One-to-one, one-to-many, many-to-many

### Normalization Levels
- **1NF**: Eliminate repeating groups
- **2NF**: Eliminate partial dependencies
- **3NF**: Eliminate transitive dependencies
- **BCNF**: Eliminate remaining anomalies
- **4NF**: Eliminate multi-valued dependencies

### Data Types Selection
```sql
-- Choose appropriate data types
CREATE TABLE products (
    product_id INTEGER,           -- or BIGINT for large datasets
    product_name VARCHAR(100),    -- Fixed length for known limits
    description TEXT,             -- Variable length for long text
    price DECIMAL(10,2),         -- Exact precision for money
    weight FLOAT,                -- Approximate for measurements
    created_at TIMESTAMP,        -- Date and time
    is_active BOOLEAN,           -- True/false values
    metadata JSONB               -- Flexible structured data
);
```

## 📈 Design for Scale

### Horizontal Scaling Considerations
```sql
-- Design for sharding
CREATE TABLE orders (
    order_id BIGINT PRIMARY KEY,
    customer_id INTEGER NOT NULL,  -- Potential shard key
    order_date DATE NOT NULL,
    -- Include shard key in all queries
    INDEX idx_orders_customer_date (customer_id, order_date)
);

-- Avoid cross-shard joins
-- Denormalize when necessary for performance
```

### Partitioning Strategy
```sql
-- Range partitioning by date
CREATE TABLE sales_data (
    sale_id BIGINT,
    sale_date DATE NOT NULL,
    customer_id INTEGER,
    amount DECIMAL(10,2)
) PARTITION BY RANGE (sale_date);

-- Create partitions
CREATE TABLE sales_2024_q1 PARTITION OF sales_data
    FOR VALUES FROM ('2024-01-01') TO ('2024-04-01');

CREATE TABLE sales_2024_q2 PARTITION OF sales_data
    FOR VALUES FROM ('2024-04-01') TO ('2024-07-01');
```

### Read Replica Considerations
```sql
-- Design for read/write separation
-- Ensure eventual consistency is acceptable
-- Consider read-only reporting tables

CREATE TABLE customer_summary (
    customer_id INTEGER PRIMARY KEY,
    total_orders INTEGER,
    total_spent DECIMAL(12,2),
    last_order_date DATE,
    updated_at TIMESTAMP
);

-- Updated via triggers or batch processes
```

## 🔍 Design Validation

### Performance Testing
```sql
-- Test with realistic data volumes
-- Measure query performance
EXPLAIN ANALYZE
SELECT c.customer_name, COUNT(o.order_id) as order_count
FROM customers c
LEFT JOIN orders o ON c.customer_id = o.customer_id
WHERE c.created_at >= '2024-01-01'
GROUP BY c.customer_id, c.customer_name;

-- Test concurrent access patterns
-- Validate index effectiveness
```

### Data Integrity Testing
```sql
-- Test constraint enforcement
-- Verify referential integrity
-- Test business rule validation

-- Example: Test order total calculation
INSERT INTO orders (customer_id, order_date) VALUES (1, '2024-01-01');
INSERT INTO order_items (order_id, product_id, quantity, unit_price) 
VALUES (1, 1, 2, 10.00), (1, 2, 1, 15.00);

-- Verify total calculation
SELECT 
    o.order_id,
    SUM(oi.quantity * oi.unit_price) as calculated_total,
    o.total_amount as stored_total
FROM orders o
JOIN order_items oi ON o.order_id = oi.order_id
GROUP BY o.order_id, o.total_amount;
```

## 🎯 Design Anti-Patterns to Avoid

### 1. God Tables
```sql
-- Avoid: Single table with too many columns
CREATE TABLE everything (
    id INTEGER,
    customer_name VARCHAR(100),
    customer_email VARCHAR(100),
    order_date DATE,
    product_name VARCHAR(100),
    product_price DECIMAL(10,2),
    -- ... 50+ more columns
);

-- Better: Properly normalized tables
-- customers, orders, products, order_items
```

### 2. Generic Tables
```sql
-- Avoid: Generic attribute-value tables
CREATE TABLE entity_attributes (
    entity_id INTEGER,
    entity_type VARCHAR(50),
    attribute_name VARCHAR(50),
    attribute_value TEXT
);

-- Better: Specific tables with proper types
CREATE TABLE customers (
    customer_id INTEGER PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE
);
```

### 3. Premature Optimization
```sql
-- Avoid: Over-denormalizing without evidence
-- Start with normalized design
-- Denormalize based on actual performance needs
-- Measure before and after optimization
```

## 📋 Design Checklist

### Requirements
- [ ] Business requirements clearly defined
- [ ] Data requirements documented
- [ ] Performance requirements specified
- [ ] Security requirements identified
- [ ] Compliance requirements understood

### Design Quality
- [ ] Appropriate normalization level
- [ ] Consistent naming conventions
- [ ] Proper data types selected
- [ ] Constraints defined
- [ ] Indexes planned

### Documentation
- [ ] ERD created and reviewed
- [ ] Business rules documented
- [ ] Design decisions explained
- [ ] Migration plan prepared
- [ ] Testing strategy defined

### Validation
- [ ] Design reviewed by stakeholders
- [ ] Performance tested with realistic data
- [ ] Security requirements validated
- [ ] Backup and recovery tested
- [ ] Monitoring strategy implemented

## 🚀 Getting Started

### 1. Understand Requirements
- Interview stakeholders
- Document business processes
- Identify data sources
- Define success criteria

### 2. Create Conceptual Model
- Identify main entities
- Define relationships
- Validate with business users
- Iterate based on feedback

### 3. Develop Logical Model
- Apply normalization rules
- Define attributes and types
- Specify constraints
- Create detailed ERD

### 4. Implement Physical Model
- Choose storage options
- Design indexes
- Plan partitioning
- Optimize for performance

## 🔗 Related Topics

After mastering database design basics, explore:
- [Schema Management](../schema/README.md) - Managing schema changes
- [Performance Optimization](../optimization/README.md) - Optimizing your design
- [Security](../security/README.md) - Securing your database design
- [Monitoring](../monitoring/README.md) - Monitoring design effectiveness

## 📚 Further Reading

- Database design methodologies
- Industry-specific design patterns
- NoSQL design principles
- Microservices data architecture

Start with [Data Modeling Best Practices](./data-modeling.md) to understand the fundamentals, then explore [Entity Relationship Diagrams](./erd.md) to learn how to visualize your database structure!
