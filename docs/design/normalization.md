# Normalization Techniques

Normalization is the process of organizing data in a database to reduce redundancy and improve data integrity. This guide covers the principles, normal forms, and practical application of normalization techniques.

## 📋 Table of Contents

1. [Normalization Fundamentals](#normalization-fundamentals)
2. [Normal Forms](#normal-forms)
3. [Practical Examples](#practical-examples)
4. [Benefits and Trade-offs](#benefits-and-trade-offs)
5. [When to Normalize](#when-to-normalize)
6. [Common Mistakes](#common-mistakes)

## Normalization Fundamentals

### What is Normalization?
Normalization is a systematic approach to organizing data that:
- Eliminates redundant data
- Ensures data dependencies make sense
- Reduces storage space
- Improves data integrity
- Simplifies data maintenance

### Key Concepts

#### Functional Dependency
A functional dependency exists when one attribute determines another attribute.
```
employee_id → employee_name, department, salary
```
This means: Given an employee_id, you can determine exactly one employee_name, department, and salary.

#### Candidate Key
A minimal set of attributes that uniquely identifies each row in a table.

#### Primary Key
The chosen candidate key that uniquely identifies each row.

#### Partial Dependency
When a non-key attribute depends on only part of a composite primary key.

#### Transitive Dependency
When a non-key attribute depends on another non-key attribute.

## Normal Forms

### First Normal Form (1NF)

**Rule**: Each column contains atomic (indivisible) values, and each row is unique.

#### Violation Example
```sql
-- Violates 1NF: Multiple values in single column
CREATE TABLE employees_bad (
    employee_id INTEGER,
    name VARCHAR(100),
    skills VARCHAR(200)  -- "Java, Python, SQL" - multiple values
);

INSERT INTO employees_bad VALUES 
(1, 'John Doe', 'Java, Python, SQL'),
(2, 'Jane Smith', 'C++, JavaScript');
```

#### 1NF Solution
```sql
-- 1NF compliant: Atomic values
CREATE TABLE employees (
    employee_id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL
);

CREATE TABLE employee_skills (
    employee_id INTEGER,
    skill VARCHAR(50),
    PRIMARY KEY (employee_id, skill),
    FOREIGN KEY (employee_id) REFERENCES employees(employee_id)
);

INSERT INTO employees VALUES (1, 'John Doe'), (2, 'Jane Smith');
INSERT INTO employee_skills VALUES 
(1, 'Java'), (1, 'Python'), (1, 'SQL'),
(2, 'C++'), (2, 'JavaScript');
```

### Second Normal Form (2NF)

**Rule**: Must be in 1NF and eliminate partial dependencies (non-key attributes must depend on the entire primary key).

#### Violation Example
```sql
-- Violates 2NF: Partial dependency
CREATE TABLE order_items_bad (
    order_id INTEGER,
    product_id INTEGER,
    quantity INTEGER,
    product_name VARCHAR(100),    -- Depends only on product_id
    product_price DECIMAL(10,2),  -- Depends only on product_id
    PRIMARY KEY (order_id, product_id)
);
```

#### 2NF Solution
```sql
-- 2NF compliant: Separate tables
CREATE TABLE products (
    product_id INTEGER PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    product_price DECIMAL(10,2) NOT NULL
);

CREATE TABLE order_items (
    order_id INTEGER,
    product_id INTEGER,
    quantity INTEGER NOT NULL,
    PRIMARY KEY (order_id, product_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);
```

### Third Normal Form (3NF)

**Rule**: Must be in 2NF and eliminate transitive dependencies (non-key attributes must not depend on other non-key attributes).

#### Violation Example
```sql
-- Violates 3NF: Transitive dependency
CREATE TABLE employees_bad (
    employee_id INTEGER PRIMARY KEY,
    name VARCHAR(100),
    department_id INTEGER,
    department_name VARCHAR(100),  -- Depends on department_id (transitive)
    department_location VARCHAR(100)  -- Depends on department_id (transitive)
);
```

#### 3NF Solution
```sql
-- 3NF compliant: Separate department information
CREATE TABLE departments (
    department_id INTEGER PRIMARY KEY,
    department_name VARCHAR(100) NOT NULL,
    department_location VARCHAR(100)
);

CREATE TABLE employees (
    employee_id INTEGER PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    department_id INTEGER,
    FOREIGN KEY (department_id) REFERENCES departments(department_id)
);
```

### Boyce-Codd Normal Form (BCNF)

**Rule**: Must be in 3NF and every determinant must be a candidate key.

#### Violation Example
```sql
-- Violates BCNF: Non-candidate key determinant
CREATE TABLE course_instructor_bad (
    course_id VARCHAR(10),
    instructor_id INTEGER,
    instructor_name VARCHAR(100),
    room VARCHAR(20),
    PRIMARY KEY (course_id, instructor_id)
);

-- Problem: instructor_id → instructor_name (instructor_id is not a candidate key)
```

#### BCNF Solution
```sql
-- BCNF compliant: Separate instructor information
CREATE TABLE instructors (
    instructor_id INTEGER PRIMARY KEY,
    instructor_name VARCHAR(100) NOT NULL
);

CREATE TABLE course_assignments (
    course_id VARCHAR(10),
    instructor_id INTEGER,
    room VARCHAR(20),
    PRIMARY KEY (course_id, instructor_id),
    FOREIGN KEY (instructor_id) REFERENCES instructors(instructor_id)
);
```

### Fourth Normal Form (4NF)

**Rule**: Must be in BCNF and eliminate multi-valued dependencies.

#### Violation Example
```sql
-- Violates 4NF: Multi-valued dependencies
CREATE TABLE student_courses_hobbies_bad (
    student_id INTEGER,
    course VARCHAR(50),
    hobby VARCHAR(50),
    PRIMARY KEY (student_id, course, hobby)
);

-- Problem: student_id →→ course and student_id →→ hobby are independent
```

#### 4NF Solution
```sql
-- 4NF compliant: Separate multi-valued attributes
CREATE TABLE students (
    student_id INTEGER PRIMARY KEY,
    student_name VARCHAR(100) NOT NULL
);

CREATE TABLE student_courses (
    student_id INTEGER,
    course VARCHAR(50),
    PRIMARY KEY (student_id, course),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);

CREATE TABLE student_hobbies (
    student_id INTEGER,
    hobby VARCHAR(50),
    PRIMARY KEY (student_id, hobby),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);
```

## Practical Examples

### E-commerce Database Normalization

#### Unnormalized Structure
```sql
-- Unnormalized: All data in one table
CREATE TABLE orders_unnormalized (
    order_id INTEGER,
    order_date DATE,
    customer_name VARCHAR(100),
    customer_email VARCHAR(100),
    customer_address TEXT,
    product_name VARCHAR(100),
    product_category VARCHAR(50),
    product_price DECIMAL(10,2),
    quantity INTEGER,
    line_total DECIMAL(10,2)
);
```

#### Normalized Structure (3NF)
```sql
-- Customers table
CREATE TABLE customers (
    customer_id SERIAL PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    address TEXT
);

-- Product categories table
CREATE TABLE categories (
    category_id SERIAL PRIMARY KEY,
    category_name VARCHAR(50) NOT NULL UNIQUE
);

-- Products table
CREATE TABLE products (
    product_id SERIAL PRIMARY KEY,
    product_name VARCHAR(100) NOT NULL,
    category_id INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
);

-- Orders table
CREATE TABLE orders (
    order_id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL,
    order_date DATE NOT NULL DEFAULT CURRENT_DATE,
    FOREIGN KEY (customer_id) REFERENCES customers(customer_id)
);

-- Order items table
CREATE TABLE order_items (
    order_item_id SERIAL PRIMARY KEY,
    order_id INTEGER NOT NULL,
    product_id INTEGER NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);
```

### Library Management System

#### Normalized Design
```sql
-- Authors table
CREATE TABLE authors (
    author_id SERIAL PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    birth_date DATE
);

-- Publishers table
CREATE TABLE publishers (
    publisher_id SERIAL PRIMARY KEY,
    publisher_name VARCHAR(100) NOT NULL,
    address TEXT,
    contact_info VARCHAR(100)
);

-- Books table
CREATE TABLE books (
    book_id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    isbn VARCHAR(13) UNIQUE,
    publisher_id INTEGER,
    publication_date DATE,
    pages INTEGER,
    FOREIGN KEY (publisher_id) REFERENCES publishers(publisher_id)
);

-- Book authors (many-to-many relationship)
CREATE TABLE book_authors (
    book_id INTEGER,
    author_id INTEGER,
    author_order INTEGER DEFAULT 1,
    PRIMARY KEY (book_id, author_id),
    FOREIGN KEY (book_id) REFERENCES books(book_id),
    FOREIGN KEY (author_id) REFERENCES authors(author_id)
);

-- Members table
CREATE TABLE members (
    member_id SERIAL PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    address TEXT,
    membership_date DATE DEFAULT CURRENT_DATE
);

-- Book copies table
CREATE TABLE book_copies (
    copy_id SERIAL PRIMARY KEY,
    book_id INTEGER NOT NULL,
    copy_number INTEGER NOT NULL,
    condition VARCHAR(20) DEFAULT 'good',
    location VARCHAR(50),
    FOREIGN KEY (book_id) REFERENCES books(book_id),
    UNIQUE (book_id, copy_number)
);

-- Loans table
CREATE TABLE loans (
    loan_id SERIAL PRIMARY KEY,
    copy_id INTEGER NOT NULL,
    member_id INTEGER NOT NULL,
    loan_date DATE DEFAULT CURRENT_DATE,
    due_date DATE NOT NULL,
    return_date DATE,
    FOREIGN KEY (copy_id) REFERENCES book_copies(copy_id),
    FOREIGN KEY (member_id) REFERENCES members(member_id)
);
```

## Benefits and Trade-offs

### Benefits of Normalization

#### Data Integrity
```sql
-- Normalized: Update in one place
UPDATE categories SET category_name = 'Electronics & Gadgets' 
WHERE category_id = 1;
-- Automatically affects all products in this category

-- Unnormalized: Must update multiple rows
UPDATE products_unnormalized SET category_name = 'Electronics & Gadgets'
WHERE category_name = 'Electronics';
-- Risk of inconsistency if some rows are missed
```

#### Storage Efficiency
```sql
-- Normalized: Category name stored once
-- categories: 1 row with "Electronics"
-- products: 1000 rows referencing category_id = 1

-- Unnormalized: Category name repeated
-- products: 1000 rows each storing "Electronics"
-- Storage difference: significant for large datasets
```

#### Reduced Anomalies
```sql
-- Insert Anomaly Prevention
-- Normalized: Can add category without products
INSERT INTO categories (category_name) VALUES ('New Category');

-- Update Anomaly Prevention
-- Normalized: Single point of update
UPDATE customers SET email = '<EMAIL>' WHERE customer_id = 1;

-- Delete Anomaly Prevention
-- Normalized: Can delete order without losing customer data
DELETE FROM orders WHERE order_id = 123;
-- Customer data remains in customers table
```

### Trade-offs of Normalization

#### Query Complexity
```sql
-- Normalized: Requires joins
SELECT 
    c.customer_name,
    p.product_name,
    oi.quantity,
    oi.unit_price
FROM customers c
JOIN orders o ON c.customer_id = o.customer_id
JOIN order_items oi ON o.order_id = oi.order_id
JOIN products p ON oi.product_id = p.product_id;

-- Unnormalized: Simple select
SELECT customer_name, product_name, quantity, unit_price
FROM orders_unnormalized;
```

#### Performance Impact
```sql
-- Multiple joins can be slower
-- More tables to access
-- Potential for complex execution plans
-- May require careful indexing strategy
```

## When to Normalize

### Normalize When:
- **Data integrity is critical**
- **Storage space is a concern**
- **Data is frequently updated**
- **Consistency is more important than performance**
- **OLTP (transactional) workloads**

### Consider Denormalization When:
- **Read performance is critical**
- **Complex joins are too slow**
- **Reporting and analytics workloads**
- **Data is mostly read-only**
- **OLAP (analytical) workloads**

### Decision Framework
```sql
-- Analyze your workload
-- 1. Read vs Write ratio
-- 2. Query complexity tolerance
-- 3. Data consistency requirements
-- 4. Performance requirements
-- 5. Storage constraints

-- Example: Reporting table (denormalized)
CREATE TABLE sales_summary (
    summary_id SERIAL PRIMARY KEY,
    customer_name VARCHAR(100),
    product_name VARCHAR(100),
    category_name VARCHAR(50),
    order_date DATE,
    quantity INTEGER,
    unit_price DECIMAL(10,2),
    line_total DECIMAL(10,2),
    -- Denormalized for fast reporting
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Common Mistakes

### Over-Normalization
```sql
-- Mistake: Excessive normalization
CREATE TABLE address_types (
    type_id INTEGER PRIMARY KEY,
    type_name VARCHAR(20) -- 'home', 'work', 'billing'
);

CREATE TABLE addresses (
    address_id SERIAL PRIMARY KEY,
    customer_id INTEGER,
    type_id INTEGER,
    street VARCHAR(100),
    city VARCHAR(50),
    -- ... more fields
);

-- Better: Simple approach for small, stable lists
CREATE TABLE customers (
    customer_id SERIAL PRIMARY KEY,
    name VARCHAR(100),
    home_address TEXT,
    work_address TEXT,
    billing_address TEXT
);
```

### Ignoring Performance
```sql
-- Mistake: Not considering query patterns
-- If you frequently need customer + order data together,
-- consider strategic denormalization

-- Add computed columns for common calculations
ALTER TABLE orders 
ADD COLUMN total_amount DECIMAL(10,2);

-- Update via triggers or application logic
CREATE TRIGGER update_order_total
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION calculate_order_total();
```

### Inconsistent Normalization
```sql
-- Mistake: Mixing normalized and unnormalized approaches
CREATE TABLE orders (
    order_id INTEGER PRIMARY KEY,
    customer_id INTEGER,  -- Normalized reference
    customer_name VARCHAR(100),  -- Denormalized copy
    -- Inconsistent approach leads to maintenance issues
);

-- Better: Choose one approach consistently
-- Either fully normalize or strategically denormalize
```

## Normalization Checklist

### 1NF Checklist
- [ ] Each column contains atomic values
- [ ] No repeating groups
- [ ] Each row is unique
- [ ] Column order doesn't matter

### 2NF Checklist
- [ ] Table is in 1NF
- [ ] No partial dependencies
- [ ] All non-key attributes depend on entire primary key
- [ ] Consider composite keys carefully

### 3NF Checklist
- [ ] Table is in 2NF
- [ ] No transitive dependencies
- [ ] Non-key attributes don't depend on other non-key attributes
- [ ] Separate lookup tables created

### BCNF Checklist
- [ ] Table is in 3NF
- [ ] Every determinant is a candidate key
- [ ] No anomalies remain
- [ ] Consider if BCNF is necessary for your use case

## Best Practices

### 1. Start with Business Requirements
```sql
-- Understand the domain
-- Identify entities and relationships
-- Consider future requirements
-- Balance normalization with practicality
```

### 2. Use Consistent Naming
```sql
-- Good naming conventions
CREATE TABLE customers (
    customer_id SERIAL PRIMARY KEY,  -- Consistent ID naming
    customer_name VARCHAR(100),      -- Descriptive names
    created_at TIMESTAMP,           -- Consistent timestamp naming
    updated_at TIMESTAMP
);
```

### 3. Document Design Decisions
```sql
-- Document why you chose specific normal form
-- Explain any denormalization decisions
-- Note performance considerations
-- Include business rules and constraints
```

### 4. Test with Realistic Data
```sql
-- Test normalization with actual data volumes
-- Measure query performance
-- Validate data integrity constraints
-- Consider maintenance overhead
```

## 🚀 Next Steps

After mastering normalization:
- [Denormalization Strategies](./denormalization.md) - When and how to denormalize
- [Schema Design Patterns](./schema-patterns.md) - Common design solutions
- [Constraint Design](./constraints.md) - Implementing business rules

## 📝 Practice Exercises

1. Normalize a spreadsheet-like data structure to 3NF
2. Identify and fix normalization violations in existing schemas
3. Design a normalized schema for a specific business domain
4. Compare performance of normalized vs denormalized designs
5. Create a normalization strategy for a legacy system

Remember: Normalization is a tool, not a goal. The right level of normalization depends on your specific requirements, performance needs, and maintenance considerations!
